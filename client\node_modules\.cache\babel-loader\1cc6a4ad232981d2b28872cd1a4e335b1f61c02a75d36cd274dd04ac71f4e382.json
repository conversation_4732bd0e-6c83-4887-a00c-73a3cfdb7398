{"ast": null, "code": "'use client';\n\nexport { default } from './TableFooter';\nexport { default as tableFooterClasses } from './tableFooterClasses';\nexport * from './tableFooterClasses';", "map": {"version": 3, "names": ["default", "tableFooterClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/TableFooter/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './TableFooter';\nexport { default as tableFooterClasses } from './tableFooterClasses';\nexport * from './tableFooterClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,eAAe;AACvC,SAASA,OAAO,IAAIC,kBAAkB,QAAQ,sBAAsB;AACpE,cAAc,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}