{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp } from '@mui/utils';\nimport { DropdownContext } from '../useDropdown/DropdownContext';\nimport { useDropdown } from '../useDropdown/useDropdown';\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/)\n *\n * API:\n *\n * - [Dropdown API](https://mui.com/base-ui/react-menu/components-api/#dropdown)\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction Dropdown(props) {\n  const {\n    children,\n    open,\n    defaultOpen,\n    onOpenChange\n  } = props;\n  const {\n    contextValue\n  } = useDropdown({\n    defaultOpen,\n    onOpenChange,\n    open\n  });\n  return /*#__PURE__*/_jsx(DropdownContext.Provider, {\n    value: contextValue,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? Dropdown.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the dropdown is initially open.\n   */\n  defaultOpen: PropTypes.bool,\n  /**\n   * Callback fired when the component requests to be opened or closed.\n   */\n  onOpenChange: PropTypes.func,\n  /**\n   * Allows to control whether the dropdown is open.\n   * This is a controlled counterpart of `defaultOpen`.\n   */\n  open: PropTypes.bool\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  Dropdown['propTypes' + ''] = exactProp(Dropdown.propTypes);\n}\nexport { Dropdown };", "map": {"version": 3, "names": ["React", "PropTypes", "exactProp", "DropdownContext", "useDropdown", "jsx", "_jsx", "Dropdown", "props", "children", "open", "defaultOpen", "onOpenChange", "contextValue", "Provider", "value", "process", "env", "NODE_ENV", "propTypes", "node", "bool", "func"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/Dropdown/Dropdown.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { exactProp } from '@mui/utils';\nimport { DropdownContext } from '../useDropdown/DropdownContext';\nimport { useDropdown } from '../useDropdown/useDropdown';\n/**\n *\n * Demos:\n *\n * - [Menu](https://mui.com/base-ui/react-menu/)\n *\n * API:\n *\n * - [Dropdown API](https://mui.com/base-ui/react-menu/components-api/#dropdown)\n */\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nfunction Dropdown(props) {\n  const {\n    children,\n    open,\n    defaultOpen,\n    onOpenChange\n  } = props;\n  const {\n    contextValue\n  } = useDropdown({\n    defaultOpen,\n    onOpenChange,\n    open\n  });\n  return /*#__PURE__*/_jsx(DropdownContext.Provider, {\n    value: contextValue,\n    children: children\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? Dropdown.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * If `true`, the dropdown is initially open.\n   */\n  defaultOpen: PropTypes.bool,\n  /**\n   * Callback fired when the component requests to be opened or closed.\n   */\n  onOpenChange: PropTypes.func,\n  /**\n   * Allows to control whether the dropdown is open.\n   * This is a controlled counterpart of `defaultOpen`.\n   */\n  open: PropTypes.bool\n} : void 0;\nif (process.env.NODE_ENV !== 'production') {\n  // eslint-disable-next-line\n  Dropdown['propTypes' + ''] = exactProp(Dropdown.propTypes);\n}\nexport { Dropdown };"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,SAAS,QAAQ,YAAY;AACtC,SAASC,eAAe,QAAQ,gCAAgC;AAChE,SAASC,WAAW,QAAQ,4BAA4B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,QAAQA,CAACC,KAAK,EAAE;EACvB,MAAM;IACJC,QAAQ;IACRC,IAAI;IACJC,WAAW;IACXC;EACF,CAAC,GAAGJ,KAAK;EACT,MAAM;IACJK;EACF,CAAC,GAAGT,WAAW,CAAC;IACdO,WAAW;IACXC,YAAY;IACZF;EACF,CAAC,CAAC;EACF,OAAO,aAAaJ,IAAI,CAACH,eAAe,CAACW,QAAQ,EAAE;IACjDC,KAAK,EAAEF,YAAY;IACnBJ,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ;AACAO,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGX,QAAQ,CAACY,SAAS,CAAC,yBAAyB;EAClF;EACA;EACA;EACA;EACA;AACF;AACA;EACEV,QAAQ,EAAER,SAAS,CAACmB,IAAI;EACxB;AACF;AACA;EACET,WAAW,EAAEV,SAAS,CAACoB,IAAI;EAC3B;AACF;AACA;EACET,YAAY,EAAEX,SAAS,CAACqB,IAAI;EAC5B;AACF;AACA;AACA;EACEZ,IAAI,EAAET,SAAS,CAACoB;AAClB,CAAC,GAAG,KAAK,CAAC;AACV,IAAIL,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzC;EACAX,QAAQ,CAAC,WAAW,GAAG,EAAE,CAAC,GAAGL,SAAS,CAACK,QAAQ,CAACY,SAAS,CAAC;AAC5D;AACA,SAASZ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}