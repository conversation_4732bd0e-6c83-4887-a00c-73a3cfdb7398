{"ast": null, "code": "export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/uuid/dist/esm-browser/regex.js"], "sourcesContent": ["export default /^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;"], "mappings": "AAAA,eAAe,qHAAqH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}