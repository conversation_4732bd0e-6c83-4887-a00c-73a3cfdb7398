{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultContextValue = {\n  disableDefaultClasses: false\n};\nconst ClassNameConfiguratorContext = /*#__PURE__*/React.createContext(defaultContextValue);\nif (process.env.NODE_ENV !== 'production') {\n  ClassNameConfiguratorContext.displayName = 'ClassNameConfiguratorContext';\n}\n/**\n * @ignore - internal hook.\n *\n * Wraps the `generateUtilityClass` function and controls how the classes are generated.\n * Currently it only affects whether the classes are applied or not.\n *\n * @returns Function to be called with the `generateUtilityClass` function specific to a component to generate the classes.\n */\nexport function useClassNamesOverride(generateUtilityClass) {\n  const {\n    disableDefaultClasses\n  } = React.useContext(ClassNameConfiguratorContext);\n  return slot => {\n    if (disableDefaultClasses) {\n      return '';\n    }\n    return generateUtilityClass(slot);\n  };\n}\n\n/**\n * Allows to configure the components within to not apply any built-in classes.\n */\nexport function ClassNameConfigurator(props) {\n  const {\n    disableDefaultClasses,\n    children\n  } = props;\n  const contextValue = React.useMemo(() => ({\n    disableDefaultClasses: disableDefaultClasses != null ? disableDefaultClasses : false\n  }), [disableDefaultClasses]);\n  return /*#__PURE__*/_jsx(ClassNameConfiguratorContext.Provider, {\n    value: contextValue,\n    children: children\n  });\n}", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "defaultContextValue", "disableDefaultClasses", "ClassNameConfiguratorContext", "createContext", "process", "env", "NODE_ENV", "displayName", "useClassNamesOverride", "generateUtilityClass", "useContext", "slot", "ClassNameConfigurator", "props", "children", "contextValue", "useMemo", "Provider", "value"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/utils/ClassNameConfigurator.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst defaultContextValue = {\n  disableDefaultClasses: false\n};\nconst ClassNameConfiguratorContext = /*#__PURE__*/React.createContext(defaultContextValue);\nif (process.env.NODE_ENV !== 'production') {\n  ClassNameConfiguratorContext.displayName = 'ClassNameConfiguratorContext';\n}\n/**\n * @ignore - internal hook.\n *\n * Wraps the `generateUtilityClass` function and controls how the classes are generated.\n * Currently it only affects whether the classes are applied or not.\n *\n * @returns Function to be called with the `generateUtilityClass` function specific to a component to generate the classes.\n */\nexport function useClassNamesOverride(generateUtilityClass) {\n  const {\n    disableDefaultClasses\n  } = React.useContext(ClassNameConfiguratorContext);\n  return slot => {\n    if (disableDefaultClasses) {\n      return '';\n    }\n    return generateUtilityClass(slot);\n  };\n}\n\n/**\n * Allows to configure the components within to not apply any built-in classes.\n */\nexport function ClassNameConfigurator(props) {\n  const {\n    disableDefaultClasses,\n    children\n  } = props;\n  const contextValue = React.useMemo(() => ({\n    disableDefaultClasses: disableDefaultClasses != null ? disableDefaultClasses : false\n  }), [disableDefaultClasses]);\n  return /*#__PURE__*/_jsx(ClassNameConfiguratorContext.Provider, {\n    value: contextValue,\n    children: children\n  });\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,mBAAmB,GAAG;EAC1BC,qBAAqB,EAAE;AACzB,CAAC;AACD,MAAMC,4BAA4B,GAAG,aAAaL,KAAK,CAACM,aAAa,CAACH,mBAAmB,CAAC;AAC1F,IAAII,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCJ,4BAA4B,CAACK,WAAW,GAAG,8BAA8B;AAC3E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,oBAAoB,EAAE;EAC1D,MAAM;IACJR;EACF,CAAC,GAAGJ,KAAK,CAACa,UAAU,CAACR,4BAA4B,CAAC;EAClD,OAAOS,IAAI,IAAI;IACb,IAAIV,qBAAqB,EAAE;MACzB,OAAO,EAAE;IACX;IACA,OAAOQ,oBAAoB,CAACE,IAAI,CAAC;EACnC,CAAC;AACH;;AAEA;AACA;AACA;AACA,OAAO,SAASC,qBAAqBA,CAACC,KAAK,EAAE;EAC3C,MAAM;IACJZ,qBAAqB;IACrBa;EACF,CAAC,GAAGD,KAAK;EACT,MAAME,YAAY,GAAGlB,KAAK,CAACmB,OAAO,CAAC,OAAO;IACxCf,qBAAqB,EAAEA,qBAAqB,IAAI,IAAI,GAAGA,qBAAqB,GAAG;EACjF,CAAC,CAAC,EAAE,CAACA,qBAAqB,CAAC,CAAC;EAC5B,OAAO,aAAaF,IAAI,CAACG,4BAA4B,CAACe,QAAQ,EAAE;IAC9DC,KAAK,EAAEH,YAAY;IACnBD,QAAQ,EAAEA;EACZ,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}