{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAccordionSummaryUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordionSummary', slot);\n}\nconst accordionSummaryClasses = generateUtilityClasses('MuiAccordionSummary', ['root', 'expanded', 'focusVisible', 'disabled', 'gutters', 'contentGutters', 'content', 'expandIconWrapper']);\nexport default accordionSummaryClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getAccordionSummaryUtilityClass", "slot", "accordionSummaryClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/AccordionSummary/accordionSummaryClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getAccordionSummaryUtilityClass(slot) {\n  return generateUtilityClass('MuiAccordionSummary', slot);\n}\nconst accordionSummaryClasses = generateUtilityClasses('MuiAccordionSummary', ['root', 'expanded', 'focusVisible', 'disabled', 'gutters', 'contentGutters', 'content', 'expandIconWrapper']);\nexport default accordionSummaryClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,+BAA+BA,CAACC,IAAI,EAAE;EACpD,OAAOF,oBAAoB,CAAC,qBAAqB,EAAEE,IAAI,CAAC;AAC1D;AACA,MAAMC,uBAAuB,GAAGJ,sBAAsB,CAAC,qBAAqB,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,gBAAgB,EAAE,SAAS,EAAE,mBAAmB,CAAC,CAAC;AAC5L,eAAeI,uBAAuB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}