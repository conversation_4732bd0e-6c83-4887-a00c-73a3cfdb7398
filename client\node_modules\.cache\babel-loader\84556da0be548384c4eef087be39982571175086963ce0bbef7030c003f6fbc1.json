{"ast": null, "code": "export { default } from './composeClasses';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/composeClasses/index.js"], "sourcesContent": ["export { default } from './composeClasses';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}