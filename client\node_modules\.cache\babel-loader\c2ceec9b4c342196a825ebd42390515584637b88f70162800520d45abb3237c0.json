{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"cols\", \"component\", \"rowHeight\", \"gap\", \"style\", \"variant\"];\nimport composeClasses from '@mui/utils/composeClasses';\nimport integerPropType from '@mui/utils/integerPropType';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport { getImageListUtilityClass } from './imageListClasses';\nimport ImageListContext from './ImageListContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant]\n  };\n  return composeClasses(slots, getImageListUtilityClass, classes);\n};\nconst ImageListRoot = styled('ul', {\n  name: 'MuiImageList',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant]];\n  }\n})(_ref => {\n  let {\n    ownerState\n  } = _ref;\n  return _extends({\n    display: 'grid',\n    overflowY: 'auto',\n    listStyle: 'none',\n    padding: 0,\n    // Add iOS momentum scrolling for iOS < 13.0\n    WebkitOverflowScrolling: 'touch'\n  }, ownerState.variant === 'masonry' && {\n    display: 'block'\n  });\n});\nconst ImageList = /*#__PURE__*/React.forwardRef(function ImageList(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiImageList'\n  });\n  const {\n      children,\n      className,\n      cols = 2,\n      component = 'ul',\n      rowHeight = 'auto',\n      gap = 4,\n      style: styleProp,\n      variant = 'standard'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const contextValue = React.useMemo(() => ({\n    rowHeight,\n    gap,\n    variant\n  }), [rowHeight, gap, variant]);\n  React.useEffect(() => {\n    if (process.env.NODE_ENV !== 'production') {\n      // Detect Internet Explorer 8+\n      if (document !== undefined && 'objectFit' in document.documentElement.style === false) {\n        console.error(['MUI: ImageList v5+ no longer natively supports Internet Explorer.', 'Use v4 of this component instead, or polyfill CSS object-fit.'].join('\\n'));\n      }\n    }\n  }, []);\n  const style = variant === 'masonry' ? _extends({\n    columnCount: cols,\n    columnGap: gap\n  }, styleProp) : _extends({\n    gridTemplateColumns: \"repeat(\".concat(cols, \", 1fr)\"),\n    gap\n  }, styleProp);\n  const ownerState = _extends({}, props, {\n    component,\n    gap,\n    rowHeight,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ImageListRoot, _extends({\n    as: component,\n    className: clsx(classes.root, classes[variant], className),\n    ref: ref,\n    style: style,\n    ownerState: ownerState\n  }, other, {\n    children: /*#__PURE__*/_jsx(ImageListContext.Provider, {\n      value: contextValue,\n      children: children\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ImageList.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `ImageListItem`s.\n   */\n  children: PropTypes /* @typescript-to-proptypes-ignore */.node.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Number of columns.\n   * @default 2\n   */\n  cols: integerPropType,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The gap between items in px.\n   * @default 4\n   */\n  gap: PropTypes.number,\n  /**\n   * The height of one row in px.\n   * @default 'auto'\n   */\n  rowHeight: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number]),\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'standard'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['masonry', 'quilted', 'standard', 'woven']), PropTypes.string])\n} : void 0;\nexport default ImageList;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "composeClasses", "integerPropType", "clsx", "PropTypes", "React", "styled", "useThemeProps", "getImageListUtilityClass", "ImageListContext", "jsx", "_jsx", "useUtilityClasses", "ownerState", "classes", "variant", "slots", "root", "ImageListRoot", "name", "slot", "overridesResolver", "props", "styles", "_ref", "display", "overflowY", "listStyle", "padding", "WebkitOverflowScrolling", "ImageList", "forwardRef", "inProps", "ref", "children", "className", "cols", "component", "rowHeight", "gap", "style", "styleProp", "other", "contextValue", "useMemo", "useEffect", "process", "env", "NODE_ENV", "document", "undefined", "documentElement", "console", "error", "join", "columnCount", "columnGap", "gridTemplateColumns", "concat", "as", "Provider", "value", "propTypes", "node", "isRequired", "object", "string", "elementType", "number", "oneOfType", "oneOf", "sx", "arrayOf", "func", "bool"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/ImageList/ImageList.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"children\", \"className\", \"cols\", \"component\", \"rowHeight\", \"gap\", \"style\", \"variant\"];\nimport composeClasses from '@mui/utils/composeClasses';\nimport integerPropType from '@mui/utils/integerPropType';\nimport clsx from 'clsx';\nimport PropTypes from 'prop-types';\nimport * as React from 'react';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nimport { getImageListUtilityClass } from './imageListClasses';\nimport ImageListContext from './ImageListContext';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    classes,\n    variant\n  } = ownerState;\n  const slots = {\n    root: ['root', variant]\n  };\n  return composeClasses(slots, getImageListUtilityClass, classes);\n};\nconst ImageListRoot = styled('ul', {\n  name: 'MuiImageList',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant]];\n  }\n})(({\n  ownerState\n}) => {\n  return _extends({\n    display: 'grid',\n    overflowY: 'auto',\n    listStyle: 'none',\n    padding: 0,\n    // Add iOS momentum scrolling for iOS < 13.0\n    WebkitOverflowScrolling: 'touch'\n  }, ownerState.variant === 'masonry' && {\n    display: 'block'\n  });\n});\nconst ImageList = /*#__PURE__*/React.forwardRef(function ImageList(inProps, ref) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiImageList'\n  });\n  const {\n      children,\n      className,\n      cols = 2,\n      component = 'ul',\n      rowHeight = 'auto',\n      gap = 4,\n      style: styleProp,\n      variant = 'standard'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const contextValue = React.useMemo(() => ({\n    rowHeight,\n    gap,\n    variant\n  }), [rowHeight, gap, variant]);\n  React.useEffect(() => {\n    if (process.env.NODE_ENV !== 'production') {\n      // Detect Internet Explorer 8+\n      if (document !== undefined && 'objectFit' in document.documentElement.style === false) {\n        console.error(['MUI: ImageList v5+ no longer natively supports Internet Explorer.', 'Use v4 of this component instead, or polyfill CSS object-fit.'].join('\\n'));\n      }\n    }\n  }, []);\n  const style = variant === 'masonry' ? _extends({\n    columnCount: cols,\n    columnGap: gap\n  }, styleProp) : _extends({\n    gridTemplateColumns: `repeat(${cols}, 1fr)`,\n    gap\n  }, styleProp);\n  const ownerState = _extends({}, props, {\n    component,\n    gap,\n    rowHeight,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  return /*#__PURE__*/_jsx(ImageListRoot, _extends({\n    as: component,\n    className: clsx(classes.root, classes[variant], className),\n    ref: ref,\n    style: style,\n    ownerState: ownerState\n  }, other, {\n    children: /*#__PURE__*/_jsx(ImageListContext.Provider, {\n      value: contextValue,\n      children: children\n    })\n  }));\n});\nprocess.env.NODE_ENV !== \"production\" ? ImageList.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component, normally `ImageListItem`s.\n   */\n  children: PropTypes /* @typescript-to-proptypes-ignore */.node.isRequired,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * Number of columns.\n   * @default 2\n   */\n  cols: integerPropType,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * The gap between items in px.\n   * @default 4\n   */\n  gap: PropTypes.number,\n  /**\n   * The height of one row in px.\n   * @default 'auto'\n   */\n  rowHeight: PropTypes.oneOfType([PropTypes.oneOf(['auto']), PropTypes.number]),\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'standard'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['masonry', 'quilted', 'standard', 'woven']), PropTypes.string])\n} : void 0;\nexport default ImageList;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,WAAW,EAAE,KAAK,EAAE,OAAO,EAAE,SAAS,CAAC;AACxG,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,SAASC,wBAAwB,QAAQ,oBAAoB;AAC7D,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,OAAO;IACPC;EACF,CAAC,GAAGF,UAAU;EACd,MAAMG,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEF,OAAO;EACxB,CAAC;EACD,OAAOd,cAAc,CAACe,KAAK,EAAER,wBAAwB,EAAEM,OAAO,CAAC;AACjE,CAAC;AACD,MAAMI,aAAa,GAAGZ,MAAM,CAAC,IAAI,EAAE;EACjCa,IAAI,EAAE,cAAc;EACpBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJV;IACF,CAAC,GAAGS,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACV,UAAU,CAACE,OAAO,CAAC,CAAC;EAClD;AACF,CAAC,CAAC,CAACS,IAAA,IAEG;EAAA,IAFF;IACFX;EACF,CAAC,GAAAW,IAAA;EACC,OAAOzB,QAAQ,CAAC;IACd0B,OAAO,EAAE,MAAM;IACfC,SAAS,EAAE,MAAM;IACjBC,SAAS,EAAE,MAAM;IACjBC,OAAO,EAAE,CAAC;IACV;IACAC,uBAAuB,EAAE;EAC3B,CAAC,EAAEhB,UAAU,CAACE,OAAO,KAAK,SAAS,IAAI;IACrCU,OAAO,EAAE;EACX,CAAC,CAAC;AACJ,CAAC,CAAC;AACF,MAAMK,SAAS,GAAG,aAAazB,KAAK,CAAC0B,UAAU,CAAC,SAASD,SAASA,CAACE,OAAO,EAAEC,GAAG,EAAE;EAC/E,MAAMX,KAAK,GAAGf,aAAa,CAAC;IAC1Be,KAAK,EAAEU,OAAO;IACdb,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFe,QAAQ;MACRC,SAAS;MACTC,IAAI,GAAG,CAAC;MACRC,SAAS,GAAG,IAAI;MAChBC,SAAS,GAAG,MAAM;MAClBC,GAAG,GAAG,CAAC;MACPC,KAAK,EAAEC,SAAS;MAChB1B,OAAO,GAAG;IACZ,CAAC,GAAGO,KAAK;IACToB,KAAK,GAAG5C,6BAA6B,CAACwB,KAAK,EAAEtB,SAAS,CAAC;EACzD,MAAM2C,YAAY,GAAGtC,KAAK,CAACuC,OAAO,CAAC,OAAO;IACxCN,SAAS;IACTC,GAAG;IACHxB;EACF,CAAC,CAAC,EAAE,CAACuB,SAAS,EAAEC,GAAG,EAAExB,OAAO,CAAC,CAAC;EAC9BV,KAAK,CAACwC,SAAS,CAAC,MAAM;IACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC;MACA,IAAIC,QAAQ,KAAKC,SAAS,IAAI,WAAW,IAAID,QAAQ,CAACE,eAAe,CAACX,KAAK,KAAK,KAAK,EAAE;QACrFY,OAAO,CAACC,KAAK,CAAC,CAAC,mEAAmE,EAAE,+DAA+D,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;MAClK;IACF;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMd,KAAK,GAAGzB,OAAO,KAAK,SAAS,GAAGhB,QAAQ,CAAC;IAC7CwD,WAAW,EAAEnB,IAAI;IACjBoB,SAAS,EAAEjB;EACb,CAAC,EAAEE,SAAS,CAAC,GAAG1C,QAAQ,CAAC;IACvB0D,mBAAmB,YAAAC,MAAA,CAAYtB,IAAI,WAAQ;IAC3CG;EACF,CAAC,EAAEE,SAAS,CAAC;EACb,MAAM5B,UAAU,GAAGd,QAAQ,CAAC,CAAC,CAAC,EAAEuB,KAAK,EAAE;IACrCe,SAAS;IACTE,GAAG;IACHD,SAAS;IACTvB;EACF,CAAC,CAAC;EACF,MAAMD,OAAO,GAAGF,iBAAiB,CAACC,UAAU,CAAC;EAC7C,OAAO,aAAaF,IAAI,CAACO,aAAa,EAAEnB,QAAQ,CAAC;IAC/C4D,EAAE,EAAEtB,SAAS;IACbF,SAAS,EAAEhC,IAAI,CAACW,OAAO,CAACG,IAAI,EAAEH,OAAO,CAACC,OAAO,CAAC,EAAEoB,SAAS,CAAC;IAC1DF,GAAG,EAAEA,GAAG;IACRO,KAAK,EAAEA,KAAK;IACZ3B,UAAU,EAAEA;EACd,CAAC,EAAE6B,KAAK,EAAE;IACRR,QAAQ,EAAE,aAAavB,IAAI,CAACF,gBAAgB,CAACmD,QAAQ,EAAE;MACrDC,KAAK,EAAElB,YAAY;MACnBT,QAAQ,EAAEA;IACZ,CAAC;EACH,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACFY,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGlB,SAAS,CAACgC,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACE5B,QAAQ,EAAE9B,SAAS,CAAC,sCAAsC2D,IAAI,CAACC,UAAU;EACzE;AACF;AACA;EACElD,OAAO,EAAEV,SAAS,CAAC6D,MAAM;EACzB;AACF;AACA;EACE9B,SAAS,EAAE/B,SAAS,CAAC8D,MAAM;EAC3B;AACF;AACA;AACA;EACE9B,IAAI,EAAElC,eAAe;EACrB;AACF;AACA;AACA;EACEmC,SAAS,EAAEjC,SAAS,CAAC+D,WAAW;EAChC;AACF;AACA;AACA;EACE5B,GAAG,EAAEnC,SAAS,CAACgE,MAAM;EACrB;AACF;AACA;AACA;EACE9B,SAAS,EAAElC,SAAS,CAACiE,SAAS,CAAC,CAACjE,SAAS,CAACkE,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC,EAAElE,SAAS,CAACgE,MAAM,CAAC,CAAC;EAC7E;AACF;AACA;EACE5B,KAAK,EAAEpC,SAAS,CAAC6D,MAAM;EACvB;AACF;AACA;EACEM,EAAE,EAAEnE,SAAS,CAACiE,SAAS,CAAC,CAACjE,SAAS,CAACoE,OAAO,CAACpE,SAAS,CAACiE,SAAS,CAAC,CAACjE,SAAS,CAACqE,IAAI,EAAErE,SAAS,CAAC6D,MAAM,EAAE7D,SAAS,CAACsE,IAAI,CAAC,CAAC,CAAC,EAAEtE,SAAS,CAACqE,IAAI,EAAErE,SAAS,CAAC6D,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACElD,OAAO,EAAEX,SAAS,CAAC,sCAAsCiE,SAAS,CAAC,CAACjE,SAAS,CAACkE,KAAK,CAAC,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC,EAAElE,SAAS,CAAC8D,MAAM,CAAC;AACrJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAepC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}