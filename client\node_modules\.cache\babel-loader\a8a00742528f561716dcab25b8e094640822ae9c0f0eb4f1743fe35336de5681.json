{"ast": null, "code": "'use client';\n\nexport { TabsList } from './TabsList';\nexport * from './TabsList.types';\nexport * from './tabsListClasses';", "map": {"version": 3, "names": ["TabsList"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/TabsList/index.js"], "sourcesContent": ["'use client';\n\nexport { TabsList } from './TabsList';\nexport * from './TabsList.types';\nexport * from './tabsListClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,QAAQ,YAAY;AACrC,cAAc,kBAAkB;AAChC,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}