{"ast": null, "code": "export function createInvalidArgFactory(arg, name) {\n  return (dispatch, options) => {\n    throw new Error(\"Invalid value of type \".concat(typeof arg, \" for \").concat(name, \" argument when connecting component \").concat(options.wrappedComponentName, \".\"));\n  };\n}", "map": {"version": 3, "names": ["createInvalidArgFactory", "arg", "name", "dispatch", "options", "Error", "concat", "wrappedComponentName"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/react-redux/es/connect/invalidArgFactory.js"], "sourcesContent": ["export function createInvalidArgFactory(arg, name) {\n  return (dispatch, options) => {\n    throw new Error(`Invalid value of type ${typeof arg} for ${name} argument when connecting component ${options.wrappedComponentName}.`);\n  };\n}"], "mappings": "AAAA,OAAO,SAASA,uBAAuBA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACjD,OAAO,CAACC,QAAQ,EAAEC,OAAO,KAAK;IAC5B,MAAM,IAAIC,KAAK,0BAAAC,MAAA,CAA0B,OAAOL,GAAG,WAAAK,MAAA,CAAQJ,IAAI,0CAAAI,MAAA,CAAuCF,OAAO,CAACG,oBAAoB,MAAG,CAAC;EACxI,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}