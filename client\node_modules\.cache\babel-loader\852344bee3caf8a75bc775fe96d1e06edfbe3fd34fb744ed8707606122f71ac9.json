{"ast": null, "code": "export { FormControl } from './FormControl';\nexport { FormControlContext } from './FormControlContext';\nexport * from './FormControl.types';\nexport * from './formControlClasses';\nexport { useFormControlContext } from './useFormControlContext';", "map": {"version": 3, "names": ["FormControl", "FormControlContext", "useFormControlContext"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/FormControl/index.js"], "sourcesContent": ["export { FormControl } from './FormControl';\nexport { FormControlContext } from './FormControlContext';\nexport * from './FormControl.types';\nexport * from './formControlClasses';\nexport { useFormControlContext } from './useFormControlContext';"], "mappings": "AAAA,SAASA,WAAW,QAAQ,eAAe;AAC3C,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,cAAc,qBAAqB;AACnC,cAAc,sBAAsB;AACpC,SAASC,qBAAqB,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}