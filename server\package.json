{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "type": "module", "scripts": {"start": "nodemon index.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"axios": "^1.6.7", "bcryptjs": "^2.4.3", "body-parser": "^1.20.2", "cloudinary": "^2.0.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.3", "helmet": "^7.0.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.5.0", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.4", "nodemon": "^3.0.1", "uuid": "^9.0.0"}}