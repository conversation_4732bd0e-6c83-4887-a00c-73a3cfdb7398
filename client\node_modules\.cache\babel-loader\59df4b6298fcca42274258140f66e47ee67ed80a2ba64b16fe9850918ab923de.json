{"ast": null, "code": "'use client';\n\nexport { default } from './Pagination';\nexport { default as paginationClasses } from './paginationClasses';\nexport * from './paginationClasses';", "map": {"version": 3, "names": ["default", "paginationClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/Pagination/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Pagination';\nexport { default as paginationClasses } from './paginationClasses';\nexport * from './paginationClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASA,OAAO,IAAIC,iBAAiB,QAAQ,qBAAqB;AAClE,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}