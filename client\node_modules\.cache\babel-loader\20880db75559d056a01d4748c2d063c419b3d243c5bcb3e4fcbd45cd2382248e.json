{"ast": null, "code": "import{BACKEND_URL}from'../utils/api';import React,{useState,useEffect}from'react';import{Link}from'react-router-dom';import{BsPersonFillAdd}from'react-icons/bs';import{CiLocationOn}from'react-icons/ci';import moment from'moment';import{NoProfile}from'../assets';import axios from'axios';import{BsInstagram,BsFacebook}from'react-icons/bs';import{FaTwitterSquare}from'react-icons/fa';import Modal from'../components/FollowerModel';import{FaCheckCircle}from'react-icons/fa';import SkillProfilePills from'./SkillProfilePills';import EditProfile from'./EditProfile';import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const ProfileCard=_ref=>{var _user$profession,_user$location,_user$profession2,_user$followers;let{user,loggedInUser}=_ref;const[isFollowing,setIsFollowing]=useState(false);const[showFollowersModal,setShowFollowersModal]=useState(false);const[followers,setFollowers]=useState([]);const[showEditModal,setEditModal]=useState(false);useEffect(()=>{const checkFollowingStatus=async()=>{try{const response=await axios.get(\"\".concat(BACKEND_URL,\"/users/checkFollowing/\").concat(loggedInUser,\"/\").concat(user._id));setIsFollowing(response.data.isFollowing);}catch(error){console.error('Failed to check following status:',error);}};if(loggedInUser){checkFollowingStatus();}},[loggedInUser,user._id]);const handleFollow=async()=>{try{await axios.post(\"\".concat(BACKEND_URL,\"/users/follow\"),{followerId:loggedInUser,followeeId:user._id});setIsFollowing(true);// Update isFollowing state after successful follow\nconsole.log('User followed successfully');}catch(error){console.error('Failed to follow user:',error);}};const handleUnfollow=async()=>{try{await axios.post(\"\".concat(BACKEND_URL,\"/users/unfollow\"),{followerId:loggedInUser,followeeId:user._id});setIsFollowing(false);// Update isFollowing state after successful unfollow\nconsole.log('User unfollowed successfully');}catch(error){console.error('Failed to unfollow user:',error);}};const toggleFollowersModal=async()=>{try{const response=await axios.get(\"\".concat(BACKEND_URL,\"/users/followers/\").concat(user._id));setFollowers(response.data.followers);setShowFollowersModal(!showFollowersModal);}catch(error){console.error('Failed to fetch followers:',error);}};return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full bg-primary flex flex-col items-center shadow-sm rounded-xl px-6 py-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-wrap items-center justify-between border-b pb-5 border-[#********]\",children:[/*#__PURE__*/_jsxs(Link,{to:'/profile/'+(user===null||user===void 0?void 0:user._id),className:\"flex gap-2\",children:[/*#__PURE__*/_jsx(\"img\",{src:user&&user.profileUrl?user.profileUrl:NoProfile,alt:user&&user.email,className:\"w-14 h-14 object-cover rounded-full\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col justify-center\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-lg font-medium text-ascent-1\",style:{marginBottom:'-5px'},children:[user===null||user===void 0?void 0:user.firstName,\" \",user===null||user===void 0?void 0:user.lastName]}),(user===null||user===void 0?void 0:user.tick)&&/*#__PURE__*/_jsx(FaCheckCircle,{className:\"ml-1\",style:{color:'#0084ff'}})]}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-ascent-2\",children:[(_user$profession=user===null||user===void 0?void 0:user.profession)!==null&&_user$profession!==void 0?_user$profession:'No Profession',\" \\u2022 Joined\",' ',moment(user===null||user===void 0?void 0:user.createdAt).fromNow()]})]})]}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-auto md:ml-2 lg:ml-4 mt-4\",children:/*#__PURE__*/_jsxs(\"button\",{style:{backgroundColor:'black',color:'white',padding:'10px',borderRadius:'5px',display:'flex',alignItems:'center',justifyContent:'center'},onClick:()=>setEditModal(true),children:[/*#__PURE__*/_jsx(\"svg\",{xmlns:\"http://www.w3.org/2000/svg\",viewBox:\"-2 -2 24 24\",width:\"28\",fill:\"currentColor\",children:/*#__PURE__*/_jsx(\"path\",{d:\"M5.72 14.456l1.761-.508 10.603-10.73a.456.456 0 0 0-.003-.64l-.635-.642a.443.443 0 0 0-.632-.003L6.239 12.635l-.52 1.82zM18.703.664l.635.643c.876.887.884 2.318.016 3.196L8.428 15.561l-3.764 1.084a.901.901 0 0 1-1.11-.623.915.915 0 0 1-.002-.506l1.095-3.84L15.544.647a2.215 2.215 0 0 1 3.159.016zM7.184 1.817c.496 0 .898.407.898.909a.903.903 0 0 1-.898.909H3.592c-.992 0-1.796.814-1.796 1.817v10.906c0 1.004.804 1.818 1.796 1.818h10.776c.992 0 1.797-.814 1.797-1.818v-3.635c0-.502.402-.909.898-.909s.898.407.898.91v3.634c0 2.008-1.609 3.636-3.593 3.636H3.592C1.608 19.994 0 18.366 0 16.358V5.452c0-2.007 1.608-3.635 3.592-3.635h3.592z\"})}),/*#__PURE__*/_jsx(\"span\",{children:\"Edit Profile\"})]})}),/*#__PURE__*/_jsx(\"div\",{className:\"\",children:loggedInUser&&user&&loggedInUser!==user._id&&/*#__PURE__*/_jsxs(\"button\",{onClick:isFollowing?handleUnfollow:handleFollow,className:\"bg-[#000000] flex text-sm text-white p-2 rounded \".concat(isFollowing?'border-black':''),style:{backgroundColor:isFollowing?'#fc4e54':''},children:[isFollowing?'Unfollow':'Follow',/*#__PURE__*/_jsx(BsPersonFillAdd,{size:20,className:\"text-[#ffffff] ml-3\"})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col gap-2 py-4 border-b border-[#********]\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2 items-center text-ascent-2\",children:[/*#__PURE__*/_jsx(CiLocationOn,{className:\"text-xl text-ascent-1\"}),/*#__PURE__*/_jsx(\"span\",{children:(_user$location=user===null||user===void 0?void 0:user.location)!==null&&_user$location!==void 0?_user$location:'Add Class'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2 items-center text-ascent-2\",children:[/*#__PURE__*/_jsx(\"span\",{children:\"Profession:\"}),/*#__PURE__*/_jsx(\"span\",{children:(_user$profession2=user===null||user===void 0?void 0:user.profession)!==null&&_user$profession2!==void 0?_user$profession2:'Add Proffession'})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2 items-center text-ascent-2\",style:{cursor:'pointer'},onClick:toggleFollowersModal,children:[/*#__PURE__*/_jsx(\"span\",{children:\"Followers:\"}),/*#__PURE__*/_jsxs(\"span\",{children:[user===null||user===void 0?void 0:(_user$followers=user.followers)===null||_user$followers===void 0?void 0:_user$followers.length,\" Followers\"]})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col gap-4 py-4 pb-6\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-ascent-1 text-lg font-semibold\",children:\"Skills\"}),/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-2\",children:user.skills&&user.skills.length>0?/*#__PURE__*/_jsx(SkillProfilePills,{skills:user.skills}):/*#__PURE__*/_jsx(\"span\",{className:\"text-ascent-2\",children:\"No Skills Added\"})})]}),showFollowersModal&&/*#__PURE__*/_jsx(Modal,{onClose:toggleFollowersModal,followers:followers}),showEditModal&&/*#__PURE__*/_jsx(EditProfile,{closeModal:()=>{setEditModal(false);},userskills:user.skills,userlocation:user.location,userprof:user.profession,userprofileurl:user.profileUrl,userid:user._id}),/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col gap-4 py-4 pb-6\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-ascent-1 text-lg font-semibold\",children:\"Social Profile\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2 items-center text-ascent-2\",children:[/*#__PURE__*/_jsx(BsInstagram,{className:\" text-xl text-ascent-1\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Instagram\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2 items-center text-ascent-2\",children:[/*#__PURE__*/_jsx(FaTwitterSquare,{className:\" text-xl text-ascent-1\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Twitter\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-2 items-center text-ascent-2\",children:[/*#__PURE__*/_jsx(BsFacebook,{className:\" text-xl text-ascent-1\"}),/*#__PURE__*/_jsx(\"span\",{children:\"Facebook\"})]})]})]})});};export default ProfileCard;", "map": {"version": 3, "names": ["BACKEND_URL", "React", "useState", "useEffect", "Link", "BsPersonFillAdd", "CiLocationOn", "moment", "NoProfile", "axios", "BsInstagram", "BsFacebook", "FaTwitterSquare", "Modal", "FaCheckCircle", "SkillProfilePills", "EditProfile", "jsx", "_jsx", "jsxs", "_jsxs", "ProfileCard", "_ref", "_user$profession", "_user$location", "_user$profession2", "_user$followers", "user", "loggedInUser", "isFollowing", "setIsFollowing", "showFollowersModal", "setShowFollowersModal", "followers", "setFollowers", "showEditModal", "setEditModal", "checkFollowingStatus", "response", "get", "concat", "_id", "data", "error", "console", "handleFollow", "post", "followerId", "followeeId", "log", "handleUnfollow", "toggleFollowersModal", "children", "className", "to", "src", "profileUrl", "alt", "email", "style", "marginBottom", "firstName", "lastName", "tick", "color", "profession", "createdAt", "fromNow", "backgroundColor", "padding", "borderRadius", "display", "alignItems", "justifyContent", "onClick", "xmlns", "viewBox", "width", "fill", "d", "size", "location", "cursor", "length", "skills", "onClose", "closeModal", "userskills", "userlocation", "userprof", "userprofileurl", "userid"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/components/ProfileCard.jsx"], "sourcesContent": ["import { BACKEND_URL } from '../utils/api';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { Link } from 'react-router-dom';\r\nimport { BsPersonFillAdd } from 'react-icons/bs';\r\nimport { CiLocationOn } from 'react-icons/ci';\r\nimport moment from 'moment';\r\nimport { NoProfile } from '../assets';\r\nimport axios from 'axios';\r\nimport { BsInstagram, BsFacebook } from 'react-icons/bs';\r\nimport { FaTwitterSquare } from 'react-icons/fa';\r\nimport Modal from '../components/FollowerModel';\r\nimport { FaCheckCircle } from 'react-icons/fa';\r\nimport SkillProfilePills from './SkillProfilePills';\r\nimport EditProfile from './EditProfile';\r\n\r\nconst ProfileCard = ({ user, loggedInUser }) => {\r\n\tconst [isFollowing, setIsFollowing] = useState(false);\r\n\tconst [showFollowersModal, setShowFollowersModal] = useState(false);\r\n\tconst [followers, setFollowers] = useState([]);\r\n\tconst [showEditModal, setEditModal] = useState(false);\r\n\tuseEffect(() => {\r\n\t\tconst checkFollowingStatus = async () => {\r\n\t\t\ttry {\r\n\t\t\t\tconst response = await axios.get(\r\n\t\t\t\t\t`${BACKEND_URL}/users/checkFollowing/${loggedInUser}/${user._id}`\r\n\t\t\t\t);\r\n\t\t\t\tsetIsFollowing(response.data.isFollowing);\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('Failed to check following status:', error);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tif (loggedInUser) {\r\n\t\t\tcheckFollowingStatus();\r\n\t\t}\r\n\t}, [loggedInUser, user._id]);\r\n\r\n\tconst handleFollow = async () => {\r\n\t\ttry {\r\n\t\t\tawait axios.post(`${BACKEND_URL}/users/follow`, {\r\n\t\t\t\tfollowerId: loggedInUser,\r\n\t\t\t\tfolloweeId: user._id,\r\n\t\t\t});\r\n\r\n\t\t\tsetIsFollowing(true); // Update isFollowing state after successful follow\r\n\t\t\tconsole.log('User followed successfully');\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('Failed to follow user:', error);\r\n\t\t}\r\n\t};\r\n\r\n\tconst handleUnfollow = async () => {\r\n\t\ttry {\r\n\t\t\tawait axios.post(`${BACKEND_URL}/users/unfollow`, {\r\n\t\t\t\tfollowerId: loggedInUser,\r\n\t\t\t\tfolloweeId: user._id,\r\n\t\t\t});\r\n\r\n\t\t\tsetIsFollowing(false); // Update isFollowing state after successful unfollow\r\n\t\t\tconsole.log('User unfollowed successfully');\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('Failed to unfollow user:', error);\r\n\t\t}\r\n\t};\r\n\r\n\tconst toggleFollowersModal = async () => {\r\n\t\ttry {\r\n\t\t\tconst response = await axios.get(\r\n\t\t\t\t`${BACKEND_URL}/users/followers/${user._id}`\r\n\t\t\t);\r\n\t\t\tsetFollowers(response.data.followers);\r\n\t\t\tsetShowFollowersModal(!showFollowersModal);\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('Failed to fetch followers:', error);\r\n\t\t}\r\n\t};\r\n\r\n\treturn (\r\n\t\t<div>\r\n\t\t\t<div className='w-full bg-primary flex flex-col items-center shadow-sm rounded-xl px-6 py-4'>\r\n\t\t\t\t<div className='w-full flex flex-wrap items-center justify-between border-b pb-5 border-[#********]'>\r\n\t\t\t\t\t<Link to={'/profile/' + user?._id} className='flex gap-2'>\r\n\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\tsrc={user && user.profileUrl ? user.profileUrl : NoProfile}\r\n\t\t\t\t\t\t\talt={user && user.email}\r\n\t\t\t\t\t\t\tclassName='w-14 h-14 object-cover rounded-full'\r\n\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t<div className='flex flex-col justify-center'>\r\n\t\t\t\t\t\t\t<div className='flex items-center'>\r\n\t\t\t\t\t\t\t\t<p\r\n\t\t\t\t\t\t\t\t\tclassName='text-lg font-medium text-ascent-1'\r\n\t\t\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\t\t\tmarginBottom: '-5px',\r\n\t\t\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{user?.firstName} {user?.lastName}\r\n\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t\t{user?.tick && (\r\n\t\t\t\t\t\t\t\t\t<FaCheckCircle\r\n\t\t\t\t\t\t\t\t\t\tclassName='ml-1'\r\n\t\t\t\t\t\t\t\t\t\tstyle={{ color: '#0084ff' }}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<span className='text-ascent-2'>\r\n\t\t\t\t\t\t\t\t{user?.profession ?? 'No Profession'} • Joined{' '}\r\n\t\t\t\t\t\t\t\t{moment(user?.createdAt).fromNow()}\r\n\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</Link>\r\n\t\t\t\t\t<div className='ml-auto md:ml-2 lg:ml-4 mt-4'>\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\tstyle={{\r\n\t\t\t\t\t\t\t\tbackgroundColor: 'black',\r\n\t\t\t\t\t\t\t\tcolor: 'white',\r\n\t\t\t\t\t\t\t\tpadding: '10px',\r\n\t\t\t\t\t\t\t\tborderRadius: '5px',\r\n\t\t\t\t\t\t\t\tdisplay: 'flex',\r\n\t\t\t\t\t\t\t\talignItems: 'center',\r\n\t\t\t\t\t\t\t\tjustifyContent: 'center',\r\n\t\t\t\t\t\t\t}}\r\n\t\t\t\t\t\t\tonClick={() => setEditModal(true)}\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<svg\r\n\t\t\t\t\t\t\t\txmlns='http://www.w3.org/2000/svg'\r\n\t\t\t\t\t\t\t\tviewBox='-2 -2 24 24'\r\n\t\t\t\t\t\t\t\twidth='28'\r\n\t\t\t\t\t\t\t\tfill='currentColor'\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<path d='M5.72 14.456l1.761-.508 10.603-10.73a.456.456 0 0 0-.003-.64l-.635-.642a.443.443 0 0 0-.632-.003L6.239 12.635l-.52 1.82zM18.703.664l.635.643c.876.887.884 2.318.016 3.196L8.428 15.561l-3.764 1.084a.901.901 0 0 1-1.11-.623.915.915 0 0 1-.002-.506l1.095-3.84L15.544.647a2.215 2.215 0 0 1 3.159.016zM7.184 1.817c.496 0 .898.407.898.909a.903.903 0 0 1-.898.909H3.592c-.992 0-1.796.814-1.796 1.817v10.906c0 1.004.804 1.818 1.796 1.818h10.776c.992 0 1.797-.814 1.797-1.818v-3.635c0-.502.402-.909.898-.909s.898.407.898.91v3.634c0 2.008-1.609 3.636-3.593 3.636H3.592C1.608 19.994 0 18.366 0 16.358V5.452c0-2.007 1.608-3.635 3.592-3.635h3.592z'></path>\r\n\t\t\t\t\t\t\t</svg>\r\n\t\t\t\t\t\t\t<span>Edit Profile</span>\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className=''>\r\n\t\t\t\t\t\t{loggedInUser && user && loggedInUser !== user._id && (\r\n\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\tonClick={isFollowing ? handleUnfollow : handleFollow}\r\n\t\t\t\t\t\t\t\tclassName={`bg-[#000000] flex text-sm text-white p-2 rounded ${\r\n\t\t\t\t\t\t\t\t\tisFollowing ? 'border-black' : ''\r\n\t\t\t\t\t\t\t\t}`}\r\n\t\t\t\t\t\t\t\tstyle={{ backgroundColor: isFollowing ? '#fc4e54' : '' }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t{isFollowing ? 'Unfollow' : 'Follow'}\r\n\t\t\t\t\t\t\t\t<BsPersonFillAdd size={20} className='text-[#ffffff] ml-3' />\r\n\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t<div className='w-full flex flex-col gap-2 py-4 border-b border-[#********]'>\r\n\t\t\t\t\t<div className='flex gap-2 items-center text-ascent-2'>\r\n\t\t\t\t\t\t<CiLocationOn className='text-xl text-ascent-1' />\r\n\t\t\t\t\t\t<span>{user?.location ?? 'Add Class'}</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className='flex gap-2 items-center text-ascent-2'>\r\n\t\t\t\t\t\t<span>Profession:</span>\r\n\t\t\t\t\t\t<span>{user?.profession ?? 'Add Proffession'}</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div\r\n\t\t\t\t\t\tclassName='flex gap-2 items-center text-ascent-2'\r\n\t\t\t\t\t\tstyle={{ cursor: 'pointer' }}\r\n\t\t\t\t\t\tonClick={toggleFollowersModal}\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<span>Followers:</span>\r\n\t\t\t\t\t\t<span>{user?.followers?.length} Followers</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t\t{/* <div className=\"w-full flex flex-col gap-2 py-4 border-b border-[#********]\" onClick={toggleFollowersModal} >\r\n          <p className=\"text-xl text-ascent-1 font-semibold\">\r\n            {user?.followers?.length} Followers\r\n          </p>\r\n          <span className=\"text-base text-blue\">\r\n            {user?.verified ? \"Verified Account\" : \"Not Verified\"}\r\n          </span>\r\n        </div> */}\r\n\t\t\t\t<div className='w-full flex flex-col gap-4 py-4 pb-6'>\r\n\t\t\t\t\t<p className='text-ascent-1 text-lg font-semibold'>Skills</p>\r\n\t\t\t\t\t<div className='flex flex-wrap gap-2'>\r\n\t\t\t\t\t\t{user.skills && user.skills.length > 0 ? (\r\n\t\t\t\t\t\t\t<SkillProfilePills skills={user.skills} />\r\n\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t<span className='text-ascent-2'>No Skills Added</span>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t{/* Render modal if showFollowersModal is true */}\r\n\t\t\t\t{showFollowersModal && (\r\n\t\t\t\t\t<Modal onClose={toggleFollowersModal} followers={followers} />\r\n\t\t\t\t)}\r\n\t\t\t\t{showEditModal && (\r\n\t\t\t\t\t<EditProfile\r\n\t\t\t\t\t\tcloseModal={() => {\r\n\t\t\t\t\t\t\tsetEditModal(false);\r\n\t\t\t\t\t\t}}\r\n\t\t\t\t\t\tuserskills={user.skills}\r\n\t\t\t\t\t\tuserlocation={user.location}\r\n\t\t\t\t\t\tuserprof={user.profession}\r\n\t\t\t\t\t\tuserprofileurl={user.profileUrl}\r\n\t\t\t\t\t\tuserid={user._id}\r\n\t\t\t\t\t/>\r\n\t\t\t\t)}\r\n\t\t\t\t<div className='w-full flex flex-col gap-4 py-4 pb-6'>\r\n\t\t\t\t\t<p className='text-ascent-1 text-lg font-semibold'>Social Profile</p>\r\n\r\n\t\t\t\t\t<div className='flex gap-2 items-center text-ascent-2'>\r\n\t\t\t\t\t\t<BsInstagram className=' text-xl text-ascent-1' />\r\n\t\t\t\t\t\t<span>Instagram</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className='flex gap-2 items-center text-ascent-2'>\r\n\t\t\t\t\t\t<FaTwitterSquare className=' text-xl text-ascent-1' />\r\n\t\t\t\t\t\t<span>Twitter</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t\t<div className='flex gap-2 items-center text-ascent-2'>\r\n\t\t\t\t\t\t<BsFacebook className=' text-xl text-ascent-1' />\r\n\t\t\t\t\t\t<span>Facebook</span>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\t\t</div>\r\n\t);\r\n};\r\n\r\nexport default ProfileCard;\r\n"], "mappings": "AAAA,OAASA,WAAW,KAAQ,cAAc,CAC1C,MAAO,CAAAC,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,eAAe,KAAQ,gBAAgB,CAChD,OAASC,YAAY,KAAQ,gBAAgB,CAC7C,MAAO,CAAAC,MAAM,KAAM,QAAQ,CAC3B,OAASC,SAAS,KAAQ,WAAW,CACrC,MAAO,CAAAC,KAAK,KAAM,OAAO,CACzB,OAASC,WAAW,CAAEC,UAAU,KAAQ,gBAAgB,CACxD,OAASC,eAAe,KAAQ,gBAAgB,CAChD,MAAO,CAAAC,KAAK,KAAM,6BAA6B,CAC/C,OAASC,aAAa,KAAQ,gBAAgB,CAC9C,MAAO,CAAAC,iBAAiB,KAAM,qBAAqB,CACnD,MAAO,CAAAC,WAAW,KAAM,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAExC,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAA4B,KAAAC,gBAAA,CAAAC,cAAA,CAAAC,iBAAA,CAAAC,eAAA,IAA3B,CAAEC,IAAI,CAAEC,YAAa,CAAC,CAAAN,IAAA,CAC1C,KAAM,CAACO,WAAW,CAAEC,cAAc,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CACrD,KAAM,CAAC6B,kBAAkB,CAAEC,qBAAqB,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CACnE,KAAM,CAAC+B,SAAS,CAAEC,YAAY,CAAC,CAAGhC,QAAQ,CAAC,EAAE,CAAC,CAC9C,KAAM,CAACiC,aAAa,CAAEC,YAAY,CAAC,CAAGlC,QAAQ,CAAC,KAAK,CAAC,CACrDC,SAAS,CAAC,IAAM,CACf,KAAM,CAAAkC,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CACH,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAA7B,KAAK,CAAC8B,GAAG,IAAAC,MAAA,CAC5BxC,WAAW,2BAAAwC,MAAA,CAAyBZ,YAAY,MAAAY,MAAA,CAAIb,IAAI,CAACc,GAAG,CAChE,CAAC,CACDX,cAAc,CAACQ,QAAQ,CAACI,IAAI,CAACb,WAAW,CAAC,CAC1C,CAAE,MAAOc,KAAK,CAAE,CACfC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CAC1D,CACD,CAAC,CAED,GAAIf,YAAY,CAAE,CACjBS,oBAAoB,CAAC,CAAC,CACvB,CACD,CAAC,CAAE,CAACT,YAAY,CAAED,IAAI,CAACc,GAAG,CAAC,CAAC,CAE5B,KAAM,CAAAI,YAAY,CAAG,KAAAA,CAAA,GAAY,CAChC,GAAI,CACH,KAAM,CAAApC,KAAK,CAACqC,IAAI,IAAAN,MAAA,CAAIxC,WAAW,kBAAiB,CAC/C+C,UAAU,CAAEnB,YAAY,CACxBoB,UAAU,CAAErB,IAAI,CAACc,GAClB,CAAC,CAAC,CAEFX,cAAc,CAAC,IAAI,CAAC,CAAE;AACtBc,OAAO,CAACK,GAAG,CAAC,4BAA4B,CAAC,CAC1C,CAAE,MAAON,KAAK,CAAE,CACfC,OAAO,CAACD,KAAK,CAAC,wBAAwB,CAAEA,KAAK,CAAC,CAC/C,CACD,CAAC,CAED,KAAM,CAAAO,cAAc,CAAG,KAAAA,CAAA,GAAY,CAClC,GAAI,CACH,KAAM,CAAAzC,KAAK,CAACqC,IAAI,IAAAN,MAAA,CAAIxC,WAAW,oBAAmB,CACjD+C,UAAU,CAAEnB,YAAY,CACxBoB,UAAU,CAAErB,IAAI,CAACc,GAClB,CAAC,CAAC,CAEFX,cAAc,CAAC,KAAK,CAAC,CAAE;AACvBc,OAAO,CAACK,GAAG,CAAC,8BAA8B,CAAC,CAC5C,CAAE,MAAON,KAAK,CAAE,CACfC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CACjD,CACD,CAAC,CAED,KAAM,CAAAQ,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACxC,GAAI,CACH,KAAM,CAAAb,QAAQ,CAAG,KAAM,CAAA7B,KAAK,CAAC8B,GAAG,IAAAC,MAAA,CAC5BxC,WAAW,sBAAAwC,MAAA,CAAoBb,IAAI,CAACc,GAAG,CAC3C,CAAC,CACDP,YAAY,CAACI,QAAQ,CAACI,IAAI,CAACT,SAAS,CAAC,CACrCD,qBAAqB,CAAC,CAACD,kBAAkB,CAAC,CAC3C,CAAE,MAAOY,KAAK,CAAE,CACfC,OAAO,CAACD,KAAK,CAAC,4BAA4B,CAAEA,KAAK,CAAC,CACnD,CACD,CAAC,CAED,mBACCzB,IAAA,QAAAkC,QAAA,cACChC,KAAA,QAAKiC,SAAS,CAAC,6EAA6E,CAAAD,QAAA,eAC3FhC,KAAA,QAAKiC,SAAS,CAAC,qFAAqF,CAAAD,QAAA,eACnGhC,KAAA,CAAChB,IAAI,EAACkD,EAAE,CAAE,WAAW,EAAG3B,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEc,GAAG,CAAC,CAACY,SAAS,CAAC,YAAY,CAAAD,QAAA,eACxDlC,IAAA,QACCqC,GAAG,CAAE5B,IAAI,EAAIA,IAAI,CAAC6B,UAAU,CAAG7B,IAAI,CAAC6B,UAAU,CAAGhD,SAAU,CAC3DiD,GAAG,CAAE9B,IAAI,EAAIA,IAAI,CAAC+B,KAAM,CACxBL,SAAS,CAAC,qCAAqC,CAC/C,CAAC,cACFjC,KAAA,QAAKiC,SAAS,CAAC,8BAA8B,CAAAD,QAAA,eAC5ChC,KAAA,QAAKiC,SAAS,CAAC,mBAAmB,CAAAD,QAAA,eACjChC,KAAA,MACCiC,SAAS,CAAC,mCAAmC,CAC7CM,KAAK,CAAE,CACNC,YAAY,CAAE,MACf,CAAE,CAAAR,QAAA,EAEDzB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEkC,SAAS,CAAC,GAAC,CAAClC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEmC,QAAQ,EAC/B,CAAC,CACH,CAAAnC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEoC,IAAI,gBACV7C,IAAA,CAACJ,aAAa,EACbuC,SAAS,CAAC,MAAM,CAChBM,KAAK,CAAE,CAAEK,KAAK,CAAE,SAAU,CAAE,CAC5B,CACD,EACG,CAAC,cACN5C,KAAA,SAAMiC,SAAS,CAAC,eAAe,CAAAD,QAAA,GAAA7B,gBAAA,CAC7BI,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEsC,UAAU,UAAA1C,gBAAA,UAAAA,gBAAA,CAAI,eAAe,CAAC,gBAAS,CAAC,GAAG,CACjDhB,MAAM,CAACoB,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEuC,SAAS,CAAC,CAACC,OAAO,CAAC,CAAC,EAC7B,CAAC,EACH,CAAC,EACD,CAAC,cACPjD,IAAA,QAAKmC,SAAS,CAAC,8BAA8B,CAAAD,QAAA,cAC5ChC,KAAA,WACCuC,KAAK,CAAE,CACNS,eAAe,CAAE,OAAO,CACxBJ,KAAK,CAAE,OAAO,CACdK,OAAO,CAAE,MAAM,CACfC,YAAY,CAAE,KAAK,CACnBC,OAAO,CAAE,MAAM,CACfC,UAAU,CAAE,QAAQ,CACpBC,cAAc,CAAE,QACjB,CAAE,CACFC,OAAO,CAAEA,CAAA,GAAMtC,YAAY,CAAC,IAAI,CAAE,CAAAgB,QAAA,eAElClC,IAAA,QACCyD,KAAK,CAAC,4BAA4B,CAClCC,OAAO,CAAC,aAAa,CACrBC,KAAK,CAAC,IAAI,CACVC,IAAI,CAAC,cAAc,CAAA1B,QAAA,cAEnBlC,IAAA,SAAM6D,CAAC,CAAC,2nBAA2nB,CAAO,CAAC,CACvoB,CAAC,cACN7D,IAAA,SAAAkC,QAAA,CAAM,cAAY,CAAM,CAAC,EAClB,CAAC,CACL,CAAC,cACNlC,IAAA,QAAKmC,SAAS,CAAC,EAAE,CAAAD,QAAA,CACfxB,YAAY,EAAID,IAAI,EAAIC,YAAY,GAAKD,IAAI,CAACc,GAAG,eACjDrB,KAAA,WACCsD,OAAO,CAAE7C,WAAW,CAAGqB,cAAc,CAAGL,YAAa,CACrDQ,SAAS,qDAAAb,MAAA,CACRX,WAAW,CAAG,cAAc,CAAG,EAAE,CAC/B,CACH8B,KAAK,CAAE,CAAES,eAAe,CAAEvC,WAAW,CAAG,SAAS,CAAG,EAAG,CAAE,CAAAuB,QAAA,EAExDvB,WAAW,CAAG,UAAU,CAAG,QAAQ,cACpCX,IAAA,CAACb,eAAe,EAAC2E,IAAI,CAAE,EAAG,CAAC3B,SAAS,CAAC,qBAAqB,CAAE,CAAC,EACtD,CACR,CACG,CAAC,EACF,CAAC,cACNjC,KAAA,QAAKiC,SAAS,CAAC,6DAA6D,CAAAD,QAAA,eAC3EhC,KAAA,QAAKiC,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACrDlC,IAAA,CAACZ,YAAY,EAAC+C,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAClDnC,IAAA,SAAAkC,QAAA,EAAA5B,cAAA,CAAOG,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEsD,QAAQ,UAAAzD,cAAA,UAAAA,cAAA,CAAI,WAAW,CAAO,CAAC,EACxC,CAAC,cACNJ,KAAA,QAAKiC,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACrDlC,IAAA,SAAAkC,QAAA,CAAM,aAAW,CAAM,CAAC,cACxBlC,IAAA,SAAAkC,QAAA,EAAA3B,iBAAA,CAAOE,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEsC,UAAU,UAAAxC,iBAAA,UAAAA,iBAAA,CAAI,iBAAiB,CAAO,CAAC,EAChD,CAAC,cACNL,KAAA,QACCiC,SAAS,CAAC,uCAAuC,CACjDM,KAAK,CAAE,CAAEuB,MAAM,CAAE,SAAU,CAAE,CAC7BR,OAAO,CAAEvB,oBAAqB,CAAAC,QAAA,eAE9BlC,IAAA,SAAAkC,QAAA,CAAM,YAAU,CAAM,CAAC,cACvBhC,KAAA,SAAAgC,QAAA,EAAOzB,IAAI,SAAJA,IAAI,kBAAAD,eAAA,CAAJC,IAAI,CAAEM,SAAS,UAAAP,eAAA,iBAAfA,eAAA,CAAiByD,MAAM,CAAC,YAAU,EAAM,CAAC,EAC5C,CAAC,EACF,CAAC,cASN/D,KAAA,QAAKiC,SAAS,CAAC,sCAAsC,CAAAD,QAAA,eACpDlC,IAAA,MAAGmC,SAAS,CAAC,qCAAqC,CAAAD,QAAA,CAAC,QAAM,CAAG,CAAC,cAC7DlC,IAAA,QAAKmC,SAAS,CAAC,sBAAsB,CAAAD,QAAA,CACnCzB,IAAI,CAACyD,MAAM,EAAIzD,IAAI,CAACyD,MAAM,CAACD,MAAM,CAAG,CAAC,cACrCjE,IAAA,CAACH,iBAAiB,EAACqE,MAAM,CAAEzD,IAAI,CAACyD,MAAO,CAAE,CAAC,cAE1ClE,IAAA,SAAMmC,SAAS,CAAC,eAAe,CAAAD,QAAA,CAAC,iBAAe,CAAM,CACrD,CACG,CAAC,EACF,CAAC,CAGLrB,kBAAkB,eAClBb,IAAA,CAACL,KAAK,EAACwE,OAAO,CAAElC,oBAAqB,CAAClB,SAAS,CAAEA,SAAU,CAAE,CAC7D,CACAE,aAAa,eACbjB,IAAA,CAACF,WAAW,EACXsE,UAAU,CAAEA,CAAA,GAAM,CACjBlD,YAAY,CAAC,KAAK,CAAC,CACpB,CAAE,CACFmD,UAAU,CAAE5D,IAAI,CAACyD,MAAO,CACxBI,YAAY,CAAE7D,IAAI,CAACsD,QAAS,CAC5BQ,QAAQ,CAAE9D,IAAI,CAACsC,UAAW,CAC1ByB,cAAc,CAAE/D,IAAI,CAAC6B,UAAW,CAChCmC,MAAM,CAAEhE,IAAI,CAACc,GAAI,CACjB,CACD,cACDrB,KAAA,QAAKiC,SAAS,CAAC,sCAAsC,CAAAD,QAAA,eACpDlC,IAAA,MAAGmC,SAAS,CAAC,qCAAqC,CAAAD,QAAA,CAAC,gBAAc,CAAG,CAAC,cAErEhC,KAAA,QAAKiC,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACrDlC,IAAA,CAACR,WAAW,EAAC2C,SAAS,CAAC,wBAAwB,CAAE,CAAC,cAClDnC,IAAA,SAAAkC,QAAA,CAAM,WAAS,CAAM,CAAC,EAClB,CAAC,cACNhC,KAAA,QAAKiC,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACrDlC,IAAA,CAACN,eAAe,EAACyC,SAAS,CAAC,wBAAwB,CAAE,CAAC,cACtDnC,IAAA,SAAAkC,QAAA,CAAM,SAAO,CAAM,CAAC,EAChB,CAAC,cACNhC,KAAA,QAAKiC,SAAS,CAAC,uCAAuC,CAAAD,QAAA,eACrDlC,IAAA,CAACP,UAAU,EAAC0C,SAAS,CAAC,wBAAwB,CAAE,CAAC,cACjDnC,IAAA,SAAAkC,QAAA,CAAM,UAAQ,CAAM,CAAC,EACjB,CAAC,EACF,CAAC,EACF,CAAC,CACF,CAAC,CAER,CAAC,CAED,cAAe,CAAA/B,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}