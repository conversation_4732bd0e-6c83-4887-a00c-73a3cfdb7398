{"ast": null, "code": "/**\n * @license React\n * use-sync-external-store-shim.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';\n\nvar e = require(\"react\");\nfunction h(a, b) {\n  return a === b && (0 !== a || 1 / a === 1 / b) || a !== a && b !== b;\n}\nvar k = \"function\" === typeof Object.is ? Object.is : h,\n  l = e.useState,\n  m = e.useEffect,\n  n = e.useLayoutEffect,\n  p = e.useDebugValue;\nfunction q(a, b) {\n  var d = b(),\n    f = l({\n      inst: {\n        value: d,\n        getSnapshot: b\n      }\n    }),\n    c = f[0].inst,\n    g = f[1];\n  n(function () {\n    c.value = d;\n    c.getSnapshot = b;\n    r(c) && g({\n      inst: c\n    });\n  }, [a, d, b]);\n  m(function () {\n    r(c) && g({\n      inst: c\n    });\n    return a(function () {\n      r(c) && g({\n        inst: c\n      });\n    });\n  }, [a]);\n  p(d);\n  return d;\n}\nfunction r(a) {\n  var b = a.getSnapshot;\n  a = a.value;\n  try {\n    var d = b();\n    return !k(a, d);\n  } catch (f) {\n    return !0;\n  }\n}\nfunction t(a, b) {\n  return b();\n}\nvar u = \"undefined\" === typeof window || \"undefined\" === typeof window.document || \"undefined\" === typeof window.document.createElement ? t : q;\nexports.useSyncExternalStore = void 0 !== e.useSyncExternalStore ? e.useSyncExternalStore : u;", "map": {"version": 3, "names": ["e", "require", "h", "a", "b", "k", "Object", "is", "l", "useState", "m", "useEffect", "n", "useLayoutEffect", "p", "useDebugValue", "q", "d", "f", "inst", "value", "getSnapshot", "c", "g", "r", "t", "u", "window", "document", "createElement", "exports", "useSyncExternalStore"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/use-sync-external-store/cjs/use-sync-external-store-shim.production.min.js"], "sourcesContent": ["/**\n * @license React\n * use-sync-external-store-shim.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n'use strict';var e=require(\"react\");function h(a,b){return a===b&&(0!==a||1/a===1/b)||a!==a&&b!==b}var k=\"function\"===typeof Object.is?Object.is:h,l=e.useState,m=e.useEffect,n=e.useLayoutEffect,p=e.useDebugValue;function q(a,b){var d=b(),f=l({inst:{value:d,getSnapshot:b}}),c=f[0].inst,g=f[1];n(function(){c.value=d;c.getSnapshot=b;r(c)&&g({inst:c})},[a,d,b]);m(function(){r(c)&&g({inst:c});return a(function(){r(c)&&g({inst:c})})},[a]);p(d);return d}\nfunction r(a){var b=a.getSnapshot;a=a.value;try{var d=b();return!k(a,d)}catch(f){return!0}}function t(a,b){return b()}var u=\"undefined\"===typeof window||\"undefined\"===typeof window.document||\"undefined\"===typeof window.document.createElement?t:q;exports.useSyncExternalStore=void 0!==e.useSyncExternalStore?e.useSyncExternalStore:u;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,YAAY;;AAAC,IAAIA,CAAC,GAACC,OAAO,CAAC,OAAO,CAAC;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOD,CAAC,KAAGC,CAAC,KAAG,CAAC,KAAGD,CAAC,IAAE,CAAC,GAACA,CAAC,KAAG,CAAC,GAACC,CAAC,CAAC,IAAED,CAAC,KAAGA,CAAC,IAAEC,CAAC,KAAGA,CAAC;AAAA;AAAC,IAAIC,CAAC,GAAC,UAAU,KAAG,OAAOC,MAAM,CAACC,EAAE,GAACD,MAAM,CAACC,EAAE,GAACL,CAAC;EAACM,CAAC,GAACR,CAAC,CAACS,QAAQ;EAACC,CAAC,GAACV,CAAC,CAACW,SAAS;EAACC,CAAC,GAACZ,CAAC,CAACa,eAAe;EAACC,CAAC,GAACd,CAAC,CAACe,aAAa;AAAC,SAASC,CAACA,CAACb,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIa,CAAC,GAACb,CAAC,CAAC,CAAC;IAACc,CAAC,GAACV,CAAC,CAAC;MAACW,IAAI,EAAC;QAACC,KAAK,EAACH,CAAC;QAACI,WAAW,EAACjB;MAAC;IAAC,CAAC,CAAC;IAACkB,CAAC,GAACJ,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI;IAACI,CAAC,GAACL,CAAC,CAAC,CAAC,CAAC;EAACN,CAAC,CAAC,YAAU;IAACU,CAAC,CAACF,KAAK,GAACH,CAAC;IAACK,CAAC,CAACD,WAAW,GAACjB,CAAC;IAACoB,CAAC,CAACF,CAAC,CAAC,IAAEC,CAAC,CAAC;MAACJ,IAAI,EAACG;IAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACnB,CAAC,EAACc,CAAC,EAACb,CAAC,CAAC,CAAC;EAACM,CAAC,CAAC,YAAU;IAACc,CAAC,CAACF,CAAC,CAAC,IAAEC,CAAC,CAAC;MAACJ,IAAI,EAACG;IAAC,CAAC,CAAC;IAAC,OAAOnB,CAAC,CAAC,YAAU;MAACqB,CAAC,CAACF,CAAC,CAAC,IAAEC,CAAC,CAAC;QAACJ,IAAI,EAACG;MAAC,CAAC,CAAC;IAAA,CAAC,CAAC;EAAA,CAAC,EAAC,CAACnB,CAAC,CAAC,CAAC;EAACW,CAAC,CAACG,CAAC,CAAC;EAAC,OAAOA,CAAC;AAAA;AAClc,SAASO,CAACA,CAACrB,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACD,CAAC,CAACkB,WAAW;EAAClB,CAAC,GAACA,CAAC,CAACiB,KAAK;EAAC,IAAG;IAAC,IAAIH,CAAC,GAACb,CAAC,CAAC,CAAC;IAAC,OAAM,CAACC,CAAC,CAACF,CAAC,EAACc,CAAC,CAAC;EAAA,CAAC,QAAMC,CAAC,EAAC;IAAC,OAAM,CAAC,CAAC;EAAA;AAAC;AAAC,SAASO,CAACA,CAACtB,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOA,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIsB,CAAC,GAAC,WAAW,KAAG,OAAOC,MAAM,IAAE,WAAW,KAAG,OAAOA,MAAM,CAACC,QAAQ,IAAE,WAAW,KAAG,OAAOD,MAAM,CAACC,QAAQ,CAACC,aAAa,GAACJ,CAAC,GAACT,CAAC;AAACc,OAAO,CAACC,oBAAoB,GAAC,KAAK,CAAC,KAAG/B,CAAC,CAAC+B,oBAAoB,GAAC/B,CAAC,CAAC+B,oBAAoB,GAACL,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}