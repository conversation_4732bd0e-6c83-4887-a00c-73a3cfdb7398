{"ast": null, "code": "'use client';\n\nexport { TabPanel } from './TabPanel';\nexport * from './TabPanel.types';\nexport * from './tabPanelClasses';", "map": {"version": 3, "names": ["TabPanel"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/TabPanel/index.js"], "sourcesContent": ["'use client';\n\nexport { TabPanel } from './TabPanel';\nexport * from './TabPanel.types';\nexport * from './tabPanelClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,QAAQ,YAAY;AACrC,cAAc,kBAAkB;AAChC,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}