{"ast": null, "code": "import v35 from './v35.js';\nimport md5 from './md5.js';\nconst v3 = v35('v3', 0x30, md5);\nexport default v3;", "map": {"version": 3, "names": ["v35", "md5", "v3"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/uuid/dist/esm-browser/v3.js"], "sourcesContent": ["import v35 from './v35.js';\nimport md5 from './md5.js';\nconst v3 = v35('v3', 0x30, md5);\nexport default v3;"], "mappings": "AAAA,OAAOA,GAAG,MAAM,UAAU;AAC1B,OAAOC,GAAG,MAAM,UAAU;AAC1B,MAAMC,EAAE,GAAGF,GAAG,CAAC,IAAI,EAAE,IAAI,EAAEC,GAAG,CAAC;AAC/B,eAAeC,EAAE", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}