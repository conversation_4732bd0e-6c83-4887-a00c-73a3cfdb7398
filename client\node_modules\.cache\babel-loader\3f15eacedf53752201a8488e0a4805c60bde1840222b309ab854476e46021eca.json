{"ast": null, "code": "export { default as CSSTransition } from './CSSTransition';\nexport { default as ReplaceTransition } from './ReplaceTransition';\nexport { default as SwitchTransition } from './SwitchTransition';\nexport { default as TransitionGroup } from './TransitionGroup';\nexport { default as Transition } from './Transition';\nexport { default as config } from './config';", "map": {"version": 3, "names": ["default", "CSSTransition", "ReplaceTransition", "SwitchTransition", "TransitionGroup", "Transition", "config"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/react-transition-group/esm/index.js"], "sourcesContent": ["export { default as CSSTransition } from './CSSTransition';\nexport { default as ReplaceTransition } from './ReplaceTransition';\nexport { default as SwitchTransition } from './SwitchTransition';\nexport { default as TransitionGroup } from './TransitionGroup';\nexport { default as Transition } from './Transition';\nexport { default as config } from './config';"], "mappings": "AAAA,SAASA,OAAO,IAAIC,aAAa,QAAQ,iBAAiB;AAC1D,SAASD,OAAO,IAAIE,iBAAiB,QAAQ,qBAAqB;AAClE,SAASF,OAAO,IAAIG,gBAAgB,QAAQ,oBAAoB;AAChE,SAASH,OAAO,IAAII,eAAe,QAAQ,mBAAmB;AAC9D,SAASJ,OAAO,IAAIK,UAAU,QAAQ,cAAc;AACpD,SAASL,OAAO,IAAIM,MAAM,QAAQ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}