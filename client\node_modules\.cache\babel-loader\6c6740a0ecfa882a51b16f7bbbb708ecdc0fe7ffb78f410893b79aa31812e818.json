{"ast": null, "code": "import React from\"react\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const TextInput=/*#__PURE__*/React.forwardRef((_ref,ref)=>{let{type,placeholder,styles,label,labelStyles,register,name,error}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"w-full flex flex-col mt-2\",children:[label&&/*#__PURE__*/_jsx(\"p\",{className:\"text-ascent-2 text-sm mb-2 \".concat(labelStyles),children:label}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"input\",{type:type,name:name,placeholder:placeholder,ref:ref,className:\"bg-secondary rounded border border-[#66666690] outline-none text-sm text-ascent-1 px-4 py-3 placeholder:text-[#666] \".concat(styles),...register,\"aria-invalid\":error?\"true\":\"false\"})}),error&&/*#__PURE__*/_jsx(\"span\",{className:\"text-xs text-[#f64949fe] mt-0.5 \",children:error})]});});export default TextInput;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "TextInput", "forwardRef", "_ref", "ref", "type", "placeholder", "styles", "label", "labelStyles", "register", "name", "error", "className", "children", "concat"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/components/TextInput.jsx"], "sourcesContent": ["import React from \"react\";\r\n\r\nconst TextInput = React.forwardRef(\r\n  (\r\n    { type, placeholder, styles, label, labelStyles, register, name, error },\r\n    ref\r\n  ) => {\r\n    return (\r\n      <div className='w-full flex flex-col mt-2'>\r\n        {label && (\r\n          <p className={`text-ascent-2 text-sm mb-2 ${labelStyles}`}>{label}</p>\r\n        )}\r\n\r\n        <div>\r\n          <input\r\n            type={type}\r\n            name={name}\r\n            placeholder={placeholder}\r\n            ref={ref}\r\n            className={`bg-secondary rounded border border-[#66666690] outline-none text-sm text-ascent-1 px-4 py-3 placeholder:text-[#666] ${styles}`}\r\n            {...register}\r\n            aria-invalid={error ? \"true\" : \"false\"}\r\n          />\r\n        </div>\r\n        {error && (\r\n          <span className='text-xs text-[#f64949fe] mt-0.5 '>{error}</span>\r\n        )}\r\n      </div>\r\n    );\r\n  }\r\n);\r\n\r\nexport default TextInput;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAE1B,KAAM,CAAAC,SAAS,cAAGL,KAAK,CAACM,UAAU,CAChC,CAAAC,IAAA,CAEEC,GAAG,GACA,IAFH,CAAEC,IAAI,CAAEC,WAAW,CAAEC,MAAM,CAAEC,KAAK,CAAEC,WAAW,CAAEC,QAAQ,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAAT,IAAA,CAGxE,mBACEH,KAAA,QAAKa,SAAS,CAAC,2BAA2B,CAAAC,QAAA,EACvCN,KAAK,eACJV,IAAA,MAAGe,SAAS,+BAAAE,MAAA,CAAgCN,WAAW,CAAG,CAAAK,QAAA,CAAEN,KAAK,CAAI,CACtE,cAEDV,IAAA,QAAAgB,QAAA,cACEhB,IAAA,UACEO,IAAI,CAAEA,IAAK,CACXM,IAAI,CAAEA,IAAK,CACXL,WAAW,CAAEA,WAAY,CACzBF,GAAG,CAAEA,GAAI,CACTS,SAAS,wHAAAE,MAAA,CAAyHR,MAAM,CAAG,IACvIG,QAAQ,CACZ,eAAcE,KAAK,CAAG,MAAM,CAAG,OAAQ,CACxC,CAAC,CACC,CAAC,CACLA,KAAK,eACJd,IAAA,SAAMe,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEF,KAAK,CAAO,CACjE,EACE,CAAC,CAEV,CACF,CAAC,CAED,cAAe,CAAAX,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}