{"ast": null, "code": "import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const CustomButton=_ref=>{let{title,containerStyles,iconRight,type,onClick}=_ref;return/*#__PURE__*/_jsxs(\"button\",{onClick:onClick,type:type||\"button\",className:\"inline-flex items-center text-base \".concat(containerStyles),children:[title,iconRight&&/*#__PURE__*/_jsx(\"div\",{className:\"ml-2\",children:iconRight})]});};export default CustomButton;", "map": {"version": 3, "names": ["CustomButton", "_ref", "title", "containerStyles", "iconRight", "type", "onClick", "_jsxs", "className", "concat", "children", "_jsx"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/components/CustomButton.jsx"], "sourcesContent": ["const CustomButton = ({ title, containerStyles, iconRight, type, onClick }) => {\r\n  return (\r\n    <button\r\n      onClick={onClick}\r\n      type={type || \"button\"}\r\n      className={`inline-flex items-center text-base ${containerStyles}`}\r\n    >\r\n      {title}\r\n\r\n      {iconRight && <div className='ml-2'>{iconRight}</div>}\r\n    </button>\r\n  );\r\n};\r\n\r\nexport default CustomButton;\r\n"], "mappings": "wFAAA,KAAM,CAAAA,YAAY,CAAGC,IAAA,EAA0D,IAAzD,CAAEC,KAAK,CAAEC,eAAe,CAAEC,SAAS,CAAEC,IAAI,CAAEC,OAAQ,CAAC,CAAAL,IAAA,CACxE,mBACEM,KAAA,WACED,OAAO,CAAEA,OAAQ,CACjBD,IAAI,CAAEA,IAAI,EAAI,QAAS,CACvBG,SAAS,uCAAAC,MAAA,CAAwCN,eAAe,CAAG,CAAAO,QAAA,EAElER,KAAK,CAELE,SAAS,eAAIO,IAAA,QAAKH,SAAS,CAAC,MAAM,CAAAE,QAAA,CAAEN,SAAS,CAAM,CAAC,EAC/C,CAAC,CAEb,CAAC,CAED,cAAe,CAAAJ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}