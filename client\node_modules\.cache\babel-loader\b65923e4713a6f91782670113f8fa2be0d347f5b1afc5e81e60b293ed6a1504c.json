{"ast": null, "code": "import setRef from '@mui/utils/setRef';\nexport default setRef;", "map": {"version": 3, "names": ["setRef"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/utils/setRef.js"], "sourcesContent": ["import setRef from '@mui/utils/setRef';\nexport default setRef;"], "mappings": "AAAA,OAAOA,MAAM,MAAM,mBAAmB;AACtC,eAAeA,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}