{"ast": null, "code": "'use client';\n\nexport { useSnackbar } from './useSnackbar';\nexport * from './useSnackbar.types';", "map": {"version": 3, "names": ["useSnackbar"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/useSnackbar/index.js"], "sourcesContent": ["'use client';\n\nexport { useSnackbar } from './useSnackbar';\nexport * from './useSnackbar.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,WAAW,QAAQ,eAAe;AAC3C,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}