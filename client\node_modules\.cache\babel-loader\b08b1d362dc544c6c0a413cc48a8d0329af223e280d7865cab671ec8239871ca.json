{"ast": null, "code": "export { default } from './setRef';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/setRef/index.js"], "sourcesContent": ["export { default } from './setRef';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}