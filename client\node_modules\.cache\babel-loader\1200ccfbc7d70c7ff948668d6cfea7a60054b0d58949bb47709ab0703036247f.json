{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useThemeProps from '../styles/useThemeProps';\nimport GlobalStyles from '../GlobalStyles';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const html = (theme, enableColorScheme) => _extends({\n  WebkitFontSmoothing: 'antialiased',\n  // Antialiasing.\n  MozOsxFontSmoothing: 'grayscale',\n  // Antialiasing.\n  // Change from `box-sizing: content-box` so that `width`\n  // is not affected by `padding` or `border`.\n  boxSizing: 'border-box',\n  // Fix font resize problem in iOS\n  WebkitTextSizeAdjust: '100%'\n}, enableColorScheme && !theme.vars && {\n  colorScheme: theme.palette.mode\n});\nexport const body = theme => _extends({\n  color: (theme.vars || theme).palette.text.primary\n}, theme.typography.body1, {\n  backgroundColor: (theme.vars || theme).palette.background.default,\n  '@media print': {\n    // Save printer ink.\n    backgroundColor: (theme.vars || theme).palette.common.white\n  }\n});\nexport const styles = function (theme) {\n  let enableColorScheme = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n  var _theme$components;\n  const colorSchemeStyles = {};\n  if (enableColorScheme && theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(_ref => {\n      let [key, scheme] = _ref;\n      var _scheme$palette;\n      colorSchemeStyles[theme.getColorSchemeSelector(key).replace(/\\s*&/, '')] = {\n        colorScheme: (_scheme$palette = scheme.palette) == null ? void 0 : _scheme$palette.mode\n      };\n    });\n  }\n  let defaultStyles = _extends({\n    html: html(theme, enableColorScheme),\n    '*, *::before, *::after': {\n      boxSizing: 'inherit'\n    },\n    'strong, b': {\n      fontWeight: theme.typography.fontWeightBold\n    },\n    body: _extends({\n      margin: 0\n    }, body(theme), {\n      // Add support for document.body.requestFullScreen().\n      // Other elements, if background transparent, are not supported.\n      '&::backdrop': {\n        backgroundColor: (theme.vars || theme).palette.background.default\n      }\n    })\n  }, colorSchemeStyles);\n  const themeOverrides = (_theme$components = theme.components) == null || (_theme$components = _theme$components.MuiCssBaseline) == null ? void 0 : _theme$components.styleOverrides;\n  if (themeOverrides) {\n    defaultStyles = [defaultStyles, themeOverrides];\n  }\n  return defaultStyles;\n};\n\n/**\n * Kickstart an elegant, consistent, and simple baseline to build upon.\n */\nfunction CssBaseline(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiCssBaseline'\n  });\n  const {\n    children,\n    enableColorScheme = false\n  } = props;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(GlobalStyles, {\n      styles: theme => styles(theme, enableColorScheme)\n    }), children]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? CssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */\n  enableColorScheme: PropTypes.bool\n} : void 0;\nexport default CssBaseline;", "map": {"version": 3, "names": ["_extends", "React", "PropTypes", "useThemeProps", "GlobalStyles", "jsx", "_jsx", "jsxs", "_jsxs", "html", "theme", "enableColorScheme", "WebkitFontSmoothing", "MozOsxFontSmoothing", "boxSizing", "WebkitTextSizeAdjust", "vars", "colorScheme", "palette", "mode", "body", "color", "text", "primary", "typography", "body1", "backgroundColor", "background", "default", "common", "white", "styles", "arguments", "length", "undefined", "_theme$components", "colorSchemeStyles", "colorSchemes", "Object", "entries", "for<PERSON>ach", "_ref", "key", "scheme", "_scheme$palette", "getColorSchemeSelector", "replace", "defaultStyles", "fontWeight", "fontWeightBold", "margin", "themeOverrides", "components", "MuiCssBaseline", "styleOverrides", "CssBaseline", "inProps", "props", "name", "children", "Fragment", "process", "env", "NODE_ENV", "propTypes", "node", "bool"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/CssBaseline/CssBaseline.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport useThemeProps from '../styles/useThemeProps';\nimport GlobalStyles from '../GlobalStyles';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nimport { jsxs as _jsxs } from \"react/jsx-runtime\";\nexport const html = (theme, enableColorScheme) => _extends({\n  WebkitFontSmoothing: 'antialiased',\n  // Antialiasing.\n  MozOsxFontSmoothing: 'grayscale',\n  // Antialiasing.\n  // Change from `box-sizing: content-box` so that `width`\n  // is not affected by `padding` or `border`.\n  boxSizing: 'border-box',\n  // Fix font resize problem in iOS\n  WebkitTextSizeAdjust: '100%'\n}, enableColorScheme && !theme.vars && {\n  colorScheme: theme.palette.mode\n});\nexport const body = theme => _extends({\n  color: (theme.vars || theme).palette.text.primary\n}, theme.typography.body1, {\n  backgroundColor: (theme.vars || theme).palette.background.default,\n  '@media print': {\n    // Save printer ink.\n    backgroundColor: (theme.vars || theme).palette.common.white\n  }\n});\nexport const styles = (theme, enableColorScheme = false) => {\n  var _theme$components;\n  const colorSchemeStyles = {};\n  if (enableColorScheme && theme.colorSchemes) {\n    Object.entries(theme.colorSchemes).forEach(([key, scheme]) => {\n      var _scheme$palette;\n      colorSchemeStyles[theme.getColorSchemeSelector(key).replace(/\\s*&/, '')] = {\n        colorScheme: (_scheme$palette = scheme.palette) == null ? void 0 : _scheme$palette.mode\n      };\n    });\n  }\n  let defaultStyles = _extends({\n    html: html(theme, enableColorScheme),\n    '*, *::before, *::after': {\n      boxSizing: 'inherit'\n    },\n    'strong, b': {\n      fontWeight: theme.typography.fontWeightBold\n    },\n    body: _extends({\n      margin: 0\n    }, body(theme), {\n      // Add support for document.body.requestFullScreen().\n      // Other elements, if background transparent, are not supported.\n      '&::backdrop': {\n        backgroundColor: (theme.vars || theme).palette.background.default\n      }\n    })\n  }, colorSchemeStyles);\n  const themeOverrides = (_theme$components = theme.components) == null || (_theme$components = _theme$components.MuiCssBaseline) == null ? void 0 : _theme$components.styleOverrides;\n  if (themeOverrides) {\n    defaultStyles = [defaultStyles, themeOverrides];\n  }\n  return defaultStyles;\n};\n\n/**\n * Kickstart an elegant, consistent, and simple baseline to build upon.\n */\nfunction CssBaseline(inProps) {\n  const props = useThemeProps({\n    props: inProps,\n    name: 'MuiCssBaseline'\n  });\n  const {\n    children,\n    enableColorScheme = false\n  } = props;\n  return /*#__PURE__*/_jsxs(React.Fragment, {\n    children: [/*#__PURE__*/_jsx(GlobalStyles, {\n      styles: theme => styles(theme, enableColorScheme)\n    }), children]\n  });\n}\nprocess.env.NODE_ENV !== \"production\" ? CssBaseline.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * You can wrap a node.\n   */\n  children: PropTypes.node,\n  /**\n   * Enable `color-scheme` CSS property to use `theme.palette.mode`.\n   * For more details, check out https://developer.mozilla.org/en-US/docs/Web/CSS/color-scheme\n   * For browser support, check out https://caniuse.com/?search=color-scheme\n   * @default false\n   */\n  enableColorScheme: PropTypes.bool\n} : void 0;\nexport default CssBaseline;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,OAAOC,YAAY,MAAM,iBAAiB;AAC1C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,SAASC,IAAI,IAAIC,KAAK,QAAQ,mBAAmB;AACjD,OAAO,MAAMC,IAAI,GAAGA,CAACC,KAAK,EAAEC,iBAAiB,KAAKX,QAAQ,CAAC;EACzDY,mBAAmB,EAAE,aAAa;EAClC;EACAC,mBAAmB,EAAE,WAAW;EAChC;EACA;EACA;EACAC,SAAS,EAAE,YAAY;EACvB;EACAC,oBAAoB,EAAE;AACxB,CAAC,EAAEJ,iBAAiB,IAAI,CAACD,KAAK,CAACM,IAAI,IAAI;EACrCC,WAAW,EAAEP,KAAK,CAACQ,OAAO,CAACC;AAC7B,CAAC,CAAC;AACF,OAAO,MAAMC,IAAI,GAAGV,KAAK,IAAIV,QAAQ,CAAC;EACpCqB,KAAK,EAAE,CAACX,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEQ,OAAO,CAACI,IAAI,CAACC;AAC5C,CAAC,EAAEb,KAAK,CAACc,UAAU,CAACC,KAAK,EAAE;EACzBC,eAAe,EAAE,CAAChB,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEQ,OAAO,CAACS,UAAU,CAACC,OAAO;EACjE,cAAc,EAAE;IACd;IACAF,eAAe,EAAE,CAAChB,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEQ,OAAO,CAACW,MAAM,CAACC;EACxD;AACF,CAAC,CAAC;AACF,OAAO,MAAMC,MAAM,GAAG,SAAAA,CAACrB,KAAK,EAAgC;EAAA,IAA9BC,iBAAiB,GAAAqB,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,KAAK;EACrD,IAAIG,iBAAiB;EACrB,MAAMC,iBAAiB,GAAG,CAAC,CAAC;EAC5B,IAAIzB,iBAAiB,IAAID,KAAK,CAAC2B,YAAY,EAAE;IAC3CC,MAAM,CAACC,OAAO,CAAC7B,KAAK,CAAC2B,YAAY,CAAC,CAACG,OAAO,CAACC,IAAA,IAAmB;MAAA,IAAlB,CAACC,GAAG,EAAEC,MAAM,CAAC,GAAAF,IAAA;MACvD,IAAIG,eAAe;MACnBR,iBAAiB,CAAC1B,KAAK,CAACmC,sBAAsB,CAACH,GAAG,CAAC,CAACI,OAAO,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC,GAAG;QACzE7B,WAAW,EAAE,CAAC2B,eAAe,GAAGD,MAAM,CAACzB,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG0B,eAAe,CAACzB;MACrF,CAAC;IACH,CAAC,CAAC;EACJ;EACA,IAAI4B,aAAa,GAAG/C,QAAQ,CAAC;IAC3BS,IAAI,EAAEA,IAAI,CAACC,KAAK,EAAEC,iBAAiB,CAAC;IACpC,wBAAwB,EAAE;MACxBG,SAAS,EAAE;IACb,CAAC;IACD,WAAW,EAAE;MACXkC,UAAU,EAAEtC,KAAK,CAACc,UAAU,CAACyB;IAC/B,CAAC;IACD7B,IAAI,EAAEpB,QAAQ,CAAC;MACbkD,MAAM,EAAE;IACV,CAAC,EAAE9B,IAAI,CAACV,KAAK,CAAC,EAAE;MACd;MACA;MACA,aAAa,EAAE;QACbgB,eAAe,EAAE,CAAChB,KAAK,CAACM,IAAI,IAAIN,KAAK,EAAEQ,OAAO,CAACS,UAAU,CAACC;MAC5D;IACF,CAAC;EACH,CAAC,EAAEQ,iBAAiB,CAAC;EACrB,MAAMe,cAAc,GAAG,CAAChB,iBAAiB,GAAGzB,KAAK,CAAC0C,UAAU,KAAK,IAAI,IAAI,CAACjB,iBAAiB,GAAGA,iBAAiB,CAACkB,cAAc,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGlB,iBAAiB,CAACmB,cAAc;EACnL,IAAIH,cAAc,EAAE;IAClBJ,aAAa,GAAG,CAACA,aAAa,EAAEI,cAAc,CAAC;EACjD;EACA,OAAOJ,aAAa;AACtB,CAAC;;AAED;AACA;AACA;AACA,SAASQ,WAAWA,CAACC,OAAO,EAAE;EAC5B,MAAMC,KAAK,GAAGtD,aAAa,CAAC;IAC1BsD,KAAK,EAAED,OAAO;IACdE,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;IACJC,QAAQ;IACRhD,iBAAiB,GAAG;EACtB,CAAC,GAAG8C,KAAK;EACT,OAAO,aAAajD,KAAK,CAACP,KAAK,CAAC2D,QAAQ,EAAE;IACxCD,QAAQ,EAAE,CAAC,aAAarD,IAAI,CAACF,YAAY,EAAE;MACzC2B,MAAM,EAAErB,KAAK,IAAIqB,MAAM,CAACrB,KAAK,EAAEC,iBAAiB;IAClD,CAAC,CAAC,EAAEgD,QAAQ;EACd,CAAC,CAAC;AACJ;AACAE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGR,WAAW,CAACS,SAAS,CAAC,yBAAyB;EACrF;EACA;EACA;EACA;EACA;AACF;AACA;EACEL,QAAQ,EAAEzD,SAAS,CAAC+D,IAAI;EACxB;AACF;AACA;AACA;AACA;AACA;EACEtD,iBAAiB,EAAET,SAAS,CAACgE;AAC/B,CAAC,GAAG,KAAK,CAAC;AACV,eAAeX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}