{"ast": null, "code": "export { Portal as default } from '@mui/base/Portal';", "map": {"version": 3, "names": ["Portal", "default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/Portal/index.js"], "sourcesContent": ["export { Portal as default } from '@mui/base/Portal';"], "mappings": "AAAA,SAASA,MAAM,IAAIC,OAAO,QAAQ,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}