{"ast": null, "code": "'use client';\n\nexport { ModalManager } from '@mui/base/unstable_useModal';\nexport { default } from './Modal';\nexport { default as modalClasses } from './modalClasses';\nexport * from './modalClasses';", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "default", "modalClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/Modal/index.js"], "sourcesContent": ["'use client';\n\nexport { ModalManager } from '@mui/base/unstable_useModal';\nexport { default } from './Modal';\nexport { default as modalClasses } from './modalClasses';\nexport * from './modalClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,YAAY,QAAQ,6BAA6B;AAC1D,SAASC,OAAO,QAAQ,SAAS;AACjC,SAASA,OAAO,IAAIC,YAAY,QAAQ,gBAAgB;AACxD,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}