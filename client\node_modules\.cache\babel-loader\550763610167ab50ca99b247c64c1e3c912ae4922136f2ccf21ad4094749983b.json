{"ast": null, "code": "'use client';\n\nexport { useNumberInput as unstable_useNumberInput } from './useNumberInput';\nexport * from './useNumberInput.types';", "map": {"version": 3, "names": ["useNumberInput", "unstable_useNumberInput"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/unstable_useNumberInput/index.js"], "sourcesContent": ["'use client';\n\nexport { useNumberInput as unstable_useNumberInput } from './useNumberInput';\nexport * from './useNumberInput.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,cAAc,IAAIC,uBAAuB,QAAQ,kBAAkB;AAC5E,cAAc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}