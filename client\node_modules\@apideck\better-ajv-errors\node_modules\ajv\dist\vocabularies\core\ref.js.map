{"version": 3, "file": "ref.js", "sourceRoot": "", "sources": ["../../../lib/vocabularies/core/ref.ts"], "names": [], "mappings": ";;;AAEA,uDAAqD;AACrD,kCAAwC;AACxC,mDAAmE;AACnE,+CAAmC;AACnC,2CAAmD;AACnD,6CAAiD;AAEjD,MAAM,GAAG,GAA0B;IACjC,OAAO,EAAE,MAAM;IACf,UAAU,EAAE,QAAQ;IACpB,IAAI,CAAC,GAAe;QAClB,MAAM,EAAC,GAAG,EAAE,MAAM,EAAE,IAAI,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;QACnC,MAAM,EAAC,MAAM,EAAE,SAAS,EAAE,GAAG,EAAE,YAAY,EAAE,IAAI,EAAE,IAAI,EAAC,GAAG,EAAE,CAAA;QAC7D,MAAM,EAAC,IAAI,EAAC,GAAG,GAAG,CAAA;QAClB,IAAI,CAAC,IAAI,KAAK,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC,IAAI,MAAM,KAAK,IAAI,CAAC,MAAM;YAAE,OAAO,WAAW,EAAE,CAAA;QACnF,MAAM,QAAQ,GAAG,oBAAU,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;QAC1D,IAAI,QAAQ,KAAK,SAAS;YAAE,MAAM,IAAI,mBAAe,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW,EAAE,MAAM,EAAE,IAAI,CAAC,CAAA;QACxF,IAAI,QAAQ,YAAY,mBAAS;YAAE,OAAO,YAAY,CAAC,QAAQ,CAAC,CAAA;QAChE,OAAO,eAAe,CAAC,QAAQ,CAAC,CAAA;QAEhC,SAAS,WAAW;YAClB,IAAI,GAAG,KAAK,IAAI;gBAAE,OAAO,OAAO,CAAC,GAAG,EAAE,YAAY,EAAE,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAA;YACpE,MAAM,QAAQ,GAAG,GAAG,CAAC,UAAU,CAAC,MAAM,EAAE,EAAC,GAAG,EAAE,IAAI,EAAC,CAAC,CAAA;YACpD,OAAO,OAAO,CAAC,GAAG,EAAE,IAAA,WAAC,EAAA,GAAG,QAAQ,WAAW,EAAE,IAAI,EAAE,IAAI,CAAC,MAAM,CAAC,CAAA;QACjE,CAAC;QAED,SAAS,YAAY,CAAC,GAAc;YAClC,MAAM,CAAC,GAAG,WAAW,CAAC,GAAG,EAAE,GAAG,CAAC,CAAA;YAC/B,OAAO,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAA;QAClC,CAAC;QAED,SAAS,eAAe,CAAC,GAAc;YACrC,MAAM,OAAO,GAAG,GAAG,CAAC,UAAU,CAC5B,QAAQ,EACR,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,CAAC,CAAC,EAAC,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAA,mBAAS,EAAC,GAAG,CAAC,EAAC,CAAC,CAAC,CAAC,EAAC,GAAG,EAAE,GAAG,EAAC,CAC1E,CAAA;YACD,MAAM,KAAK,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;YAC/B,MAAM,MAAM,GAAG,GAAG,CAAC,SAAS,CAC1B;gBACE,MAAM,EAAE,GAAG;gBACX,SAAS,EAAE,EAAE;gBACb,UAAU,EAAE,aAAG;gBACf,YAAY,EAAE,OAAO;gBACrB,aAAa,EAAE,IAAI;aACpB,EACD,KAAK,CACN,CAAA;YACD,GAAG,CAAC,cAAc,CAAC,MAAM,CAAC,CAAA;YAC1B,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;QACf,CAAC;IACH,CAAC;CACF,CAAA;AAED,SAAgB,WAAW,CAAC,GAAe,EAAE,GAAc;IACzD,MAAM,EAAC,GAAG,EAAC,GAAG,GAAG,CAAA;IACjB,OAAO,GAAG,CAAC,QAAQ;QACjB,CAAC,CAAC,GAAG,CAAC,UAAU,CAAC,UAAU,EAAE,EAAC,GAAG,EAAE,GAAG,CAAC,QAAQ,EAAC,CAAC;QACjD,CAAC,CAAC,IAAA,WAAC,EAAA,GAAG,GAAG,CAAC,UAAU,CAAC,SAAS,EAAE,EAAC,GAAG,EAAE,GAAG,EAAC,CAAC,WAAW,CAAA;AAC1D,CAAC;AALD,kCAKC;AAED,SAAgB,OAAO,CAAC,GAAe,EAAE,CAAO,EAAE,GAAe,EAAE,MAAgB;IACjF,MAAM,EAAC,GAAG,EAAE,EAAE,EAAC,GAAG,GAAG,CAAA;IACrB,MAAM,EAAC,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAC,GAAG,EAAE,CAAA;IAC5C,MAAM,OAAO,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC,CAAC,eAAC,CAAC,IAAI,CAAC,CAAC,CAAC,aAAG,CAAA;IAC/C,IAAI,MAAM;QAAE,YAAY,EAAE,CAAA;;QACrB,WAAW,EAAE,CAAA;IAElB,SAAS,YAAY;QACnB,IAAI,CAAC,GAAG,CAAC,MAAM;YAAE,MAAM,IAAI,KAAK,CAAC,wCAAwC,CAAC,CAAA;QAC1E,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAA;QAC9B,GAAG,CAAC,GAAG,CACL,GAAG,EAAE;YACH,GAAG,CAAC,IAAI,CAAC,IAAA,WAAC,EAAA,SAAS,IAAA,uBAAgB,EAAC,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,EAAE,CAAC,CAAA;YACvD,gBAAgB,CAAC,CAAC,CAAC,CAAA,CAAC,uEAAuE;YAC3F,IAAI,CAAC,SAAS;gBAAE,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,CAAA;QACzC,CAAC,EACD,CAAC,CAAC,EAAE,EAAE;YACJ,GAAG,CAAC,EAAE,CAAC,IAAA,WAAC,EAAA,KAAK,CAAC,eAAe,EAAE,CAAC,eAAuB,GAAG,EAAE,GAAG,EAAE,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,CAAA;YAC/E,aAAa,CAAC,CAAC,CAAC,CAAA;YAChB,IAAI,CAAC,SAAS;gBAAE,GAAG,CAAC,MAAM,CAAC,KAAK,EAAE,KAAK,CAAC,CAAA;QAC1C,CAAC,CACF,CAAA;QACD,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,CAAA;IACf,CAAC;IAED,SAAS,WAAW;QAClB,GAAG,CAAC,MAAM,CACR,IAAA,uBAAgB,EAAC,GAAG,EAAE,CAAC,EAAE,OAAO,CAAC,EACjC,GAAG,EAAE,CAAC,gBAAgB,CAAC,CAAC,CAAC,EACzB,GAAG,EAAE,CAAC,aAAa,CAAC,CAAC,CAAC,CACvB,CAAA;IACH,CAAC;IAED,SAAS,aAAa,CAAC,MAAY;QACjC,MAAM,IAAI,GAAG,IAAA,WAAC,EAAA,GAAG,MAAM,SAAS,CAAA;QAChC,GAAG,CAAC,MAAM,CAAC,eAAC,CAAC,OAAO,EAAE,IAAA,WAAC,EAAA,GAAG,eAAC,CAAC,OAAO,eAAe,IAAI,MAAM,eAAC,CAAC,OAAO,WAAW,IAAI,GAAG,CAAC,CAAA,CAAC,cAAc;QACvG,GAAG,CAAC,MAAM,CAAC,eAAC,CAAC,MAAM,EAAE,IAAA,WAAC,EAAA,GAAG,eAAC,CAAC,OAAO,SAAS,CAAC,CAAA;IAC9C,CAAC;IAED,SAAS,gBAAgB,CAAC,MAAY;;QACpC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,WAAW;YAAE,OAAM;QAChC,MAAM,YAAY,GAAG,MAAA,GAAG,aAAH,GAAG,uBAAH,GAAG,CAAE,QAAQ,0CAAE,SAAS,CAAA;QAC7C,gBAAgB;QAChB,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,EAAE;YACrB,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;gBAC9C,IAAI,YAAY,CAAC,KAAK,KAAK,SAAS,EAAE;oBACpC,EAAE,CAAC,KAAK,GAAG,qBAAc,CAAC,KAAK,CAAC,GAAG,EAAE,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;iBACnE;aACF;iBAAM;gBACL,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAA,WAAC,EAAA,GAAG,MAAM,kBAAkB,CAAC,CAAA;gBAC5D,EAAE,CAAC,KAAK,GAAG,qBAAc,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE,cAAI,CAAC,CAAA;aAC5D;SACF;QACD,IAAI,EAAE,CAAC,KAAK,KAAK,IAAI,EAAE;YACrB,IAAI,YAAY,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE;gBAC9C,IAAI,YAAY,CAAC,KAAK,KAAK,SAAS,EAAE;oBACpC,EAAE,CAAC,KAAK,GAAG,qBAAc,CAAC,KAAK,CAAC,GAAG,EAAE,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,CAAA;iBACnE;aACF;iBAAM;gBACL,MAAM,KAAK,GAAG,GAAG,CAAC,GAAG,CAAC,OAAO,EAAE,IAAA,WAAC,EAAA,GAAG,MAAM,kBAAkB,CAAC,CAAA;gBAC5D,EAAE,CAAC,KAAK,GAAG,qBAAc,CAAC,KAAK,CAAC,GAAG,EAAE,KAAK,EAAE,EAAE,CAAC,KAAK,EAAE,cAAI,CAAC,CAAA;aAC5D;SACF;IACH,CAAC;AACH,CAAC;AAhED,0BAgEC;AAED,kBAAe,GAAG,CAAA"}