{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTouchRippleUtilityClass(slot) {\n  return generateUtilityClass('MuiTouchRipple', slot);\n}\nconst touchRippleClasses = generateUtilityClasses('MuiTouchRipple', ['root', 'ripple', 'rippleVisible', 'ripplePulsate', 'child', 'childLeaving', 'childPulsate']);\nexport default touchRippleClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getTouchRippleUtilityClass", "slot", "touchRippleClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/ButtonBase/touchRippleClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getTouchRippleUtilityClass(slot) {\n  return generateUtilityClass('MuiTouchRipple', slot);\n}\nconst touchRippleClasses = generateUtilityClasses('MuiTouchRipple', ['root', 'ripple', 'rippleVisible', 'ripplePulsate', 'child', 'childLeaving', 'childPulsate']);\nexport default touchRippleClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,0BAA0BA,CAACC,IAAI,EAAE;EAC/C,OAAOF,oBAAoB,CAAC,gBAAgB,EAAEE,IAAI,CAAC;AACrD;AACA,MAAMC,kBAAkB,GAAGJ,sBAAsB,CAAC,gBAAgB,EAAE,CAAC,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;AAClK,eAAeI,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}