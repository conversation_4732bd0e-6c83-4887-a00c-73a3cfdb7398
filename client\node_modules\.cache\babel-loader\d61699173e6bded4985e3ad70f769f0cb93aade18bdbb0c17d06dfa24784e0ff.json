{"ast": null, "code": "import isPlainObject from './isPlainObject';\nimport warning from './warning';\nexport default function verifyPlainObject(value, displayName, methodName) {\n  if (!isPlainObject(value)) {\n    warning(\"\".concat(methodName, \"() in \").concat(displayName, \" must return a plain object. Instead received \").concat(value, \".\"));\n  }\n}", "map": {"version": 3, "names": ["isPlainObject", "warning", "verifyPlainObject", "value", "displayName", "methodName", "concat"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/react-redux/es/utils/verifyPlainObject.js"], "sourcesContent": ["import isPlainObject from './isPlainObject';\nimport warning from './warning';\nexport default function verifyPlainObject(value, displayName, methodName) {\n  if (!isPlainObject(value)) {\n    warning(`${methodName}() in ${displayName} must return a plain object. Instead received ${value}.`);\n  }\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,OAAO,MAAM,WAAW;AAC/B,eAAe,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,WAAW,EAAEC,UAAU,EAAE;EACxE,IAAI,CAACL,aAAa,CAACG,KAAK,CAAC,EAAE;IACzBF,OAAO,IAAAK,MAAA,CAAID,UAAU,YAAAC,MAAA,CAASF,WAAW,oDAAAE,MAAA,CAAiDH,KAAK,MAAG,CAAC;EACrG;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}