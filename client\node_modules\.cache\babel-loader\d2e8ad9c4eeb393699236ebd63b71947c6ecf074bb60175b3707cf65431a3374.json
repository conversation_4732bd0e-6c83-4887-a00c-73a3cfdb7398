{"ast": null, "code": "export { default } from './useId';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/useId/index.js"], "sourcesContent": ["export { default } from './useId';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}