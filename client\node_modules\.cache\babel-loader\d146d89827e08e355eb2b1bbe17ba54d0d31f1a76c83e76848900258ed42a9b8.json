{"ast": null, "code": "import React from\"react\";import{NoProfile}from\"../assets\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const Modal=_ref=>{let{onClose,followers}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"fixed inset-0 z-50 flex items-center justify-center m-10\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"absolute inset-0 bg-gray-500 bg-opacity-50 backdrop-blur-sm\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative bg-white w-96 rounded-lg shadow-xl\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"absolute top-1 right-1 p-2 text-gray-500 cursor-pointer\",style:{fontSize:'24px',backgroundColor:'red',borderRadius:'50%',color:'white',width:'30px',height:'30px',textAlign:'center',lineHeight:'10px',transform:'translate(50%, -50%)'},onClick:onClose,children:\"\\xD7\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"p-6\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-semibold mb-4\",children:\"Followers\"}),/*#__PURE__*/_jsx(\"ul\",{children:followers.map(follower=>/*#__PURE__*/_jsxs(\"li\",{className:\"flex items-center mb-2\",children:[/*#__PURE__*/_jsx(\"img\",{src:follower.profileUrl||NoProfile,alt:\"\".concat(follower.firstName,\" \").concat(follower.lastName),className:\"w-10 h-10 object-cover rounded-full mr-2\"}),/*#__PURE__*/_jsx(\"span\",{children:\"\".concat(follower.firstName,\" \").concat(follower.lastName)})]},follower.id))})]})]})]});};export default Modal;", "map": {"version": 3, "names": ["React", "NoProfile", "jsx", "_jsx", "jsxs", "_jsxs", "Modal", "_ref", "onClose", "followers", "className", "children", "style", "fontSize", "backgroundColor", "borderRadius", "color", "width", "height", "textAlign", "lineHeight", "transform", "onClick", "map", "follower", "src", "profileUrl", "alt", "concat", "firstName", "lastName", "id"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/components/FollowerModel.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { NoProfile } from \"../assets\";\r\n\r\nconst Modal = ({ onClose, followers }) => {\r\n  return (\r\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center m-10\">\r\n      <div className=\"absolute inset-0 bg-gray-500 bg-opacity-50 backdrop-blur-sm\"></div>\r\n      <div className=\"relative bg-white w-96 rounded-lg shadow-xl\">\r\n        <span\r\n          className=\"absolute top-1 right-1 p-2 text-gray-500 cursor-pointer\"\r\n          style={{ fontSize: '24px', backgroundColor: 'red', borderRadius: '50%', color: 'white', width: '30px', height: '30px', textAlign: 'center', lineHeight: '10px', transform: 'translate(50%, -50%)' }}\r\n          onClick={onClose}\r\n        >\r\n          &times;\r\n        </span>\r\n        <div className=\"p-6\">\r\n          <h2 className=\"text-2xl font-semibold mb-4\">Followers</h2>\r\n          <ul>\r\n            {followers.map((follower) => (\r\n              <li key={follower.id} className=\"flex items-center mb-2\">\r\n                <img\r\n                  src={follower.profileUrl || NoProfile}\r\n                  alt={`${follower.firstName} ${follower.lastName}`}\r\n                  className=\"w-10 h-10 object-cover rounded-full mr-2\"\r\n                />\r\n                <span>{`${follower.firstName} ${follower.lastName}`}</span>\r\n              </li>\r\n            ))}\r\n          </ul>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Modal;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,SAAS,KAAQ,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAEtC,KAAM,CAAAC,KAAK,CAAGC,IAAA,EAA4B,IAA3B,CAAEC,OAAO,CAAEC,SAAU,CAAC,CAAAF,IAAA,CACnC,mBACEF,KAAA,QAAKK,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvER,IAAA,QAAKO,SAAS,CAAC,6DAA6D,CAAM,CAAC,cACnFL,KAAA,QAAKK,SAAS,CAAC,6CAA6C,CAAAC,QAAA,eAC1DR,IAAA,SACEO,SAAS,CAAC,yDAAyD,CACnEE,KAAK,CAAE,CAAEC,QAAQ,CAAE,MAAM,CAAEC,eAAe,CAAE,KAAK,CAAEC,YAAY,CAAE,KAAK,CAAEC,KAAK,CAAE,OAAO,CAAEC,KAAK,CAAE,MAAM,CAAEC,MAAM,CAAE,MAAM,CAAEC,SAAS,CAAE,QAAQ,CAAEC,UAAU,CAAE,MAAM,CAAEC,SAAS,CAAE,sBAAuB,CAAE,CACpMC,OAAO,CAAEd,OAAQ,CAAAG,QAAA,CAClB,MAED,CAAM,CAAC,cACPN,KAAA,QAAKK,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBR,IAAA,OAAIO,SAAS,CAAC,6BAA6B,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,cAC1DR,IAAA,OAAAQ,QAAA,CACGF,SAAS,CAACc,GAAG,CAAEC,QAAQ,eACtBnB,KAAA,OAAsBK,SAAS,CAAC,wBAAwB,CAAAC,QAAA,eACtDR,IAAA,QACEsB,GAAG,CAAED,QAAQ,CAACE,UAAU,EAAIzB,SAAU,CACtC0B,GAAG,IAAAC,MAAA,CAAKJ,QAAQ,CAACK,SAAS,MAAAD,MAAA,CAAIJ,QAAQ,CAACM,QAAQ,CAAG,CAClDpB,SAAS,CAAC,0CAA0C,CACrD,CAAC,cACFP,IAAA,SAAAQ,QAAA,IAAAiB,MAAA,CAAUJ,QAAQ,CAACK,SAAS,MAAAD,MAAA,CAAIJ,QAAQ,CAACM,QAAQ,EAAS,CAAC,GANpDN,QAAQ,CAACO,EAOd,CACL,CAAC,CACA,CAAC,EACF,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAzB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}