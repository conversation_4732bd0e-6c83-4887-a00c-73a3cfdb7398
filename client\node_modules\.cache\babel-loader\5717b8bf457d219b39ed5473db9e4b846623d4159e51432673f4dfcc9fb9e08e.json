{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFabUtilityClass(slot) {\n  return generateUtilityClass('MuiFab', slot);\n}\nconst fabClasses = generateUtilityClasses('MuiFab', ['root', 'primary', 'secondary', 'extended', 'circular', 'focusVisible', 'disabled', 'colorInherit', 'sizeSmall', 'sizeMedium', 'sizeLarge', 'info', 'error', 'warning', 'success']);\nexport default fabClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getFabUtilityClass", "slot", "fabClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/Fab/fabClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getFabUtilityClass(slot) {\n  return generateUtilityClass('MuiFab', slot);\n}\nconst fabClasses = generateUtilityClasses('MuiFab', ['root', 'primary', 'secondary', 'extended', 'circular', 'focusVisible', 'disabled', 'colorInherit', 'sizeSmall', 'sizeMedium', 'sizeLarge', 'info', 'error', 'warning', 'success']);\nexport default fabClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,kBAAkBA,CAACC,IAAI,EAAE;EACvC,OAAOF,oBAAoB,CAAC,QAAQ,EAAEE,IAAI,CAAC;AAC7C;AACA,MAAMC,UAAU,GAAGJ,sBAAsB,CAAC,QAAQ,EAAE,CAAC,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,cAAc,EAAE,UAAU,EAAE,cAAc,EAAE,WAAW,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC;AACxO,eAAeI,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}