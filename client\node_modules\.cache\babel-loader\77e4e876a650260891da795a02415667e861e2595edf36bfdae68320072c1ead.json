{"ast": null, "code": "/**\n * If `componentProps` is a function, calls it with the provided `ownerState`.\n * Otherwise, just returns `componentProps`.\n */\nexport function resolveComponentProps(componentProps, ownerState, slotState) {\n  if (typeof componentProps === 'function') {\n    return componentProps(ownerState, slotState);\n  }\n  return componentProps;\n}", "map": {"version": 3, "names": ["resolveComponentProps", "componentProps", "ownerState", "slotState"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/utils/resolveComponentProps.js"], "sourcesContent": ["/**\n * If `componentProps` is a function, calls it with the provided `ownerState`.\n * Otherwise, just returns `componentProps`.\n */\nexport function resolveComponentProps(componentProps, ownerState, slotState) {\n  if (typeof componentProps === 'function') {\n    return componentProps(ownerState, slotState);\n  }\n  return componentProps;\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA,OAAO,SAASA,qBAAqBA,CAACC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAE;EAC3E,IAAI,OAAOF,cAAc,KAAK,UAAU,EAAE;IACxC,OAAOA,cAAc,CAACC,UAAU,EAAEC,SAAS,CAAC;EAC9C;EACA,OAAOF,cAAc;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}