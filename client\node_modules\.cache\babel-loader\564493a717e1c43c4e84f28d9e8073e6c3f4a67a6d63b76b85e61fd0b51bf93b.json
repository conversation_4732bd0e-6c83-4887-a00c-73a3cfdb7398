{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _createClass = function () {\n  function defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  return function (Constructor, protoProps, staticProps) {\n    if (protoProps) defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) defineProperties(Constructor, staticProps);\n    return Constructor;\n  };\n}();\nvar _react = require('react');\nvar React = _interopRequireWildcard(_react);\nvar _defaultComponentDecorator = require('../decorators/defaultComponentDecorator');\nvar _defaultComponentDecorator2 = _interopRequireDefault(_defaultComponentDecorator);\nvar _defaultHrefDecorator = require('../decorators/defaultHrefDecorator');\nvar _defaultHrefDecorator2 = _interopRequireDefault(_defaultHrefDecorator);\nvar _defaultMatchDecorator = require('../decorators/defaultMatchDecorator');\nvar _defaultMatchDecorator2 = _interopRequireDefault(_defaultMatchDecorator);\nvar _defaultTextDecorator = require('../decorators/defaultTextDecorator');\nvar _defaultTextDecorator2 = _interopRequireDefault(_defaultTextDecorator);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nfunction _interopRequireWildcard(obj) {\n  if (obj && obj.__esModule) {\n    return obj;\n  } else {\n    var newObj = {};\n    if (obj != null) {\n      for (var key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key];\n      }\n    }\n    newObj.default = obj;\n    return newObj;\n  }\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (!self) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass);\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      enumerable: false,\n      writable: true,\n      configurable: true\n    }\n  });\n  if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass;\n}\nvar Linkify = function (_React$Component) {\n  _inherits(Linkify, _React$Component);\n  function Linkify() {\n    _classCallCheck(this, Linkify);\n    return _possibleConstructorReturn(this, (Linkify.__proto__ || Object.getPrototypeOf(Linkify)).apply(this, arguments));\n  }\n  _createClass(Linkify, [{\n    key: 'parseString',\n    value: function parseString(string) {\n      var _this2 = this;\n      if (string === '') {\n        return string;\n      }\n      var matches = this.props.matchDecorator(string);\n      if (!matches) {\n        return string;\n      }\n      var elements = [];\n      var lastIndex = 0;\n      matches.forEach(function (match, i) {\n        // Push preceding text if there is any\n        if (match.index > lastIndex) {\n          elements.push(string.substring(lastIndex, match.index));\n        }\n        var decoratedHref = _this2.props.hrefDecorator(match.url);\n        var decoratedText = _this2.props.textDecorator(match.text);\n        var decoratedComponent = _this2.props.componentDecorator(decoratedHref, decoratedText, i);\n        elements.push(decoratedComponent);\n        lastIndex = match.lastIndex;\n      });\n\n      // Push remaining text if there is any\n      if (string.length > lastIndex) {\n        elements.push(string.substring(lastIndex));\n      }\n      return elements.length === 1 ? elements[0] : elements;\n    }\n  }, {\n    key: 'parse',\n    value: function parse(children) {\n      var _this3 = this;\n      var key = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n      if (typeof children === 'string') {\n        return this.parseString(children);\n      } else if (React.isValidElement(children) && children.type !== 'a' && children.type !== 'button') {\n        return React.cloneElement(children, {\n          key: key\n        }, this.parse(children.props.children));\n      } else if (Array.isArray(children)) {\n        return children.map(function (child, i) {\n          return _this3.parse(child, i);\n        });\n      }\n      return children;\n    }\n  }, {\n    key: 'render',\n    value: function render() {\n      return React.createElement(React.Fragment, null, this.parse(this.props.children));\n    }\n  }]);\n  return Linkify;\n}(React.Component);\nLinkify.defaultProps = {\n  componentDecorator: _defaultComponentDecorator2.default,\n  hrefDecorator: _defaultHrefDecorator2.default,\n  matchDecorator: _defaultMatchDecorator2.default,\n  textDecorator: _defaultTextDecorator2.default\n};\nexports.default = Linkify;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_createClass", "defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "key", "<PERSON><PERSON><PERSON><PERSON>", "protoProps", "staticProps", "prototype", "_react", "require", "React", "_interopRequireWildcard", "_defaultComponentDecorator", "_defaultComponentDecorator2", "_interopRequireDefault", "_defaultHrefDecorator", "_defaultHrefDecorator2", "_defaultMatchDecorator", "_defaultMatchDecorator2", "_defaultTextDecorator", "_defaultTextDecorator2", "obj", "__esModule", "default", "newObj", "hasOwnProperty", "call", "_classCallCheck", "instance", "TypeError", "_possibleConstructorReturn", "self", "ReferenceError", "_inherits", "subClass", "superClass", "create", "constructor", "setPrototypeOf", "__proto__", "Linkify", "_React$Component", "getPrototypeOf", "apply", "arguments", "parseString", "string", "_this2", "matches", "matchDecorator", "elements", "lastIndex", "for<PERSON>ach", "match", "index", "push", "substring", "decorated<PERSON><PERSON>f", "hrefDecorator", "url", "decoratedText", "textDecorator", "text", "decoratedComponent", "componentDecorator", "parse", "children", "_this3", "undefined", "isValidElement", "type", "cloneElement", "Array", "isArray", "map", "child", "render", "createElement", "Fragment", "Component", "defaultProps"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/react-linkify/dist/components/Linkify.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _createClass = function () { function defineProperties(target, props) { for (var i = 0; i < props.length; i++) { var descriptor = props[i]; descriptor.enumerable = descriptor.enumerable || false; descriptor.configurable = true; if (\"value\" in descriptor) descriptor.writable = true; Object.defineProperty(target, descriptor.key, descriptor); } } return function (Constructor, protoProps, staticProps) { if (protoProps) defineProperties(Constructor.prototype, protoProps); if (staticProps) defineProperties(Constructor, staticProps); return Constructor; }; }();\n\nvar _react = require('react');\n\nvar React = _interopRequireWildcard(_react);\n\nvar _defaultComponentDecorator = require('../decorators/defaultComponentDecorator');\n\nvar _defaultComponentDecorator2 = _interopRequireDefault(_defaultComponentDecorator);\n\nvar _defaultHrefDecorator = require('../decorators/defaultHrefDecorator');\n\nvar _defaultHrefDecorator2 = _interopRequireDefault(_defaultHrefDecorator);\n\nvar _defaultMatchDecorator = require('../decorators/defaultMatchDecorator');\n\nvar _defaultMatchDecorator2 = _interopRequireDefault(_defaultMatchDecorator);\n\nvar _defaultTextDecorator = require('../decorators/defaultTextDecorator');\n\nvar _defaultTextDecorator2 = _interopRequireDefault(_defaultTextDecorator);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key]; } } newObj.default = obj; return newObj; } }\n\nfunction _classCallCheck(instance, Constructor) { if (!(instance instanceof Constructor)) { throw new TypeError(\"Cannot call a class as a function\"); } }\n\nfunction _possibleConstructorReturn(self, call) { if (!self) { throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\"); } return call && (typeof call === \"object\" || typeof call === \"function\") ? call : self; }\n\nfunction _inherits(subClass, superClass) { if (typeof superClass !== \"function\" && superClass !== null) { throw new TypeError(\"Super expression must either be null or a function, not \" + typeof superClass); } subClass.prototype = Object.create(superClass && superClass.prototype, { constructor: { value: subClass, enumerable: false, writable: true, configurable: true } }); if (superClass) Object.setPrototypeOf ? Object.setPrototypeOf(subClass, superClass) : subClass.__proto__ = superClass; }\n\nvar Linkify = function (_React$Component) {\n  _inherits(Linkify, _React$Component);\n\n  function Linkify() {\n    _classCallCheck(this, Linkify);\n\n    return _possibleConstructorReturn(this, (Linkify.__proto__ || Object.getPrototypeOf(Linkify)).apply(this, arguments));\n  }\n\n  _createClass(Linkify, [{\n    key: 'parseString',\n    value: function parseString(string) {\n      var _this2 = this;\n\n      if (string === '') {\n        return string;\n      }\n\n      var matches = this.props.matchDecorator(string);\n      if (!matches) {\n        return string;\n      }\n\n      var elements = [];\n      var lastIndex = 0;\n      matches.forEach(function (match, i) {\n        // Push preceding text if there is any\n        if (match.index > lastIndex) {\n          elements.push(string.substring(lastIndex, match.index));\n        }\n\n        var decoratedHref = _this2.props.hrefDecorator(match.url);\n        var decoratedText = _this2.props.textDecorator(match.text);\n        var decoratedComponent = _this2.props.componentDecorator(decoratedHref, decoratedText, i);\n        elements.push(decoratedComponent);\n\n        lastIndex = match.lastIndex;\n      });\n\n      // Push remaining text if there is any\n      if (string.length > lastIndex) {\n        elements.push(string.substring(lastIndex));\n      }\n\n      return elements.length === 1 ? elements[0] : elements;\n    }\n  }, {\n    key: 'parse',\n    value: function parse(children) {\n      var _this3 = this;\n\n      var key = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n\n      if (typeof children === 'string') {\n        return this.parseString(children);\n      } else if (React.isValidElement(children) && children.type !== 'a' && children.type !== 'button') {\n        return React.cloneElement(children, { key: key }, this.parse(children.props.children));\n      } else if (Array.isArray(children)) {\n        return children.map(function (child, i) {\n          return _this3.parse(child, i);\n        });\n      }\n\n      return children;\n    }\n  }, {\n    key: 'render',\n    value: function render() {\n      return React.createElement(\n        React.Fragment,\n        null,\n        this.parse(this.props.children)\n      );\n    }\n  }]);\n\n  return Linkify;\n}(React.Component);\n\nLinkify.defaultProps = {\n  componentDecorator: _defaultComponentDecorator2.default,\n  hrefDecorator: _defaultHrefDecorator2.default,\n  matchDecorator: _defaultMatchDecorator2.default,\n  textDecorator: _defaultTextDecorator2.default\n};\nexports.default = Linkify;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,IAAIC,YAAY,GAAG,YAAY;EAAE,SAASC,gBAAgBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IAAE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MAAE,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MAAEE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MAAED,UAAU,CAACE,YAAY,GAAG,IAAI;MAAE,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MAAEb,MAAM,CAACC,cAAc,CAACK,MAAM,EAAEI,UAAU,CAACI,GAAG,EAAEJ,UAAU,CAAC;IAAE;EAAE;EAAE,OAAO,UAAUK,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAE;IAAE,IAAID,UAAU,EAAEX,gBAAgB,CAACU,WAAW,CAACG,SAAS,EAAEF,UAAU,CAAC;IAAE,IAAIC,WAAW,EAAEZ,gBAAgB,CAACU,WAAW,EAAEE,WAAW,CAAC;IAAE,OAAOF,WAAW;EAAE,CAAC;AAAE,CAAC,CAAC,CAAC;AAEnjB,IAAII,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE7B,IAAIC,KAAK,GAAGC,uBAAuB,CAACH,MAAM,CAAC;AAE3C,IAAII,0BAA0B,GAAGH,OAAO,CAAC,yCAAyC,CAAC;AAEnF,IAAII,2BAA2B,GAAGC,sBAAsB,CAACF,0BAA0B,CAAC;AAEpF,IAAIG,qBAAqB,GAAGN,OAAO,CAAC,oCAAoC,CAAC;AAEzE,IAAIO,sBAAsB,GAAGF,sBAAsB,CAACC,qBAAqB,CAAC;AAE1E,IAAIE,sBAAsB,GAAGR,OAAO,CAAC,qCAAqC,CAAC;AAE3E,IAAIS,uBAAuB,GAAGJ,sBAAsB,CAACG,sBAAsB,CAAC;AAE5E,IAAIE,qBAAqB,GAAGV,OAAO,CAAC,oCAAoC,CAAC;AAEzE,IAAIW,sBAAsB,GAAGN,sBAAsB,CAACK,qBAAqB,CAAC;AAE1E,SAASL,sBAAsBA,CAACO,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,SAASV,uBAAuBA,CAACU,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE,CAAC,MAAM;IAAE,IAAIG,MAAM,GAAG,CAAC,CAAC;IAAE,IAAIH,GAAG,IAAI,IAAI,EAAE;MAAE,KAAK,IAAIlB,GAAG,IAAIkB,GAAG,EAAE;QAAE,IAAIhC,MAAM,CAACkB,SAAS,CAACkB,cAAc,CAACC,IAAI,CAACL,GAAG,EAAElB,GAAG,CAAC,EAAEqB,MAAM,CAACrB,GAAG,CAAC,GAAGkB,GAAG,CAAClB,GAAG,CAAC;MAAE;IAAE;IAAEqB,MAAM,CAACD,OAAO,GAAGF,GAAG;IAAE,OAAOG,MAAM;EAAE;AAAE;AAE5Q,SAASG,eAAeA,CAACC,QAAQ,EAAExB,WAAW,EAAE;EAAE,IAAI,EAAEwB,QAAQ,YAAYxB,WAAW,CAAC,EAAE;IAAE,MAAM,IAAIyB,SAAS,CAAC,mCAAmC,CAAC;EAAE;AAAE;AAExJ,SAASC,0BAA0BA,CAACC,IAAI,EAAEL,IAAI,EAAE;EAAE,IAAI,CAACK,IAAI,EAAE;IAAE,MAAM,IAAIC,cAAc,CAAC,2DAA2D,CAAC;EAAE;EAAE,OAAON,IAAI,KAAK,OAAOA,IAAI,KAAK,QAAQ,IAAI,OAAOA,IAAI,KAAK,UAAU,CAAC,GAAGA,IAAI,GAAGK,IAAI;AAAE;AAE/O,SAASE,SAASA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAAE,IAAI,OAAOA,UAAU,KAAK,UAAU,IAAIA,UAAU,KAAK,IAAI,EAAE;IAAE,MAAM,IAAIN,SAAS,CAAC,0DAA0D,GAAG,OAAOM,UAAU,CAAC;EAAE;EAAED,QAAQ,CAAC3B,SAAS,GAAGlB,MAAM,CAAC+C,MAAM,CAACD,UAAU,IAAIA,UAAU,CAAC5B,SAAS,EAAE;IAAE8B,WAAW,EAAE;MAAE7C,KAAK,EAAE0C,QAAQ;MAAElC,UAAU,EAAE,KAAK;MAAEE,QAAQ,EAAE,IAAI;MAAED,YAAY,EAAE;IAAK;EAAE,CAAC,CAAC;EAAE,IAAIkC,UAAU,EAAE9C,MAAM,CAACiD,cAAc,GAAGjD,MAAM,CAACiD,cAAc,CAACJ,QAAQ,EAAEC,UAAU,CAAC,GAAGD,QAAQ,CAACK,SAAS,GAAGJ,UAAU;AAAE;AAE7e,IAAIK,OAAO,GAAG,UAAUC,gBAAgB,EAAE;EACxCR,SAAS,CAACO,OAAO,EAAEC,gBAAgB,CAAC;EAEpC,SAASD,OAAOA,CAAA,EAAG;IACjBb,eAAe,CAAC,IAAI,EAAEa,OAAO,CAAC;IAE9B,OAAOV,0BAA0B,CAAC,IAAI,EAAE,CAACU,OAAO,CAACD,SAAS,IAAIlD,MAAM,CAACqD,cAAc,CAACF,OAAO,CAAC,EAAEG,KAAK,CAAC,IAAI,EAAEC,SAAS,CAAC,CAAC;EACvH;EAEAnD,YAAY,CAAC+C,OAAO,EAAE,CAAC;IACrBrC,GAAG,EAAE,aAAa;IAClBX,KAAK,EAAE,SAASqD,WAAWA,CAACC,MAAM,EAAE;MAClC,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAID,MAAM,KAAK,EAAE,EAAE;QACjB,OAAOA,MAAM;MACf;MAEA,IAAIE,OAAO,GAAG,IAAI,CAACpD,KAAK,CAACqD,cAAc,CAACH,MAAM,CAAC;MAC/C,IAAI,CAACE,OAAO,EAAE;QACZ,OAAOF,MAAM;MACf;MAEA,IAAII,QAAQ,GAAG,EAAE;MACjB,IAAIC,SAAS,GAAG,CAAC;MACjBH,OAAO,CAACI,OAAO,CAAC,UAAUC,KAAK,EAAExD,CAAC,EAAE;QAClC;QACA,IAAIwD,KAAK,CAACC,KAAK,GAAGH,SAAS,EAAE;UAC3BD,QAAQ,CAACK,IAAI,CAACT,MAAM,CAACU,SAAS,CAACL,SAAS,EAAEE,KAAK,CAACC,KAAK,CAAC,CAAC;QACzD;QAEA,IAAIG,aAAa,GAAGV,MAAM,CAACnD,KAAK,CAAC8D,aAAa,CAACL,KAAK,CAACM,GAAG,CAAC;QACzD,IAAIC,aAAa,GAAGb,MAAM,CAACnD,KAAK,CAACiE,aAAa,CAACR,KAAK,CAACS,IAAI,CAAC;QAC1D,IAAIC,kBAAkB,GAAGhB,MAAM,CAACnD,KAAK,CAACoE,kBAAkB,CAACP,aAAa,EAAEG,aAAa,EAAE/D,CAAC,CAAC;QACzFqD,QAAQ,CAACK,IAAI,CAACQ,kBAAkB,CAAC;QAEjCZ,SAAS,GAAGE,KAAK,CAACF,SAAS;MAC7B,CAAC,CAAC;;MAEF;MACA,IAAIL,MAAM,CAAChD,MAAM,GAAGqD,SAAS,EAAE;QAC7BD,QAAQ,CAACK,IAAI,CAACT,MAAM,CAACU,SAAS,CAACL,SAAS,CAAC,CAAC;MAC5C;MAEA,OAAOD,QAAQ,CAACpD,MAAM,KAAK,CAAC,GAAGoD,QAAQ,CAAC,CAAC,CAAC,GAAGA,QAAQ;IACvD;EACF,CAAC,EAAE;IACD/C,GAAG,EAAE,OAAO;IACZX,KAAK,EAAE,SAASyE,KAAKA,CAACC,QAAQ,EAAE;MAC9B,IAAIC,MAAM,GAAG,IAAI;MAEjB,IAAIhE,GAAG,GAAGyC,SAAS,CAAC9C,MAAM,GAAG,CAAC,IAAI8C,SAAS,CAAC,CAAC,CAAC,KAAKwB,SAAS,GAAGxB,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC;MAE/E,IAAI,OAAOsB,QAAQ,KAAK,QAAQ,EAAE;QAChC,OAAO,IAAI,CAACrB,WAAW,CAACqB,QAAQ,CAAC;MACnC,CAAC,MAAM,IAAIxD,KAAK,CAAC2D,cAAc,CAACH,QAAQ,CAAC,IAAIA,QAAQ,CAACI,IAAI,KAAK,GAAG,IAAIJ,QAAQ,CAACI,IAAI,KAAK,QAAQ,EAAE;QAChG,OAAO5D,KAAK,CAAC6D,YAAY,CAACL,QAAQ,EAAE;UAAE/D,GAAG,EAAEA;QAAI,CAAC,EAAE,IAAI,CAAC8D,KAAK,CAACC,QAAQ,CAACtE,KAAK,CAACsE,QAAQ,CAAC,CAAC;MACxF,CAAC,MAAM,IAAIM,KAAK,CAACC,OAAO,CAACP,QAAQ,CAAC,EAAE;QAClC,OAAOA,QAAQ,CAACQ,GAAG,CAAC,UAAUC,KAAK,EAAE9E,CAAC,EAAE;UACtC,OAAOsE,MAAM,CAACF,KAAK,CAACU,KAAK,EAAE9E,CAAC,CAAC;QAC/B,CAAC,CAAC;MACJ;MAEA,OAAOqE,QAAQ;IACjB;EACF,CAAC,EAAE;IACD/D,GAAG,EAAE,QAAQ;IACbX,KAAK,EAAE,SAASoF,MAAMA,CAAA,EAAG;MACvB,OAAOlE,KAAK,CAACmE,aAAa,CACxBnE,KAAK,CAACoE,QAAQ,EACd,IAAI,EACJ,IAAI,CAACb,KAAK,CAAC,IAAI,CAACrE,KAAK,CAACsE,QAAQ,CAChC,CAAC;IACH;EACF,CAAC,CAAC,CAAC;EAEH,OAAO1B,OAAO;AAChB,CAAC,CAAC9B,KAAK,CAACqE,SAAS,CAAC;AAElBvC,OAAO,CAACwC,YAAY,GAAG;EACrBhB,kBAAkB,EAAEnD,2BAA2B,CAACU,OAAO;EACvDmC,aAAa,EAAE1C,sBAAsB,CAACO,OAAO;EAC7C0B,cAAc,EAAE/B,uBAAuB,CAACK,OAAO;EAC/CsC,aAAa,EAAEzC,sBAAsB,CAACG;AACxC,CAAC;AACDhC,OAAO,CAACgC,OAAO,GAAGiB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}