{"ast": null, "code": "'use client';\n\nexport * from './useTabs';\nexport * from './useTabs.types';\nexport * from './TabsProvider';", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/useTabs/index.js"], "sourcesContent": ["'use client';\n\nexport * from './useTabs';\nexport * from './useTabs.types';\nexport * from './TabsProvider';"], "mappings": "AAAA,YAAY;;AAEZ,cAAc,WAAW;AACzB,cAAc,iBAAiB;AAC/B,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}