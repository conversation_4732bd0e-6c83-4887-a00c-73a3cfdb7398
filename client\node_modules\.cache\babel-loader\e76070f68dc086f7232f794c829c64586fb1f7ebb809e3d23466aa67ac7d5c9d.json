{"ast": null, "code": "'use client';\n\nexport { default } from './SnackbarContent';\nexport { default as snackbarContentClasses } from './snackbarContentClasses';\nexport * from './snackbarContentClasses';", "map": {"version": 3, "names": ["default", "snackbarContentClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/SnackbarContent/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './SnackbarContent';\nexport { default as snackbarContentClasses } from './snackbarContentClasses';\nexport * from './snackbarContentClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,mBAAmB;AAC3C,SAASA,OAAO,IAAIC,sBAAsB,QAAQ,0BAA0B;AAC5E,cAAc,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}