{"ast": null, "code": "'use client';\n\nexport { default } from './Chip';\nexport { default as chipClasses } from './chipClasses';\nexport * from './chipClasses';", "map": {"version": 3, "names": ["default", "chipClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/Chip/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Chip';\nexport { default as chipClasses } from './chipClasses';\nexport * from './chipClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,QAAQ;AAChC,SAASA,OAAO,IAAIC,WAAW,QAAQ,eAAe;AACtD,cAAc,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}