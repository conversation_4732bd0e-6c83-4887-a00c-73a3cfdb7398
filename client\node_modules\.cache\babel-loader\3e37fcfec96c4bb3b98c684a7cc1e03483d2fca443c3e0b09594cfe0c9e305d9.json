{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getMenuItemUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuItem', slot);\n}\nconst menuItemClasses = generateUtilityClasses('MuiMenuItem', ['root', 'focusVisible', 'dense', 'disabled', 'divider', 'gutters', 'selected']);\nexport default menuItemClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getMenuItemUtilityClass", "slot", "menuItemClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/MenuItem/menuItemClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getMenuItemUtilityClass(slot) {\n  return generateUtilityClass('MuiMenuItem', slot);\n}\nconst menuItemClasses = generateUtilityClasses('MuiMenuItem', ['root', 'focusVisible', 'dense', 'disabled', 'divider', 'gutters', 'selected']);\nexport default menuItemClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOF,oBAAoB,CAAC,aAAa,EAAEE,IAAI,CAAC;AAClD;AACA,MAAMC,eAAe,GAAGJ,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;AAC9I,eAAeI,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}