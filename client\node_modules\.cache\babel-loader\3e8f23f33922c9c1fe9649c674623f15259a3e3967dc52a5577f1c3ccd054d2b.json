{"ast": null, "code": "'use client';\n\nexport { useInput } from './useInput';\nexport * from './useInput.types';", "map": {"version": 3, "names": ["useInput"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/useInput/index.js"], "sourcesContent": ["'use client';\n\nexport { useInput } from './useInput';\nexport * from './useInput.types';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,QAAQ,YAAY;AACrC,cAAc,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}