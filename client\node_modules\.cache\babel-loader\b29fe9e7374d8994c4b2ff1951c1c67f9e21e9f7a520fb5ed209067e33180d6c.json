{"ast": null, "code": "'use client';\n\nexport { useAutocomplete as default } from '@mui/base/useAutocomplete';\nexport * from '@mui/base/useAutocomplete';", "map": {"version": 3, "names": ["useAutocomplete", "default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/useAutocomplete/useAutocomplete.js"], "sourcesContent": ["'use client';\n\nexport { useAutocomplete as default } from '@mui/base/useAutocomplete';\nexport * from '@mui/base/useAutocomplete';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAe,IAAIC,OAAO,QAAQ,2BAA2B;AACtE,cAAc,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}