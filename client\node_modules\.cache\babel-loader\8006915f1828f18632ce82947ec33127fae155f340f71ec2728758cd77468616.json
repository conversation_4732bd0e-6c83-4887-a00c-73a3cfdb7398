{"ast": null, "code": "// track, thumb and active are derived from macOS 10.15.7\nconst scrollBar = {\n  track: '#2b2b2b',\n  thumb: '#6b6b6b',\n  active: '#959595'\n};\nexport default function darkScrollbar() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : scrollBar;\n  return {\n    scrollbarColor: \"\".concat(options.thumb, \" \").concat(options.track),\n    '&::-webkit-scrollbar, & *::-webkit-scrollbar': {\n      backgroundColor: options.track\n    },\n    '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {\n      borderRadius: 8,\n      backgroundColor: options.thumb,\n      minHeight: 24,\n      border: \"3px solid \".concat(options.track)\n    },\n    '&::-webkit-scrollbar-thumb:focus, & *::-webkit-scrollbar-thumb:focus': {\n      backgroundColor: options.active\n    },\n    '&::-webkit-scrollbar-thumb:active, & *::-webkit-scrollbar-thumb:active': {\n      backgroundColor: options.active\n    },\n    '&::-webkit-scrollbar-thumb:hover, & *::-webkit-scrollbar-thumb:hover': {\n      backgroundColor: options.active\n    },\n    '&::-webkit-scrollbar-corner, & *::-webkit-scrollbar-corner': {\n      backgroundColor: options.track\n    }\n  };\n}", "map": {"version": 3, "names": ["scrollBar", "track", "thumb", "active", "darkScrollbar", "options", "arguments", "length", "undefined", "scrollbarColor", "concat", "backgroundColor", "borderRadius", "minHeight", "border"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/darkScrollbar/index.js"], "sourcesContent": ["// track, thumb and active are derived from macOS 10.15.7\nconst scrollBar = {\n  track: '#2b2b2b',\n  thumb: '#6b6b6b',\n  active: '#959595'\n};\nexport default function darkScrollbar(options = scrollBar) {\n  return {\n    scrollbarColor: `${options.thumb} ${options.track}`,\n    '&::-webkit-scrollbar, & *::-webkit-scrollbar': {\n      backgroundColor: options.track\n    },\n    '&::-webkit-scrollbar-thumb, & *::-webkit-scrollbar-thumb': {\n      borderRadius: 8,\n      backgroundColor: options.thumb,\n      minHeight: 24,\n      border: `3px solid ${options.track}`\n    },\n    '&::-webkit-scrollbar-thumb:focus, & *::-webkit-scrollbar-thumb:focus': {\n      backgroundColor: options.active\n    },\n    '&::-webkit-scrollbar-thumb:active, & *::-webkit-scrollbar-thumb:active': {\n      backgroundColor: options.active\n    },\n    '&::-webkit-scrollbar-thumb:hover, & *::-webkit-scrollbar-thumb:hover': {\n      backgroundColor: options.active\n    },\n    '&::-webkit-scrollbar-corner, & *::-webkit-scrollbar-corner': {\n      backgroundColor: options.track\n    }\n  };\n}"], "mappings": "AAAA;AACA,MAAMA,SAAS,GAAG;EAChBC,KAAK,EAAE,SAAS;EAChBC,KAAK,EAAE,SAAS;EAChBC,MAAM,EAAE;AACV,CAAC;AACD,eAAe,SAASC,aAAaA,CAAA,EAAsB;EAAA,IAArBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAGN,SAAS;EACvD,OAAO;IACLS,cAAc,KAAAC,MAAA,CAAKL,OAAO,CAACH,KAAK,OAAAQ,MAAA,CAAIL,OAAO,CAACJ,KAAK,CAAE;IACnD,8CAA8C,EAAE;MAC9CU,eAAe,EAAEN,OAAO,CAACJ;IAC3B,CAAC;IACD,0DAA0D,EAAE;MAC1DW,YAAY,EAAE,CAAC;MACfD,eAAe,EAAEN,OAAO,CAACH,KAAK;MAC9BW,SAAS,EAAE,EAAE;MACbC,MAAM,eAAAJ,MAAA,CAAeL,OAAO,CAACJ,KAAK;IACpC,CAAC;IACD,sEAAsE,EAAE;MACtEU,eAAe,EAAEN,OAAO,CAACF;IAC3B,CAAC;IACD,wEAAwE,EAAE;MACxEQ,eAAe,EAAEN,OAAO,CAACF;IAC3B,CAAC;IACD,sEAAsE,EAAE;MACtEQ,eAAe,EAAEN,OAAO,CAACF;IAC3B,CAAC;IACD,4DAA4D,EAAE;MAC5DQ,eAAe,EAAEN,OAAO,CAACJ;IAC3B;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}