{"ast": null, "code": "(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('react')) : typeof define === 'function' && define.amd ? define(['exports', 'react'], factory) : (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ImageKitReact = {}, global.React));\n})(this, function (exports, React) {\n  'use strict';\n\n  function _interopDefaultLegacy(e) {\n    return e && typeof e === 'object' && 'default' in e ? e : {\n      'default': e\n    };\n  }\n  var React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n  function _typeof$1(obj) {\n    \"@babel/helpers - typeof\";\n\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n      _typeof$1 = function (obj) {\n        return typeof obj;\n      };\n    } else {\n      _typeof$1 = function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n      };\n    }\n    return _typeof$1(obj);\n  }\n  function _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError(\"Cannot call a class as a function\");\n    }\n  }\n  function _defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  function _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n  }\n  function _defineProperty$1(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  function ownKeys$1(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n      var symbols = Object.getOwnPropertySymbols(object);\n      if (enumerableOnly) symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n      keys.push.apply(keys, symbols);\n    }\n    return keys;\n  }\n  function _objectSpread2$1(target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i] != null ? arguments[i] : {};\n      if (i % 2) {\n        ownKeys$1(Object(source), true).forEach(function (key) {\n          _defineProperty$1(target, key, source[key]);\n        });\n      } else if (Object.getOwnPropertyDescriptors) {\n        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n      } else {\n        ownKeys$1(Object(source)).forEach(function (key) {\n          Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n      }\n    }\n    return target;\n  }\n  function _unsupportedIterableToArray$1(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray$1(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray$1(o, minLen);\n  }\n  function _arrayLikeToArray$1(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n    return arr2;\n  }\n  function _createForOfIteratorHelper(o, allowArrayLike) {\n    var it;\n    if (typeof Symbol === \"undefined\" || o[Symbol.iterator] == null) {\n      if (Array.isArray(o) || (it = _unsupportedIterableToArray$1(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n        if (it) o = it;\n        var i = 0;\n        var F = function () {};\n        return {\n          s: F,\n          n: function () {\n            if (i >= o.length) return {\n              done: true\n            };\n            return {\n              done: false,\n              value: o[i++]\n            };\n          },\n          e: function (e) {\n            throw e;\n          },\n          f: F\n        };\n      }\n      throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n    }\n    var normalCompletion = true,\n      didErr = false,\n      err;\n    return {\n      s: function () {\n        it = o[Symbol.iterator]();\n      },\n      n: function () {\n        var step = it.next();\n        normalCompletion = step.done;\n        return step;\n      },\n      e: function (e) {\n        didErr = true;\n        err = e;\n      },\n      f: function () {\n        try {\n          if (!normalCompletion && it.return != null) it.return();\n        } finally {\n          if (didErr) throw err;\n        }\n      }\n    };\n  }\n  var version = \"3.0.2\";\n  var errorMessages = {\n    MANDATORY_INITIALIZATION_MISSING: {\n      message: \"Missing urlEndpoint during SDK initialization\",\n      help: \"\"\n    },\n    INVALID_TRANSFORMATION_POSITION: {\n      message: \"Invalid transformationPosition parameter\",\n      help: \"\"\n    },\n    PRIVATE_KEY_CLIENT_SIDE: {\n      message: \"privateKey should not be passed on the client side\",\n      help: \"\"\n    },\n    MISSING_UPLOAD_DATA: {\n      message: \"Missing data for upload\",\n      help: \"\"\n    },\n    MISSING_UPLOAD_FILE_PARAMETER: {\n      message: \"Missing file parameter for upload\",\n      help: \"\"\n    },\n    MISSING_UPLOAD_FILENAME_PARAMETER: {\n      message: \"Missing fileName parameter for upload\",\n      help: \"\"\n    },\n    MISSING_AUTHENTICATION_ENDPOINT: {\n      message: \"Missing authentication endpoint for upload\",\n      help: \"\"\n    },\n    MISSING_PUBLIC_KEY: {\n      message: \"Missing public key for upload\",\n      help: \"\"\n    },\n    AUTH_ENDPOINT_TIMEOUT: {\n      message: \"The authenticationEndpoint you provided timed out in 60 seconds\",\n      help: \"\"\n    },\n    AUTH_ENDPOINT_NETWORK_ERROR: {\n      message: \"Request to authenticationEndpoint failed due to network error\",\n      help: \"\"\n    },\n    AUTH_INVALID_RESPONSE: {\n      message: \"Invalid response from authenticationEndpoint. The SDK expects a JSON response with three fields i.e. signature, token and expire.\",\n      help: \"\"\n    },\n    UPLOAD_ENDPOINT_NETWORK_ERROR: {\n      message: \"Request to ImageKit upload endpoint failed due to network error\",\n      help: \"\"\n    },\n    INVALID_UPLOAD_OPTIONS: {\n      message: \"Invalid uploadOptions parameter\",\n      help: \"\"\n    },\n    MISSING_SIGNATURE: {\n      message: \"Missing signature for upload. The SDK expects token, signature and expire for authentication.\",\n      help: \"\"\n    },\n    MISSING_TOKEN: {\n      message: \"Missing token for upload. The SDK expects token, signature and expire for authentication.\",\n      help: \"\"\n    },\n    MISSING_EXPIRE: {\n      message: \"Missing expire for upload. The SDK expects token, signature and expire for authentication.\",\n      help: \"\"\n    },\n    INVALID_TRANSFORMATION: {\n      message: \"Invalid transformation parameter. Please include at least pre, post, or both.\",\n      help: \"\"\n    },\n    INVALID_PRE_TRANSFORMATION: {\n      message: \"Invalid pre transformation parameter.\",\n      help: \"\"\n    },\n    INVALID_POST_TRANSFORMATION: {\n      message: \"Invalid post transformation parameter.\",\n      help: \"\"\n    }\n  };\n  function respond(isError, response, callback) {\n    if (typeof callback == \"function\") {\n      if (isError) {\n        callback(response, null);\n      } else {\n        callback(null, response);\n      }\n    }\n  }\n  function getResponseHeaderMap(xhr) {\n    var headers = {};\n    var responseHeaders = xhr.getAllResponseHeaders();\n    if (Object.keys(responseHeaders).length) {\n      responseHeaders.trim().split(/[\\r\\n]+/).map(function (value) {\n        return value.split(/: /);\n      }).forEach(function (keyValue) {\n        headers[keyValue[0].trim()] = keyValue[1].trim();\n      });\n    }\n    return headers;\n  }\n  var addResponseHeadersAndBody = function addResponseHeadersAndBody(body, xhr) {\n    var response = _objectSpread2$1({}, body);\n    var responseMetadata = {\n      statusCode: xhr.status,\n      headers: getResponseHeaderMap(xhr)\n    };\n    Object.defineProperty(response, \"$ResponseMetadata\", {\n      value: responseMetadata,\n      enumerable: false,\n      writable: false\n    });\n    return response;\n  };\n  var request = function request(uploadFileXHR, formData, callback) {\n    uploadFile(uploadFileXHR, formData).then(function (result) {\n      return respond(false, result, callback);\n    }, function (ex) {\n      return respond(true, ex, callback);\n    });\n  };\n  var uploadFile = function uploadFile(uploadFileXHR, formData) {\n    return new Promise(function (resolve, reject) {\n      uploadFileXHR.open('POST', 'https://upload.imagekit.io/api/v1/files/upload');\n      uploadFileXHR.onerror = function (e) {\n        return reject(errorMessages.UPLOAD_ENDPOINT_NETWORK_ERROR);\n      };\n      uploadFileXHR.onload = function () {\n        if (uploadFileXHR.status === 200) {\n          try {\n            var body = JSON.parse(uploadFileXHR.responseText);\n            var uploadResponse = addResponseHeadersAndBody(body, uploadFileXHR);\n            return resolve(uploadResponse);\n          } catch (ex) {\n            return reject(ex);\n          }\n        } else {\n          try {\n            var body = JSON.parse(uploadFileXHR.responseText);\n            var uploadError = addResponseHeadersAndBody(body, uploadFileXHR);\n            return reject(uploadError);\n          } catch (ex) {\n            return reject(ex);\n          }\n        }\n      };\n      uploadFileXHR.send(formData);\n    });\n  };\n  var upload = function upload(xhr, uploadOptions, options, callback) {\n    if (!uploadOptions.file) {\n      respond(true, errorMessages.MISSING_UPLOAD_FILE_PARAMETER, callback);\n      return;\n    }\n    if (!uploadOptions.fileName) {\n      respond(true, errorMessages.MISSING_UPLOAD_FILENAME_PARAMETER, callback);\n      return;\n    }\n    if (!options.publicKey) {\n      respond(true, errorMessages.MISSING_PUBLIC_KEY, callback);\n      return;\n    }\n    if (!uploadOptions.token) {\n      respond(true, errorMessages.MISSING_TOKEN, callback);\n      return;\n    }\n    if (!uploadOptions.signature) {\n      respond(true, errorMessages.MISSING_SIGNATURE, callback);\n      return;\n    }\n    if (!uploadOptions.expire) {\n      respond(true, errorMessages.MISSING_EXPIRE, callback);\n      return;\n    }\n    if (uploadOptions.transformation) {\n      if (!(Object.keys(uploadOptions.transformation).includes(\"pre\") || Object.keys(uploadOptions.transformation).includes(\"post\"))) {\n        respond(true, errorMessages.INVALID_TRANSFORMATION, callback);\n        return;\n      }\n      if (Object.keys(uploadOptions.transformation).includes(\"pre\") && !uploadOptions.transformation.pre) {\n        respond(true, errorMessages.INVALID_PRE_TRANSFORMATION, callback);\n        return;\n      }\n      if (Object.keys(uploadOptions.transformation).includes(\"post\")) {\n        if (Array.isArray(uploadOptions.transformation.post)) {\n          var _iterator = _createForOfIteratorHelper(uploadOptions.transformation.post),\n            _step;\n          try {\n            for (_iterator.s(); !(_step = _iterator.n()).done;) {\n              var transformation = _step.value;\n              if (transformation.type === \"abs\" && !(transformation.protocol || transformation.value)) {\n                respond(true, errorMessages.INVALID_POST_TRANSFORMATION, callback);\n                return;\n              } else if (transformation.type === \"transformation\" && !transformation.value) {\n                respond(true, errorMessages.INVALID_POST_TRANSFORMATION, callback);\n                return;\n              }\n            }\n          } catch (err) {\n            _iterator.e(err);\n          } finally {\n            _iterator.f();\n          }\n        } else {\n          respond(true, errorMessages.INVALID_POST_TRANSFORMATION, callback);\n          return;\n        }\n      }\n    }\n    var formData = new FormData();\n    var key;\n    for (key in uploadOptions) {\n      if (key) {\n        if (key === \"file\" && typeof uploadOptions.file != \"string\") {\n          formData.append('file', uploadOptions.file, String(uploadOptions.fileName));\n        } else if (key === \"tags\" && Array.isArray(uploadOptions.tags)) {\n          formData.append('tags', uploadOptions.tags.join(\",\"));\n        } else if (key === 'signature') {\n          formData.append(\"signature\", uploadOptions.signature);\n        } else if (key === 'expire') {\n          formData.append(\"expire\", String(uploadOptions.expire));\n        } else if (key === 'token') {\n          formData.append(\"token\", uploadOptions.token);\n        } else if (key === \"responseFields\" && Array.isArray(uploadOptions.responseFields)) {\n          formData.append('responseFields', uploadOptions.responseFields.join(\",\"));\n        } else if (key === \"extensions\" && Array.isArray(uploadOptions.extensions)) {\n          formData.append('extensions', JSON.stringify(uploadOptions.extensions));\n        } else if (key === \"customMetadata\" && _typeof$1(uploadOptions.customMetadata) === \"object\" && !Array.isArray(uploadOptions.customMetadata) && uploadOptions.customMetadata !== null) {\n          formData.append('customMetadata', JSON.stringify(uploadOptions.customMetadata));\n        } else if (key === \"transformation\" && _typeof$1(uploadOptions.transformation) === \"object\" && uploadOptions.transformation !== null) {\n          formData.append(key, JSON.stringify(uploadOptions.transformation));\n        } else if (key === 'checks' && uploadOptions.checks) {\n          formData.append(\"checks\", uploadOptions.checks);\n        } else if (uploadOptions[key] !== undefined) {\n          formData.append(key, String(uploadOptions[key]));\n        }\n      }\n    }\n    formData.append(\"publicKey\", options.publicKey);\n    request(xhr, formData, callback);\n  };\n  var supportedTransforms = {\n    width: \"w\",\n    height: \"h\",\n    aspectRatio: \"ar\",\n    quality: \"q\",\n    crop: \"c\",\n    cropMode: \"cm\",\n    focus: \"fo\",\n    x: \"x\",\n    y: \"y\",\n    format: \"f\",\n    radius: \"r\",\n    background: \"bg\",\n    border: \"b\",\n    rotation: \"rt\",\n    rotate: \"rt\",\n    blur: \"bl\",\n    named: \"n\",\n    progressive: \"pr\",\n    lossless: \"lo\",\n    trim: \"t\",\n    metadata: \"md\",\n    colorProfile: \"cp\",\n    defaultImage: \"di\",\n    dpr: \"dpr\",\n    effectSharpen: \"e-sharpen\",\n    effectUSM: \"e-usm\",\n    effectContrast: \"e-contrast\",\n    effectGray: \"e-grayscale\",\n    original: \"orig\",\n    effectShadow: \"e-shadow\",\n    effectGradient: \"e-gradient\",\n    raw: \"raw\"\n  };\n  var DEFAULT_TRANSFORMATION_POSITION = \"path\";\n  var QUERY_TRANSFORMATION_POSITION = \"query\";\n  var VALID_TRANSFORMATION_POSITIONS = [DEFAULT_TRANSFORMATION_POSITION, QUERY_TRANSFORMATION_POSITION];\n  var CHAIN_TRANSFORM_DELIMITER = \":\";\n  var TRANSFORM_DELIMITER = \",\";\n  var TRANSFORM_KEY_VALUE_DELIMITER = \"-\";\n  var transformationUtils = {\n    getDefault: function getDefault() {\n      return DEFAULT_TRANSFORMATION_POSITION;\n    },\n    addAsQueryParameter: function addAsQueryParameter(options) {\n      return options.transformationPosition === QUERY_TRANSFORMATION_POSITION;\n    },\n    validParameters: function validParameters(options) {\n      if (typeof options.transformationPosition == \"undefined\") return false;\n      return VALID_TRANSFORMATION_POSITIONS.indexOf(options.transformationPosition) != -1;\n    },\n    getTransformKey: function getTransformKey(transform) {\n      if (!transform) {\n        return \"\";\n      }\n      return supportedTransforms[transform] || supportedTransforms[transform.toLowerCase()] || \"\";\n    },\n    getChainTransformDelimiter: function getChainTransformDelimiter() {\n      return CHAIN_TRANSFORM_DELIMITER;\n    },\n    getTransformDelimiter: function getTransformDelimiter() {\n      return TRANSFORM_DELIMITER;\n    },\n    getTransformKeyValueDelimiter: function getTransformKeyValueDelimiter() {\n      return TRANSFORM_KEY_VALUE_DELIMITER;\n    }\n  };\n  var TRANSFORMATION_PARAMETER = \"tr\";\n  function removeTrailingSlash(str) {\n    if (typeof str == \"string\" && str[str.length - 1] == \"/\") {\n      str = str.substring(0, str.length - 1);\n    }\n    return str;\n  }\n  function removeLeadingSlash(str) {\n    if (typeof str == \"string\" && str[0] == \"/\") {\n      str = str.slice(1);\n    }\n    return str;\n  }\n  function pathJoin(parts, sep) {\n    var separator = sep || \"/\";\n    var replace = new RegExp(separator + \"{1,}\", \"g\");\n    return parts.join(separator).replace(replace, separator);\n  }\n  var buildURL = function buildURL(opts) {\n    if (!opts.path && !opts.src) {\n      return \"\";\n    }\n    var urlObj, isSrcParameterUsedForURL, urlEndpointPattern;\n    try {\n      if (opts.path) {\n        urlEndpointPattern = new URL(opts.urlEndpoint).pathname;\n        urlObj = new URL(pathJoin([opts.urlEndpoint.replace(urlEndpointPattern, \"\"), opts.path]));\n      } else {\n        urlObj = new URL(opts.src);\n        isSrcParameterUsedForURL = true;\n      }\n    } catch (e) {\n      console.error(e);\n      return \"\";\n    }\n    for (var i in opts.queryParameters) {\n      urlObj.searchParams.append(i, String(opts.queryParameters[i]));\n    }\n    var transformationString = constructTransformationString(opts.transformation);\n    if (transformationString && transformationString.length) {\n      if (transformationUtils.addAsQueryParameter(opts) || isSrcParameterUsedForURL) {\n        urlObj.searchParams.append(TRANSFORMATION_PARAMETER, transformationString);\n      } else {\n        urlObj.pathname = pathJoin([TRANSFORMATION_PARAMETER + transformationUtils.getChainTransformDelimiter() + transformationString, urlObj.pathname]);\n      }\n    }\n    if (urlEndpointPattern) {\n      urlObj.pathname = pathJoin([urlEndpointPattern, urlObj.pathname]);\n    } else {\n      urlObj.pathname = pathJoin([urlObj.pathname]);\n    }\n    return urlObj.href;\n  };\n  function constructTransformationString(transformation) {\n    if (!Array.isArray(transformation)) {\n      return \"\";\n    }\n    var parsedTransforms = [];\n    for (var i = 0, l = transformation.length; i < l; i++) {\n      var parsedTransformStep = [];\n      for (var key in transformation[i]) {\n        if (transformation[i][key] === undefined || transformation[i][key] === null) continue;\n        var transformKey = transformationUtils.getTransformKey(key);\n        if (!transformKey) {\n          transformKey = key;\n        }\n        if (transformation[i][key] === \"-\") {\n          parsedTransformStep.push(transformKey);\n        } else if (key === \"raw\") {\n          parsedTransformStep.push(transformation[i][key]);\n        } else {\n          var value = transformation[i][key];\n          if (transformKey === \"di\") {\n            value = removeTrailingSlash(removeLeadingSlash(value || \"\"));\n            value = value.replace(/\\//g, \"@@\");\n          }\n          parsedTransformStep.push([transformKey, value].join(transformationUtils.getTransformKeyValueDelimiter()));\n        }\n      }\n      parsedTransforms.push(parsedTransformStep.join(transformationUtils.getTransformDelimiter()));\n    }\n    return parsedTransforms.join(transformationUtils.getChainTransformDelimiter());\n  }\n  var url = function url(urlOpts, defaultOptions) {\n    return buildURL(_objectSpread2$1(_objectSpread2$1({}, defaultOptions), urlOpts));\n  };\n  function mandatoryParametersAvailable(options) {\n    return options.urlEndpoint;\n  }\n  var promisify = function promisify(thisContext, fn) {\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      if (args.length === fn.length && typeof args[args.length - 1] !== \"undefined\") {\n        if (typeof args[args.length - 1] !== \"function\") {\n          throw new Error(\"Callback must be a function.\");\n        }\n        fn.call.apply(fn, [thisContext].concat(args));\n      } else {\n        return new Promise(function (resolve, reject) {\n          var callback = function callback(err) {\n            if (err) {\n              return reject(err);\n            } else {\n              for (var _len2 = arguments.length, results = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n                results[_key2 - 1] = arguments[_key2];\n              }\n              resolve(results.length > 1 ? results : results[0]);\n            }\n          };\n          args.pop();\n          args.push(callback);\n          fn.call.apply(fn, [thisContext].concat(args));\n        });\n      }\n    };\n  };\n  var ImageKit = function () {\n    function ImageKit(opts) {\n      _classCallCheck(this, ImageKit);\n      _defineProperty$1(this, \"options\", {\n        sdkVersion: \"javascript-\".concat(version),\n        publicKey: \"\",\n        urlEndpoint: \"\",\n        transformationPosition: transformationUtils.getDefault()\n      });\n      this.options = _objectSpread2$1(_objectSpread2$1({}, this.options), opts || {});\n      if (!mandatoryParametersAvailable(this.options)) {\n        throw errorMessages.MANDATORY_INITIALIZATION_MISSING;\n      }\n      if (!transformationUtils.validParameters(this.options)) {\n        throw errorMessages.INVALID_TRANSFORMATION_POSITION;\n      }\n    }\n    _createClass(ImageKit, [{\n      key: \"url\",\n      value: function url$1(urlOptions) {\n        return url(urlOptions, this.options);\n      }\n    }, {\n      key: \"upload\",\n      value: function upload$1(uploadOptions, callbackOrOptions, options) {\n        var callback;\n        if (typeof callbackOrOptions === 'function') {\n          callback = callbackOrOptions;\n        } else {\n          options = callbackOrOptions || {};\n        }\n        if (!uploadOptions || _typeof$1(uploadOptions) !== \"object\") {\n          return respond(true, errorMessages.INVALID_UPLOAD_OPTIONS, callback);\n        }\n        var mergedOptions = _objectSpread2$1(_objectSpread2$1({}, this.options), options);\n        var _ref = uploadOptions || {},\n          userProvidedXHR = _ref.xhr;\n        delete uploadOptions.xhr;\n        var xhr = userProvidedXHR || new XMLHttpRequest();\n        return promisify(this, upload)(xhr, uploadOptions, mergedOptions, callback);\n      }\n    }]);\n    return ImageKit;\n  }();\n  function _iterableToArrayLimit(r, l) {\n    var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n    if (null != t) {\n      var e,\n        n,\n        i,\n        u,\n        a = [],\n        f = !0,\n        o = !1;\n      try {\n        if (i = (t = t.call(r)).next, 0 === l) {\n          if (Object(t) !== t) return;\n          f = !1;\n        } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n      } catch (r) {\n        o = !0, n = r;\n      } finally {\n        try {\n          if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return;\n        } finally {\n          if (o) throw n;\n        }\n      }\n      return a;\n    }\n  }\n  function ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n      var o = Object.getOwnPropertySymbols(e);\n      r && (o = o.filter(function (r) {\n        return Object.getOwnPropertyDescriptor(e, r).enumerable;\n      })), t.push.apply(t, o);\n    }\n    return t;\n  }\n  function _objectSpread2(e) {\n    for (var r = 1; r < arguments.length; r++) {\n      var t = null != arguments[r] ? arguments[r] : {};\n      r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n        _defineProperty(e, r, t[r]);\n      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n        Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n      });\n    }\n    return e;\n  }\n  function _regeneratorRuntime() {\n    _regeneratorRuntime = function () {\n      return e;\n    };\n    var t,\n      e = {},\n      r = Object.prototype,\n      n = r.hasOwnProperty,\n      o = Object.defineProperty || function (t, e, r) {\n        t[e] = r.value;\n      },\n      i = \"function\" == typeof Symbol ? Symbol : {},\n      a = i.iterator || \"@@iterator\",\n      c = i.asyncIterator || \"@@asyncIterator\",\n      u = i.toStringTag || \"@@toStringTag\";\n    function define(t, e, r) {\n      return Object.defineProperty(t, e, {\n        value: r,\n        enumerable: !0,\n        configurable: !0,\n        writable: !0\n      }), t[e];\n    }\n    try {\n      define({}, \"\");\n    } catch (t) {\n      define = function (t, e, r) {\n        return t[e] = r;\n      };\n    }\n    function wrap(t, e, r, n) {\n      var i = e && e.prototype instanceof Generator ? e : Generator,\n        a = Object.create(i.prototype),\n        c = new Context(n || []);\n      return o(a, \"_invoke\", {\n        value: makeInvokeMethod(t, r, c)\n      }), a;\n    }\n    function tryCatch(t, e, r) {\n      try {\n        return {\n          type: \"normal\",\n          arg: t.call(e, r)\n        };\n      } catch (t) {\n        return {\n          type: \"throw\",\n          arg: t\n        };\n      }\n    }\n    e.wrap = wrap;\n    var h = \"suspendedStart\",\n      l = \"suspendedYield\",\n      f = \"executing\",\n      s = \"completed\",\n      y = {};\n    function Generator() {}\n    function GeneratorFunction() {}\n    function GeneratorFunctionPrototype() {}\n    var p = {};\n    define(p, a, function () {\n      return this;\n    });\n    var d = Object.getPrototypeOf,\n      v = d && d(d(values([])));\n    v && v !== r && n.call(v, a) && (p = v);\n    var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p);\n    function defineIteratorMethods(t) {\n      [\"next\", \"throw\", \"return\"].forEach(function (e) {\n        define(t, e, function (t) {\n          return this._invoke(e, t);\n        });\n      });\n    }\n    function AsyncIterator(t, e) {\n      function invoke(r, o, i, a) {\n        var c = tryCatch(t[r], t, o);\n        if (\"throw\" !== c.type) {\n          var u = c.arg,\n            h = u.value;\n          return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) {\n            invoke(\"next\", t, i, a);\n          }, function (t) {\n            invoke(\"throw\", t, i, a);\n          }) : e.resolve(h).then(function (t) {\n            u.value = t, i(u);\n          }, function (t) {\n            return invoke(\"throw\", t, i, a);\n          });\n        }\n        a(c.arg);\n      }\n      var r;\n      o(this, \"_invoke\", {\n        value: function (t, n) {\n          function callInvokeWithMethodAndArg() {\n            return new e(function (e, r) {\n              invoke(t, n, e, r);\n            });\n          }\n          return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg();\n        }\n      });\n    }\n    function makeInvokeMethod(e, r, n) {\n      var o = h;\n      return function (i, a) {\n        if (o === f) throw new Error(\"Generator is already running\");\n        if (o === s) {\n          if (\"throw\" === i) throw a;\n          return {\n            value: t,\n            done: !0\n          };\n        }\n        for (n.method = i, n.arg = a;;) {\n          var c = n.delegate;\n          if (c) {\n            var u = maybeInvokeDelegate(c, n);\n            if (u) {\n              if (u === y) continue;\n              return u;\n            }\n          }\n          if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) {\n            if (o === h) throw o = s, n.arg;\n            n.dispatchException(n.arg);\n          } else \"return\" === n.method && n.abrupt(\"return\", n.arg);\n          o = f;\n          var p = tryCatch(e, r, n);\n          if (\"normal\" === p.type) {\n            if (o = n.done ? s : l, p.arg === y) continue;\n            return {\n              value: p.arg,\n              done: n.done\n            };\n          }\n          \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg);\n        }\n      };\n    }\n    function maybeInvokeDelegate(e, r) {\n      var n = r.method,\n        o = e.iterator[n];\n      if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y;\n      var i = tryCatch(o, e.iterator, r.arg);\n      if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y;\n      var a = i.arg;\n      return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y);\n    }\n    function pushTryEntry(t) {\n      var e = {\n        tryLoc: t[0]\n      };\n      1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e);\n    }\n    function resetTryEntry(t) {\n      var e = t.completion || {};\n      e.type = \"normal\", delete e.arg, t.completion = e;\n    }\n    function Context(t) {\n      this.tryEntries = [{\n        tryLoc: \"root\"\n      }], t.forEach(pushTryEntry, this), this.reset(!0);\n    }\n    function values(e) {\n      if (e || \"\" === e) {\n        var r = e[a];\n        if (r) return r.call(e);\n        if (\"function\" == typeof e.next) return e;\n        if (!isNaN(e.length)) {\n          var o = -1,\n            i = function next() {\n              for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next;\n              return next.value = t, next.done = !0, next;\n            };\n          return i.next = i;\n        }\n      }\n      throw new TypeError(typeof e + \" is not iterable\");\n    }\n    return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", {\n      value: GeneratorFunctionPrototype,\n      configurable: !0\n    }), o(GeneratorFunctionPrototype, \"constructor\", {\n      value: GeneratorFunction,\n      configurable: !0\n    }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) {\n      var e = \"function\" == typeof t && t.constructor;\n      return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name));\n    }, e.mark = function (t) {\n      return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t;\n    }, e.awrap = function (t) {\n      return {\n        __await: t\n      };\n    }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () {\n      return this;\n    }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) {\n      void 0 === i && (i = Promise);\n      var a = new AsyncIterator(wrap(t, r, n, o), i);\n      return e.isGeneratorFunction(r) ? a : a.next().then(function (t) {\n        return t.done ? t.value : a.next();\n      });\n    }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () {\n      return this;\n    }), define(g, \"toString\", function () {\n      return \"[object Generator]\";\n    }), e.keys = function (t) {\n      var e = Object(t),\n        r = [];\n      for (var n in e) r.push(n);\n      return r.reverse(), function next() {\n        for (; r.length;) {\n          var t = r.pop();\n          if (t in e) return next.value = t, next.done = !1, next;\n        }\n        return next.done = !0, next;\n      };\n    }, e.values = values, Context.prototype = {\n      constructor: Context,\n      reset: function (e) {\n        if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t);\n      },\n      stop: function () {\n        this.done = !0;\n        var t = this.tryEntries[0].completion;\n        if (\"throw\" === t.type) throw t.arg;\n        return this.rval;\n      },\n      dispatchException: function (e) {\n        if (this.done) throw e;\n        var r = this;\n        function handle(n, o) {\n          return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o;\n        }\n        for (var o = this.tryEntries.length - 1; o >= 0; --o) {\n          var i = this.tryEntries[o],\n            a = i.completion;\n          if (\"root\" === i.tryLoc) return handle(\"end\");\n          if (i.tryLoc <= this.prev) {\n            var c = n.call(i, \"catchLoc\"),\n              u = n.call(i, \"finallyLoc\");\n            if (c && u) {\n              if (this.prev < i.catchLoc) return handle(i.catchLoc, !0);\n              if (this.prev < i.finallyLoc) return handle(i.finallyLoc);\n            } else if (c) {\n              if (this.prev < i.catchLoc) return handle(i.catchLoc, !0);\n            } else {\n              if (!u) throw new Error(\"try statement without catch or finally\");\n              if (this.prev < i.finallyLoc) return handle(i.finallyLoc);\n            }\n          }\n        }\n      },\n      abrupt: function (t, e) {\n        for (var r = this.tryEntries.length - 1; r >= 0; --r) {\n          var o = this.tryEntries[r];\n          if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) {\n            var i = o;\n            break;\n          }\n        }\n        i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);\n        var a = i ? i.completion : {};\n        return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a);\n      },\n      complete: function (t, e) {\n        if (\"throw\" === t.type) throw t.arg;\n        return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y;\n      },\n      finish: function (t) {\n        for (var e = this.tryEntries.length - 1; e >= 0; --e) {\n          var r = this.tryEntries[e];\n          if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y;\n        }\n      },\n      catch: function (t) {\n        for (var e = this.tryEntries.length - 1; e >= 0; --e) {\n          var r = this.tryEntries[e];\n          if (r.tryLoc === t) {\n            var n = r.completion;\n            if (\"throw\" === n.type) {\n              var o = n.arg;\n              resetTryEntry(r);\n            }\n            return o;\n          }\n        }\n        throw new Error(\"illegal catch attempt\");\n      },\n      delegateYield: function (e, r, n) {\n        return this.delegate = {\n          iterator: values(e),\n          resultName: r,\n          nextLoc: n\n        }, \"next\" === this.method && (this.arg = t), y;\n      }\n    }, e;\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : String(i);\n  }\n  function _typeof(o) {\n    \"@babel/helpers - typeof\";\n\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n      return typeof o;\n    } : function (o) {\n      return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n  }\n  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n      var info = gen[key](arg);\n      var value = info.value;\n    } catch (error) {\n      reject(error);\n      return;\n    }\n    if (info.done) {\n      resolve(value);\n    } else {\n      Promise.resolve(value).then(_next, _throw);\n    }\n  }\n  function _asyncToGenerator(fn) {\n    return function () {\n      var self = this,\n        args = arguments;\n      return new Promise(function (resolve, reject) {\n        var gen = fn.apply(self, args);\n        function _next(value) {\n          asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n        }\n        function _throw(err) {\n          asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n        }\n        _next(undefined);\n      });\n    };\n  }\n  function _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  function _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function (target) {\n      for (var i = 1; i < arguments.length; i++) {\n        var source = arguments[i];\n        for (var key in source) {\n          if (Object.prototype.hasOwnProperty.call(source, key)) {\n            target[key] = source[key];\n          }\n        }\n      }\n      return target;\n    };\n    return _extends.apply(this, arguments);\n  }\n  function _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for (i = 0; i < sourceKeys.length; i++) {\n      key = sourceKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n    return target;\n  }\n  function _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n      var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n      for (i = 0; i < sourceSymbolKeys.length; i++) {\n        key = sourceSymbolKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n        target[key] = source[key];\n      }\n    }\n    return target;\n  }\n  function _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n  }\n  function _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n  }\n  function _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n  }\n  function _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n  }\n  function _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n  }\n  function _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n  }\n  function _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n    return arr2;\n  }\n  function _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  function _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  function createCommonjsModule(fn, basedir, module) {\n    return module = {\n      path: basedir,\n      exports: {},\n      require: function (path, base) {\n        return commonjsRequire(path, base === undefined || base === null ? module.path : base);\n      }\n    }, fn(module, module.exports), module.exports;\n  }\n  function commonjsRequire() {\n    throw new Error('Dynamic requires are not currently supported by @rollup/plugin-commonjs');\n  }\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  var ReactPropTypesSecret$1 = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n  var ReactPropTypesSecret_1 = ReactPropTypesSecret$1;\n  var ReactPropTypesSecret = ReactPropTypesSecret_1;\n  function emptyFunction() {}\n  function emptyFunctionWithReset() {}\n  emptyFunctionWithReset.resetWarningCache = emptyFunction;\n  var factoryWithThrowingShims = function () {\n    function shim(props, propName, componentName, location, propFullName, secret) {\n      if (secret === ReactPropTypesSecret) {\n        // It is still safe when called from React.\n        return;\n      }\n      var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use PropTypes.checkPropTypes() to call them. ' + 'Read more at http://fb.me/use-check-prop-types');\n      err.name = 'Invariant Violation';\n      throw err;\n    }\n    shim.isRequired = shim;\n    function getShim() {\n      return shim;\n    }\n    // Important!\n    // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n    var ReactPropTypes = {\n      array: shim,\n      bigint: shim,\n      bool: shim,\n      func: shim,\n      number: shim,\n      object: shim,\n      string: shim,\n      symbol: shim,\n      any: shim,\n      arrayOf: getShim,\n      element: shim,\n      elementType: shim,\n      instanceOf: getShim,\n      node: shim,\n      objectOf: getShim,\n      oneOf: getShim,\n      oneOfType: getShim,\n      shape: getShim,\n      exact: getShim,\n      checkPropTypes: emptyFunctionWithReset,\n      resetWarningCache: emptyFunction\n    };\n    ReactPropTypes.PropTypes = ReactPropTypes;\n    return ReactPropTypes;\n  };\n  var require$$0 = factoryWithThrowingShims;\n  var propTypes = createCommonjsModule(function (module) {\n    /**\n     * Copyright (c) 2013-present, Facebook, Inc.\n     *\n     * This source code is licensed under the MIT license found in the\n     * LICENSE file in the root directory of this source tree.\n     */\n\n    {\n      // By explicitly using `prop-types` you are opting into new production behavior.\n      // http://fb.me/prop-types-in-prod\n      module.exports = require$$0();\n    }\n  });\n  var PropTypes = propTypes;\n  var Props$2 = {\n    publicKey: PropTypes.string,\n    urlEndpoint: PropTypes.string,\n    authenticator: PropTypes.func\n  };\n  var IKContextProps = _objectSpread2(_objectSpread2({}, Props$2), {}, {\n    transformationPosition: PropTypes.oneOf(['path', 'query'])\n  });\n  var IKContextExtractedProps = _objectSpread2(_objectSpread2({}, IKContextProps), {}, {\n    ikClient: PropTypes.instanceOf(ImageKit)\n  });\n\n  // Create the context\n  var ImageKitContext = /*#__PURE__*/React.createContext({});\n\n  /**\n   * Provides a container for ImageKit components. Any option set in IKContext will be passed to the children.\n   *\n   * @example\n   *<IKContext  publicKey=\"<public key>\" urlEndpoint=\"url link\">\n   *    <!-- other tags -->\n   *    <Image src={link}/>\n   *</IKContext>\n   */\n  var IKContext = function IKContext(props) {\n    var extractContextOptions = function extractContextOptions(mergedOptions) {\n      var result = {};\n      var propKeys = Object.keys(IKContextExtractedProps);\n      for (var i = 0; i < propKeys.length; i++) {\n        var key = propKeys[i];\n        var value = mergedOptions[key];\n        if (value) {\n          result[key] = value;\n        }\n      }\n      return result;\n    };\n    var mergedOptions = _objectSpread2({}, props);\n    var contextOptionsExtracted = extractContextOptions(mergedOptions);\n    if (contextOptionsExtracted.urlEndpoint && contextOptionsExtracted.urlEndpoint.trim() !== \"\") {\n      contextOptionsExtracted.ikClient = new ImageKit({\n        urlEndpoint: contextOptionsExtracted.urlEndpoint,\n        // @ts-ignore\n        sdkVersion: \"\"\n      });\n    }\n    return /*#__PURE__*/React__default[\"default\"].createElement(ImageKitContext.Provider, {\n      value: contextOptionsExtracted\n    }, props.children);\n  };\n  var Props$1 = {\n    loading: PropTypes.oneOf(['lazy']),\n    lqip: PropTypes.shape({\n      active: PropTypes.bool,\n      quality: PropTypes.number,\n      threshold: PropTypes.number,\n      blur: PropTypes.number,\n      raw: PropTypes.string\n    }),\n    path: PropTypes.string,\n    src: PropTypes.string,\n    queryParameters: PropTypes.objectOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired),\n    transformation: PropTypes.arrayOf(PropTypes.object.isRequired),\n    transformationPosition: PropTypes.oneOf(['path', 'query'])\n  };\n  var COMBINED_IMAGE_PROP_TYPES$1 = _objectSpread2(_objectSpread2({}, Props$2), Props$1);\n  var fetchEffectiveConnection = function fetchEffectiveConnection() {\n    try {\n      return navigator.connection.effectiveType;\n    } catch (ex) {\n      return \"4g\";\n    }\n  };\n  var getSrc = function getSrc(_ref, ikClient, contextOptions) {\n    var urlEndpoint = _ref.urlEndpoint,\n      lqip = _ref.lqip,\n      src = _ref.src,\n      path = _ref.path,\n      transformation = _ref.transformation,\n      transformationPosition = _ref.transformationPosition,\n      queryParameters = _ref.queryParameters;\n    var options;\n    if (src) {\n      options = {\n        urlEndpoint: urlEndpoint || contextOptions.urlEndpoint,\n        src: src,\n        transformation: transformation || undefined,\n        transformationPosition: transformationPosition || contextOptions.transformationPosition || undefined,\n        queryParameters: queryParameters || {}\n      };\n    } else if (path) {\n      options = {\n        urlEndpoint: urlEndpoint || contextOptions.urlEndpoint,\n        path: path,\n        transformation: transformation || undefined,\n        transformationPosition: transformationPosition || contextOptions.transformationPosition || undefined,\n        queryParameters: queryParameters || {}\n      };\n    } else return {\n      originalSrc: \"\"\n    };\n    var result = {\n      originalSrc: ikClient.url(options)\n    };\n    if (lqip && lqip.active) {\n      var quality = Math.round(lqip.quality || lqip.threshold || 20);\n      var blur = Math.round(lqip.blur || 6);\n      var newTransformation = options.transformation ? _toConsumableArray(options.transformation) : [];\n      if (lqip.raw && typeof lqip.raw === \"string\" && lqip.raw.trim() !== \"\") {\n        newTransformation.push({\n          raw: lqip.raw.trim()\n        });\n      } else {\n        newTransformation.push({\n          quality: String(quality),\n          blur: String(blur)\n        });\n      }\n      result.lqipSrc = ikClient.url(_objectSpread2(_objectSpread2({}, options), {}, {\n        transformation: newTransformation\n      }));\n    }\n    return result;\n  };\n  var getIKElementsUrl = function getIKElementsUrl(_ref2, _ref3) {\n    var _ref2$lqip = _ref2.lqip,\n      lqip = _ref2$lqip === void 0 ? null : _ref2$lqip,\n      loading = _ref2.loading;\n    var intersected = _ref3.intersected,\n      originalSrcLoaded = _ref3.originalSrcLoaded,\n      originalSrc = _ref3.originalSrc,\n      lqipSrc = _ref3.lqipSrc;\n    /*\n      No lazy loading no lqip\n        src=originalImage\n      No lazy loading lqip\n        src=lqip\n        src=originalImage (when loaded)\n      lazy loading and no lqip\n        src=''\n        onIntersect:\n        src=originalImage\n      lazy loading and lqip\n        src=lqip\n        onIntersect:\n        src=originalImage (when loaded)\n    */\n    var isLqipActive = function isLqipActive(lqip) {\n      return lqip && lqip.active;\n    };\n    if (loading !== \"lazy\" && !isLqipActive(lqip)) {\n      return originalSrc;\n    } else if (loading !== \"lazy\" && isLqipActive(lqip)) {\n      if (originalSrcLoaded) {\n        return originalSrc;\n      } else {\n        return lqipSrc;\n      }\n    } else if (loading === \"lazy\" && !isLqipActive(lqip)) {\n      if (intersected) {\n        return originalSrc;\n      } else {\n        return \"\";\n      }\n    } else {\n      //  if (loading === \"lazy\" && isLqipActive(lqip))\n      if (intersected && originalSrcLoaded) {\n        return originalSrc;\n      } else {\n        return lqipSrc;\n      }\n    }\n  };\n  var useImageKitComponent = function useImageKitComponent(props) {\n    var contextOptions = React.useContext(ImageKitContext);\n    var getIKClient = function getIKClient() {\n      if (contextOptions && contextOptions.ikClient) {\n        return contextOptions.ikClient;\n      }\n      var urlEndpoint = props.urlEndpoint;\n      urlEndpoint = urlEndpoint || contextOptions && contextOptions.urlEndpoint;\n      if (!urlEndpoint || urlEndpoint.trim() === \"\") {\n        throw new Error(\"Missing urlEndpoint during initialization\");\n      }\n      var ikClient = new ImageKit({\n        urlEndpoint: urlEndpoint,\n        // @ts-ignore\n        sdkVersion: \"\"\n      });\n      return ikClient;\n    };\n    return {\n      getIKClient: getIKClient\n    };\n  };\n  var _excluded$2 = [\"urlEndpoint\", \"authenticator\", \"publicKey\", \"loading\", \"lqip\", \"path\", \"src\", \"transformation\", \"transformationPosition\", \"queryParameters\"];\n  var IKImage = function IKImage(props) {\n    var imageRef = React.useRef(null);\n    var _useImageKitComponent = useImageKitComponent(_objectSpread2({}, props)),\n      getIKClient = _useImageKitComponent.getIKClient;\n    var contextOptions = React.useContext(ImageKitContext);\n    var _useState = React.useState(undefined),\n      _useState2 = _slicedToArray(_useState, 2),\n      currentUrl = _useState2[0],\n      setCurrentUrl = _useState2[1];\n    var _useState3 = React.useState(\"\"),\n      _useState4 = _slicedToArray(_useState3, 2),\n      originalSrc = _useState4[0],\n      setOriginalSrc = _useState4[1];\n    var _useState5 = React.useState(\"\"),\n      _useState6 = _slicedToArray(_useState5, 2),\n      lqipSrc = _useState6[0],\n      setLqipSrc = _useState6[1];\n    var _useState7 = React.useState(false),\n      _useState8 = _slicedToArray(_useState7, 2),\n      originalSrcLoaded = _useState8[0],\n      setOriginalSrcLoaded = _useState8[1];\n    var _useState9 = React.useState(undefined),\n      _useState10 = _slicedToArray(_useState9, 2),\n      observe = _useState10[0],\n      setObserve = _useState10[1];\n    var _useState11 = React.useState(false),\n      _useState12 = _slicedToArray(_useState11, 2),\n      initialized = _useState12[0],\n      setInitialized = _useState12[1];\n    var _useState13 = React.useState(false),\n      _useState14 = _slicedToArray(_useState13, 2),\n      intersected = _useState14[0],\n      setIntersected = _useState14[1];\n    React.useEffect(function () {\n      var _getSrc = getSrc(props, getIKClient(), contextOptions),\n        newOriginalSrc = _getSrc.originalSrc,\n        newLqipSrc = _getSrc.lqipSrc;\n      setOriginalSrc(newOriginalSrc);\n      setLqipSrc(newLqipSrc ? newLqipSrc : '');\n      setInitialized(true);\n    }, [contextOptions, props]);\n    var updateImageUrl = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var url;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return getIKElementsUrl(props, {\n                originalSrc: originalSrc,\n                lqipSrc: lqipSrc,\n                intersected: intersected,\n                contextOptions: contextOptions,\n                initialzeState: initialized,\n                originalSrcLoaded: originalSrcLoaded,\n                observe: observe\n              });\n            case 2:\n              url = _context.sent;\n              // Include intersected state\n              if (url) {\n                setCurrentUrl(url);\n              }\n            case 4:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function updateImageUrl() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    var triggerOriginalImageLoad = function triggerOriginalImageLoad() {\n      var img = new Image();\n      img.onload = function () {\n        setOriginalSrcLoaded(true);\n      };\n      img.src = originalSrc;\n    };\n    React.useEffect(function () {\n      if (originalSrcLoaded) updateImageUrl();\n    }, [originalSrcLoaded]);\n    React.useEffect(function () {\n      var image = imageRef.current;\n      var loading = props.loading;\n      if (initialized) {\n        if (window && 'IntersectionObserver' in window && loading === \"lazy\") {\n          var connectionType = fetchEffectiveConnection();\n          var rootMargin = \"1250px\";\n          if (connectionType !== \"4g\") rootMargin = \"2500px\";\n          var imageObserver = new IntersectionObserver(function (entries) {\n            var el = entries[0];\n            if (el && el.isIntersecting && !intersected) {\n              setIntersected(true);\n              setObserve(function (prevObserver) {\n                if (prevObserver) {\n                  prevObserver.disconnect();\n                }\n                return undefined;\n              });\n              triggerOriginalImageLoad();\n              updateImageUrl();\n            }\n          }, {\n            rootMargin: \"\".concat(rootMargin, \" 0px \").concat(rootMargin, \" 0px\")\n          });\n          if (image) {\n            imageObserver.observe(image);\n            setObserve(imageObserver);\n          }\n        } else {\n          setIntersected(true);\n          triggerOriginalImageLoad();\n          updateImageUrl();\n        }\n      }\n      return function () {\n        if (observe) {\n          observe.disconnect();\n        }\n      };\n    }, [props, originalSrc, lqipSrc]);\n    props.urlEndpoint;\n    props.authenticator;\n    props.publicKey;\n    props.loading;\n    props.lqip;\n    props.path;\n    props.src;\n    props.transformation;\n    props.transformationPosition;\n    props.queryParameters;\n    var restProps = _objectWithoutProperties(props, _excluded$2);\n    return /*#__PURE__*/React__default[\"default\"].createElement(\"img\", _extends({\n      alt: props.alt || \"\",\n      src: currentUrl ? currentUrl : undefined,\n      ref: imageRef\n    }, restProps));\n  };\n  IKImage.propTypes = COMBINED_IMAGE_PROP_TYPES$1;\n  var Props = {\n    path: PropTypes.string,\n    src: PropTypes.string,\n    queryParameters: PropTypes.objectOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired),\n    transformation: PropTypes.arrayOf(PropTypes.object.isRequired),\n    transformationPosition: PropTypes.oneOf(['path', 'query'])\n  };\n  var COMBINED_IMAGE_PROP_TYPES = _objectSpread2(_objectSpread2({}, Props$2), Props);\n  var _excluded$1 = [\"urlEndpoint\", \"publicKey\", \"authenticator\", \"path\", \"src\", \"transformation\", \"transformationPosition\", \"queryParameters\"];\n  var IKVideo = function IKVideo(props) {\n    var videoRef = React.useRef(null);\n    var _useState = React.useState({\n        currentUrl: undefined,\n        contextOptions: {}\n      }),\n      _useState2 = _slicedToArray(_useState, 2),\n      state = _useState2[0],\n      setState = _useState2[1];\n    var _useImageKitComponent = useImageKitComponent(_objectSpread2({}, props)),\n      getIKClient = _useImageKitComponent.getIKClient;\n    var contextItems = React.useContext(ImageKitContext);\n    React.useEffect(function () {\n      var _getSrc = getSrc(props, getIKClient(), contextItems),\n        originalSrc = _getSrc.originalSrc;\n      setState(function (prevState) {\n        return _objectSpread2(_objectSpread2({}, prevState), {}, {\n          currentUrl: originalSrc,\n          contextOptions: contextItems\n        });\n      });\n    }, [contextItems, props]);\n    var currentUrl = state.currentUrl;\n    props.urlEndpoint;\n    props.publicKey;\n    props.authenticator;\n    props.path;\n    props.src;\n    props.transformation;\n    props.transformationPosition;\n    props.queryParameters;\n    var restProps = _objectWithoutProperties(props, _excluded$1);\n    return /*#__PURE__*/React__default[\"default\"].createElement(\"video\", _extends({}, restProps, {\n      ref: videoRef,\n      key: currentUrl\n    }), /*#__PURE__*/React__default[\"default\"].createElement(\"source\", {\n      src: currentUrl,\n      type: \"video/mp4\"\n    }));\n  };\n  IKVideo.propTypes = COMBINED_IMAGE_PROP_TYPES;\n  var _excluded = [\"publicKey\", \"urlEndpoint\", \"authenticator\", \"fileName\", \"useUniqueFileName\", \"tags\", \"folder\", \"isPrivateFile\", \"customCoordinates\", \"responseFields\", \"onError\", \"onSuccess\", \"onUploadStart\", \"onUploadProgress\", \"validateFile\", \"webhookUrl\", \"overwriteFile\", \"overwriteAITags\", \"overwriteTags\", \"overwriteCustomMetadata\", \"extensions\", \"customMetadata\", \"transformation\", \"checks\", \"overrideParameters\"];\n  var IKUpload = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var _useState = React.useState({}),\n      _useState2 = _slicedToArray(_useState, 2),\n      state = _useState2[0],\n      setState = _useState2[1];\n    var contextOptions = React.useContext(ImageKitContext);\n    var _useImageKitComponent = useImageKitComponent(_objectSpread2({}, props)),\n      getIKClient = _useImageKitComponent.getIKClient;\n    React.useEffect(function () {\n      var abort = function abort() {\n        if (state.xhr) {\n          state.xhr.abort();\n        }\n      };\n      if (ref && _typeof(ref) === \"object\" && ref.hasOwnProperty(\"current\")) {\n        var refObject = ref;\n        refObject.current.abort = abort;\n      }\n    }, [state.xhr, ref]);\n    props.publicKey;\n    props.urlEndpoint;\n    props.authenticator;\n    var fileName = props.fileName,\n      useUniqueFileName = props.useUniqueFileName,\n      tags = props.tags,\n      folder = props.folder,\n      isPrivateFile = props.isPrivateFile,\n      customCoordinates = props.customCoordinates,\n      responseFields = props.responseFields,\n      onError = props.onError,\n      onSuccess = props.onSuccess;\n    props.onUploadStart;\n    props.onUploadProgress;\n    props.validateFile;\n    var webhookUrl = props.webhookUrl,\n      overwriteFile = props.overwriteFile,\n      overwriteAITags = props.overwriteAITags,\n      overwriteTags = props.overwriteTags,\n      overwriteCustomMetadata = props.overwriteCustomMetadata,\n      extensions = props.extensions,\n      customMetadata = props.customMetadata,\n      transformation = props.transformation,\n      checks = props.checks;\n    props.overrideParameters;\n    var restProps = _objectWithoutProperties(props, _excluded);\n    var uploadFile = function uploadFile(e) {\n      var _e$target$files;\n      var publicKey = props.publicKey || contextOptions.publicKey;\n      var authenticator = props.authenticator || contextOptions.authenticator;\n      var urlEndpoint = props.urlEndpoint || contextOptions.urlEndpoint;\n      if (!publicKey || publicKey.trim() === \"\") {\n        console.error(\"Missing publicKey\");\n        if (onError && typeof onError === \"function\") {\n          onError({\n            message: \"Missing publicKey\"\n          });\n        }\n        return;\n      }\n      if (!authenticator) {\n        console.error(\"The authenticator function is not provided.\");\n        if (onError && typeof onError === \"function\") {\n          onError({\n            message: \"The authenticator function is not provided.\"\n          });\n        }\n        return;\n      }\n      if (typeof authenticator !== 'function') {\n        console.error(\"The provided authenticator is not a function.\");\n        if (onError && typeof onError === \"function\") {\n          onError({\n            message: \"The provided authenticator is not a function.\"\n          });\n        }\n        return;\n      }\n      if (!urlEndpoint || urlEndpoint.trim() === \"\") {\n        console.error(\"Missing urlEndpoint\");\n        if (onError && typeof onError === \"function\") {\n          onError({\n            message: \"Missing urlEndpoint\"\n          });\n        }\n        return;\n      }\n      var ikClient = getIKClient();\n      var file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n      if (!file) {\n        return;\n      }\n      if (props.validateFile && !props.validateFile(file)) {\n        return;\n      }\n      if (props.onUploadStart && typeof props.onUploadStart === \"function\") {\n        props.onUploadStart(e);\n      }\n      var overrideValues = {};\n      if (props.overrideParameters && typeof props.overrideParameters === 'function') {\n        overrideValues = props.overrideParameters(file) || {};\n      }\n      var xhr = new XMLHttpRequest();\n      var progressCb = function progressCb(e) {\n        if (props.onUploadProgress && typeof props.onUploadProgress === 'function') {\n          props.onUploadProgress(e);\n        }\n      };\n      xhr.upload.addEventListener('progress', progressCb);\n      var params = {\n        file: file,\n        fileName: overrideValues.fileName || fileName || file.name,\n        useUniqueFileName: overrideValues.useUniqueFileName || useUniqueFileName,\n        tags: overrideValues.tags || tags,\n        folder: overrideValues.folder || folder,\n        isPrivateFile: overrideValues.isPrivateFile || isPrivateFile,\n        customCoordinates: overrideValues.customCoordinates || customCoordinates,\n        responseFields: responseFields,\n        extensions: overrideValues.extensions || extensions,\n        webhookUrl: overrideValues.webhookUrl || webhookUrl,\n        overwriteFile: overrideValues.overwriteFile || overwriteFile,\n        overwriteAITags: overrideValues.overwriteAITags || overwriteAITags,\n        overwriteTags: overrideValues.overwriteTags || overwriteTags,\n        overwriteCustomMetadata: overrideValues.overwriteCustomMetadata || overwriteCustomMetadata,\n        customMetadata: overrideValues.customMetadata || customMetadata,\n        signature: '',\n        expire: 0,\n        token: '',\n        xhr: xhr,\n        transformation: overrideValues.transformation || transformation,\n        checks: overrideValues.checks || checks\n      };\n      var authPromise = authenticator();\n      if (!(authPromise instanceof Promise)) {\n        if (onError && typeof onError === \"function\") {\n          onError({\n            message: \"The authenticator function is expected to return a Promise instance.\"\n          });\n        }\n        return;\n      }\n      authPromise.then(function (_ref) {\n        var signature = _ref.signature,\n          token = _ref.token,\n          expire = _ref.expire;\n        params['signature'] = signature;\n        params['expire'] = expire;\n        params['token'] = token;\n        ikClient.upload(params, function (err, result) {\n          if (err) {\n            if (onError && typeof onError === \"function\") {\n              console.log(err);\n              onError(err);\n            }\n          } else {\n            if (onSuccess && typeof onSuccess === \"function\") {\n              onSuccess(result);\n            }\n          }\n          xhr.upload.removeEventListener('progress', progressCb);\n        }, {\n          publicKey: publicKey\n        });\n        setState({\n          xhr: xhr\n        });\n      })[\"catch\"](function (data) {\n        var error;\n        if (data instanceof Array) {\n          error = data[0];\n        } else {\n          error = data;\n        }\n        if (onError && typeof onError === \"function\") {\n          onError({\n            message: String(error)\n          });\n        }\n        return;\n      });\n    };\n    return /*#__PURE__*/React__default[\"default\"].createElement(\"input\", _extends({}, restProps, {\n      ref: ref,\n      type: \"file\",\n      onChange: function onChange(e) {\n        if (props.onChange && typeof props.onChange === \"function\") {\n          props.onChange(e);\n        }\n        uploadFile(e);\n      }\n    }));\n  });\n  exports.IKContext = IKContext;\n  exports.IKCore = ImageKit;\n  exports.IKImage = IKImage;\n  exports.IKUpload = IKUpload;\n  exports.IKVideo = IKVideo;\n  Object.defineProperty(exports, '__esModule', {\n    value: true\n  });\n});", "map": {"version": 3, "names": ["global", "factory", "exports", "module", "require", "define", "amd", "globalThis", "self", "ImageKitReact", "React", "_interopDefaultLegacy", "e", "React__default", "_typeof$1", "obj", "Symbol", "iterator", "constructor", "prototype", "_classCallCheck", "instance", "<PERSON><PERSON><PERSON><PERSON>", "TypeError", "_defineProperties", "target", "props", "i", "length", "descriptor", "enumerable", "configurable", "writable", "Object", "defineProperty", "key", "_createClass", "protoProps", "staticProps", "_defineProperty$1", "value", "ownKeys$1", "object", "enumerableOnly", "keys", "getOwnPropertySymbols", "symbols", "filter", "sym", "getOwnPropertyDescriptor", "push", "apply", "_objectSpread2$1", "arguments", "source", "for<PERSON>ach", "getOwnPropertyDescriptors", "defineProperties", "_unsupportedIterableToArray$1", "o", "minLen", "_arrayLikeToArray$1", "n", "toString", "call", "slice", "name", "Array", "from", "test", "arr", "len", "arr2", "_createForOfIteratorHelper", "allowArrayLike", "it", "isArray", "F", "s", "done", "f", "normalCompletion", "didErr", "err", "step", "next", "return", "version", "errorMessages", "MANDATORY_INITIALIZATION_MISSING", "message", "help", "INVALID_TRANSFORMATION_POSITION", "PRIVATE_KEY_CLIENT_SIDE", "MISSING_UPLOAD_DATA", "MISSING_UPLOAD_FILE_PARAMETER", "MISSING_UPLOAD_FILENAME_PARAMETER", "MISSING_AUTHENTICATION_ENDPOINT", "MISSING_PUBLIC_KEY", "AUTH_ENDPOINT_TIMEOUT", "AUTH_ENDPOINT_NETWORK_ERROR", "AUTH_INVALID_RESPONSE", "UPLOAD_ENDPOINT_NETWORK_ERROR", "INVALID_UPLOAD_OPTIONS", "MISSING_SIGNATURE", "MISSING_TOKEN", "MISSING_EXPIRE", "INVALID_TRANSFORMATION", "INVALID_PRE_TRANSFORMATION", "INVALID_POST_TRANSFORMATION", "respond", "isError", "response", "callback", "getResponseHeaderMap", "xhr", "headers", "responseHeaders", "getAllResponseHeaders", "trim", "split", "map", "keyValue", "addResponseHeadersAndBody", "body", "responseMetadata", "statusCode", "status", "request", "uploadFileXHR", "formData", "uploadFile", "then", "result", "ex", "Promise", "resolve", "reject", "open", "onerror", "onload", "JSON", "parse", "responseText", "uploadResponse", "uploadError", "send", "upload", "uploadOptions", "options", "file", "fileName", "public<PERSON>ey", "token", "signature", "expire", "transformation", "includes", "pre", "post", "_iterator", "_step", "type", "protocol", "FormData", "append", "String", "tags", "join", "responseFields", "extensions", "stringify", "customMetadata", "checks", "undefined", "supportedTransforms", "width", "height", "aspectRatio", "quality", "crop", "cropMode", "focus", "x", "y", "format", "radius", "background", "border", "rotation", "rotate", "blur", "named", "progressive", "lossless", "metadata", "colorProfile", "defaultImage", "dpr", "effectSharpen", "effectUSM", "effectContrast", "effectGray", "original", "effectShadow", "effectGradient", "raw", "DEFAULT_TRANSFORMATION_POSITION", "QUERY_TRANSFORMATION_POSITION", "VALID_TRANSFORMATION_POSITIONS", "CHAIN_TRANSFORM_DELIMITER", "TRANSFORM_DELIMITER", "TRANSFORM_KEY_VALUE_DELIMITER", "transformationUtils", "getDefault", "addAsQueryParameter", "transformationPosition", "validParameters", "indexOf", "getTransformKey", "transform", "toLowerCase", "getChainTransformDelimiter", "getTransformDelimiter", "getTransformKeyValueDelimiter", "TRANSFORMATION_PARAMETER", "removeTrailingSlash", "str", "substring", "removeLeadingSlash", "pathJoin", "parts", "sep", "separator", "replace", "RegExp", "buildURL", "opts", "path", "src", "url<PERSON>bj", "isSrcParameterUsedForURL", "urlEndpointPattern", "URL", "urlEndpoint", "pathname", "console", "error", "queryParameters", "searchParams", "transformationString", "constructTransformationString", "href", "parsedTransforms", "l", "parsedTransformStep", "transform<PERSON>ey", "url", "urlOpts", "defaultOptions", "mandatoryParametersAvailable", "promisify", "thisContext", "fn", "_len", "args", "_key", "Error", "concat", "_len2", "results", "_key2", "pop", "ImageKit", "sdkVersion", "url$1", "urlOptions", "upload$1", "callbackOrOptions", "mergedOptions", "_ref", "userProvidedXHR", "XMLHttpRequest", "_iterableToArrayLimit", "r", "t", "u", "a", "ownKeys", "_objectSpread2", "_defineProperty", "_regeneratorRuntime", "hasOwnProperty", "c", "asyncIterator", "toStringTag", "wrap", "Generator", "create", "Context", "makeInvokeMethod", "tryCatch", "arg", "h", "GeneratorFunction", "GeneratorFunctionPrototype", "p", "d", "getPrototypeOf", "v", "values", "g", "defineIteratorMethods", "_invoke", "AsyncIterator", "invoke", "__await", "callInvokeWithMethodAndArg", "method", "delegate", "maybeInvokeDelegate", "sent", "_sent", "dispatchException", "abrupt", "resultName", "nextLoc", "pushTryEntry", "tryLoc", "catchLoc", "finallyLoc", "afterLoc", "tryEntries", "resetTryEntry", "completion", "reset", "isNaN", "displayName", "isGeneratorFunction", "mark", "setPrototypeOf", "__proto__", "awrap", "async", "reverse", "prev", "char<PERSON>t", "stop", "rval", "handle", "complete", "finish", "catch", "<PERSON><PERSON><PERSON>", "_toPrimitive", "toPrimitive", "Number", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_typeof", "asyncGeneratorStep", "gen", "_next", "_throw", "info", "_asyncToGenerator", "_extends", "assign", "bind", "_objectWithoutPropertiesLoose", "excluded", "sourceKeys", "_objectWithoutProperties", "sourceSymbolKeys", "propertyIsEnumerable", "_slicedToArray", "_arrayWithHoles", "_unsupportedIterableToArray", "_nonIterableRest", "_toConsumableArray", "_arrayWithoutHoles", "_iterableToArray", "_nonIterableSpread", "_arrayLikeToArray", "iter", "createCommonjsModule", "basedir", "base", "commonjsRequire", "ReactPropTypesSecret$1", "ReactPropTypesSecret_1", "ReactPropTypesSecret", "emptyFunction", "emptyFunctionWithReset", "resetWarningCache", "factoryWithThrowingShims", "shim", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "secret", "isRequired", "getShim", "ReactPropTypes", "array", "bigint", "bool", "func", "number", "string", "symbol", "any", "arrayOf", "element", "elementType", "instanceOf", "node", "objectOf", "oneOf", "oneOfType", "shape", "exact", "checkPropTypes", "PropTypes", "require$$0", "propTypes", "Props$2", "authenticator", "IKContextProps", "IKContextExtractedProps", "ikClient", "ImageKitContext", "createContext", "IKContext", "extractContextOptions", "propKeys", "contextOptionsExtracted", "createElement", "Provider", "children", "Props$1", "loading", "lqip", "active", "threshold", "COMBINED_IMAGE_PROP_TYPES$1", "fetchEffectiveConnection", "navigator", "connection", "effectiveType", "getSrc", "contextOptions", "originalSrc", "Math", "round", "newTransformation", "lqipSrc", "getIKElementsUrl", "_ref2", "_ref3", "_ref2$lqip", "intersected", "originalSrcLoaded", "isLqipActive", "useImageKitComponent", "useContext", "getIKClient", "_excluded$2", "IKImage", "imageRef", "useRef", "_useImageKitComponent", "_useState", "useState", "_useState2", "currentUrl", "setCurrentUrl", "_useState3", "_useState4", "setOriginalSrc", "_useState5", "_useState6", "setLqipSrc", "_useState7", "_useState8", "setOriginalSrcLoaded", "_useState9", "_useState10", "observe", "setObserve", "_useState11", "_useState12", "initialized", "setInitialized", "_useState13", "_useState14", "setIntersected", "useEffect", "_getSrc", "newOriginalSrc", "newLqipSrc", "updateImageUrl", "_callee", "_callee$", "_context", "initialzeState", "triggerOriginalImageLoad", "img", "Image", "image", "current", "window", "connectionType", "rootMargin", "imageObserver", "IntersectionObserver", "entries", "el", "isIntersecting", "prevObserver", "disconnect", "restProps", "alt", "ref", "Props", "COMBINED_IMAGE_PROP_TYPES", "_excluded$1", "IKVideo", "videoRef", "state", "setState", "contextItems", "prevState", "_excluded", "IKUpload", "forwardRef", "abort", "refObject", "useUniqueFileName", "folder", "isPrivateFile", "customCoordinates", "onError", "onSuccess", "onUploadStart", "onUploadProgress", "validateFile", "webhookUrl", "overwriteFile", "overwriteAITags", "overwriteTags", "overwriteCustomMetadata", "overrideParameters", "_e$target$files", "files", "overrideValues", "progressCb", "addEventListener", "params", "authPromise", "log", "removeEventListener", "data", "onChange", "IKCore"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/imagekitio-react/dist/imagekitio-react.umd.js"], "sourcesContent": ["(function (global, factory) {\n  typeof exports === 'object' && typeof module !== 'undefined' ? factory(exports, require('react')) :\n  typeof define === 'function' && define.amd ? define(['exports', 'react'], factory) :\n  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, factory(global.ImageKitReact = {}, global.React));\n})(this, (function (exports, React) { 'use strict';\n\n  function _interopDefaultLegacy (e) { return e && typeof e === 'object' && 'default' in e ? e : { 'default': e }; }\n\n  var React__default = /*#__PURE__*/_interopDefaultLegacy(React);\n\n  function _typeof$1(obj) {\n    \"@babel/helpers - typeof\";\n\n    if (typeof Symbol === \"function\" && typeof Symbol.iterator === \"symbol\") {\n      _typeof$1 = function (obj) {\n        return typeof obj;\n      };\n    } else {\n      _typeof$1 = function (obj) {\n        return obj && typeof Symbol === \"function\" && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n      };\n    }\n    return _typeof$1(obj);\n  }\n  function _classCallCheck(instance, Constructor) {\n    if (!(instance instanceof Constructor)) {\n      throw new TypeError(\"Cannot call a class as a function\");\n    }\n  }\n  function _defineProperties(target, props) {\n    for (var i = 0; i < props.length; i++) {\n      var descriptor = props[i];\n      descriptor.enumerable = descriptor.enumerable || false;\n      descriptor.configurable = true;\n      if (\"value\" in descriptor) descriptor.writable = true;\n      Object.defineProperty(target, descriptor.key, descriptor);\n    }\n  }\n  function _createClass(Constructor, protoProps, staticProps) {\n    if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n    if (staticProps) _defineProperties(Constructor, staticProps);\n    return Constructor;\n  }\n  function _defineProperty$1(obj, key, value) {\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  function ownKeys$1(object, enumerableOnly) {\n    var keys = Object.keys(object);\n    if (Object.getOwnPropertySymbols) {\n      var symbols = Object.getOwnPropertySymbols(object);\n      if (enumerableOnly) symbols = symbols.filter(function (sym) {\n        return Object.getOwnPropertyDescriptor(object, sym).enumerable;\n      });\n      keys.push.apply(keys, symbols);\n    }\n    return keys;\n  }\n  function _objectSpread2$1(target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i] != null ? arguments[i] : {};\n      if (i % 2) {\n        ownKeys$1(Object(source), true).forEach(function (key) {\n          _defineProperty$1(target, key, source[key]);\n        });\n      } else if (Object.getOwnPropertyDescriptors) {\n        Object.defineProperties(target, Object.getOwnPropertyDescriptors(source));\n      } else {\n        ownKeys$1(Object(source)).forEach(function (key) {\n          Object.defineProperty(target, key, Object.getOwnPropertyDescriptor(source, key));\n        });\n      }\n    }\n    return target;\n  }\n  function _unsupportedIterableToArray$1(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray$1(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray$1(o, minLen);\n  }\n  function _arrayLikeToArray$1(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n    return arr2;\n  }\n  function _createForOfIteratorHelper(o, allowArrayLike) {\n    var it;\n    if (typeof Symbol === \"undefined\" || o[Symbol.iterator] == null) {\n      if (Array.isArray(o) || (it = _unsupportedIterableToArray$1(o)) || allowArrayLike && o && typeof o.length === \"number\") {\n        if (it) o = it;\n        var i = 0;\n        var F = function () {};\n        return {\n          s: F,\n          n: function () {\n            if (i >= o.length) return {\n              done: true\n            };\n            return {\n              done: false,\n              value: o[i++]\n            };\n          },\n          e: function (e) {\n            throw e;\n          },\n          f: F\n        };\n      }\n      throw new TypeError(\"Invalid attempt to iterate non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n    }\n    var normalCompletion = true,\n      didErr = false,\n      err;\n    return {\n      s: function () {\n        it = o[Symbol.iterator]();\n      },\n      n: function () {\n        var step = it.next();\n        normalCompletion = step.done;\n        return step;\n      },\n      e: function (e) {\n        didErr = true;\n        err = e;\n      },\n      f: function () {\n        try {\n          if (!normalCompletion && it.return != null) it.return();\n        } finally {\n          if (didErr) throw err;\n        }\n      }\n    };\n  }\n  var version = \"3.0.2\";\n  var errorMessages = {\n    MANDATORY_INITIALIZATION_MISSING: {\n      message: \"Missing urlEndpoint during SDK initialization\",\n      help: \"\"\n    },\n    INVALID_TRANSFORMATION_POSITION: {\n      message: \"Invalid transformationPosition parameter\",\n      help: \"\"\n    },\n    PRIVATE_KEY_CLIENT_SIDE: {\n      message: \"privateKey should not be passed on the client side\",\n      help: \"\"\n    },\n    MISSING_UPLOAD_DATA: {\n      message: \"Missing data for upload\",\n      help: \"\"\n    },\n    MISSING_UPLOAD_FILE_PARAMETER: {\n      message: \"Missing file parameter for upload\",\n      help: \"\"\n    },\n    MISSING_UPLOAD_FILENAME_PARAMETER: {\n      message: \"Missing fileName parameter for upload\",\n      help: \"\"\n    },\n    MISSING_AUTHENTICATION_ENDPOINT: {\n      message: \"Missing authentication endpoint for upload\",\n      help: \"\"\n    },\n    MISSING_PUBLIC_KEY: {\n      message: \"Missing public key for upload\",\n      help: \"\"\n    },\n    AUTH_ENDPOINT_TIMEOUT: {\n      message: \"The authenticationEndpoint you provided timed out in 60 seconds\",\n      help: \"\"\n    },\n    AUTH_ENDPOINT_NETWORK_ERROR: {\n      message: \"Request to authenticationEndpoint failed due to network error\",\n      help: \"\"\n    },\n    AUTH_INVALID_RESPONSE: {\n      message: \"Invalid response from authenticationEndpoint. The SDK expects a JSON response with three fields i.e. signature, token and expire.\",\n      help: \"\"\n    },\n    UPLOAD_ENDPOINT_NETWORK_ERROR: {\n      message: \"Request to ImageKit upload endpoint failed due to network error\",\n      help: \"\"\n    },\n    INVALID_UPLOAD_OPTIONS: {\n      message: \"Invalid uploadOptions parameter\",\n      help: \"\"\n    },\n    MISSING_SIGNATURE: {\n      message: \"Missing signature for upload. The SDK expects token, signature and expire for authentication.\",\n      help: \"\"\n    },\n    MISSING_TOKEN: {\n      message: \"Missing token for upload. The SDK expects token, signature and expire for authentication.\",\n      help: \"\"\n    },\n    MISSING_EXPIRE: {\n      message: \"Missing expire for upload. The SDK expects token, signature and expire for authentication.\",\n      help: \"\"\n    },\n    INVALID_TRANSFORMATION: {\n      message: \"Invalid transformation parameter. Please include at least pre, post, or both.\",\n      help: \"\"\n    },\n    INVALID_PRE_TRANSFORMATION: {\n      message: \"Invalid pre transformation parameter.\",\n      help: \"\"\n    },\n    INVALID_POST_TRANSFORMATION: {\n      message: \"Invalid post transformation parameter.\",\n      help: \"\"\n    }\n  };\n  function respond(isError, response, callback) {\n    if (typeof callback == \"function\") {\n      if (isError) {\n        callback(response, null);\n      } else {\n        callback(null, response);\n      }\n    }\n  }\n  function getResponseHeaderMap(xhr) {\n    var headers = {};\n    var responseHeaders = xhr.getAllResponseHeaders();\n    if (Object.keys(responseHeaders).length) {\n      responseHeaders.trim().split(/[\\r\\n]+/).map(function (value) {\n        return value.split(/: /);\n      }).forEach(function (keyValue) {\n        headers[keyValue[0].trim()] = keyValue[1].trim();\n      });\n    }\n    return headers;\n  }\n  var addResponseHeadersAndBody = function addResponseHeadersAndBody(body, xhr) {\n    var response = _objectSpread2$1({}, body);\n    var responseMetadata = {\n      statusCode: xhr.status,\n      headers: getResponseHeaderMap(xhr)\n    };\n    Object.defineProperty(response, \"$ResponseMetadata\", {\n      value: responseMetadata,\n      enumerable: false,\n      writable: false\n    });\n    return response;\n  };\n  var request = function request(uploadFileXHR, formData, callback) {\n    uploadFile(uploadFileXHR, formData).then(function (result) {\n      return respond(false, result, callback);\n    }, function (ex) {\n      return respond(true, ex, callback);\n    });\n  };\n  var uploadFile = function uploadFile(uploadFileXHR, formData) {\n    return new Promise(function (resolve, reject) {\n      uploadFileXHR.open('POST', 'https://upload.imagekit.io/api/v1/files/upload');\n      uploadFileXHR.onerror = function (e) {\n        return reject(errorMessages.UPLOAD_ENDPOINT_NETWORK_ERROR);\n      };\n      uploadFileXHR.onload = function () {\n        if (uploadFileXHR.status === 200) {\n          try {\n            var body = JSON.parse(uploadFileXHR.responseText);\n            var uploadResponse = addResponseHeadersAndBody(body, uploadFileXHR);\n            return resolve(uploadResponse);\n          } catch (ex) {\n            return reject(ex);\n          }\n        } else {\n          try {\n            var body = JSON.parse(uploadFileXHR.responseText);\n            var uploadError = addResponseHeadersAndBody(body, uploadFileXHR);\n            return reject(uploadError);\n          } catch (ex) {\n            return reject(ex);\n          }\n        }\n      };\n      uploadFileXHR.send(formData);\n    });\n  };\n  var upload = function upload(xhr, uploadOptions, options, callback) {\n    if (!uploadOptions.file) {\n      respond(true, errorMessages.MISSING_UPLOAD_FILE_PARAMETER, callback);\n      return;\n    }\n    if (!uploadOptions.fileName) {\n      respond(true, errorMessages.MISSING_UPLOAD_FILENAME_PARAMETER, callback);\n      return;\n    }\n    if (!options.publicKey) {\n      respond(true, errorMessages.MISSING_PUBLIC_KEY, callback);\n      return;\n    }\n    if (!uploadOptions.token) {\n      respond(true, errorMessages.MISSING_TOKEN, callback);\n      return;\n    }\n    if (!uploadOptions.signature) {\n      respond(true, errorMessages.MISSING_SIGNATURE, callback);\n      return;\n    }\n    if (!uploadOptions.expire) {\n      respond(true, errorMessages.MISSING_EXPIRE, callback);\n      return;\n    }\n    if (uploadOptions.transformation) {\n      if (!(Object.keys(uploadOptions.transformation).includes(\"pre\") || Object.keys(uploadOptions.transformation).includes(\"post\"))) {\n        respond(true, errorMessages.INVALID_TRANSFORMATION, callback);\n        return;\n      }\n      if (Object.keys(uploadOptions.transformation).includes(\"pre\") && !uploadOptions.transformation.pre) {\n        respond(true, errorMessages.INVALID_PRE_TRANSFORMATION, callback);\n        return;\n      }\n      if (Object.keys(uploadOptions.transformation).includes(\"post\")) {\n        if (Array.isArray(uploadOptions.transformation.post)) {\n          var _iterator = _createForOfIteratorHelper(uploadOptions.transformation.post),\n            _step;\n          try {\n            for (_iterator.s(); !(_step = _iterator.n()).done;) {\n              var transformation = _step.value;\n              if (transformation.type === \"abs\" && !(transformation.protocol || transformation.value)) {\n                respond(true, errorMessages.INVALID_POST_TRANSFORMATION, callback);\n                return;\n              } else if (transformation.type === \"transformation\" && !transformation.value) {\n                respond(true, errorMessages.INVALID_POST_TRANSFORMATION, callback);\n                return;\n              }\n            }\n          } catch (err) {\n            _iterator.e(err);\n          } finally {\n            _iterator.f();\n          }\n        } else {\n          respond(true, errorMessages.INVALID_POST_TRANSFORMATION, callback);\n          return;\n        }\n      }\n    }\n    var formData = new FormData();\n    var key;\n    for (key in uploadOptions) {\n      if (key) {\n        if (key === \"file\" && typeof uploadOptions.file != \"string\") {\n          formData.append('file', uploadOptions.file, String(uploadOptions.fileName));\n        } else if (key === \"tags\" && Array.isArray(uploadOptions.tags)) {\n          formData.append('tags', uploadOptions.tags.join(\",\"));\n        } else if (key === 'signature') {\n          formData.append(\"signature\", uploadOptions.signature);\n        } else if (key === 'expire') {\n          formData.append(\"expire\", String(uploadOptions.expire));\n        } else if (key === 'token') {\n          formData.append(\"token\", uploadOptions.token);\n        } else if (key === \"responseFields\" && Array.isArray(uploadOptions.responseFields)) {\n          formData.append('responseFields', uploadOptions.responseFields.join(\",\"));\n        } else if (key === \"extensions\" && Array.isArray(uploadOptions.extensions)) {\n          formData.append('extensions', JSON.stringify(uploadOptions.extensions));\n        } else if (key === \"customMetadata\" && _typeof$1(uploadOptions.customMetadata) === \"object\" && !Array.isArray(uploadOptions.customMetadata) && uploadOptions.customMetadata !== null) {\n          formData.append('customMetadata', JSON.stringify(uploadOptions.customMetadata));\n        } else if (key === \"transformation\" && _typeof$1(uploadOptions.transformation) === \"object\" && uploadOptions.transformation !== null) {\n          formData.append(key, JSON.stringify(uploadOptions.transformation));\n        } else if (key === 'checks' && uploadOptions.checks) {\n          formData.append(\"checks\", uploadOptions.checks);\n        } else if (uploadOptions[key] !== undefined) {\n          formData.append(key, String(uploadOptions[key]));\n        }\n      }\n    }\n    formData.append(\"publicKey\", options.publicKey);\n    request(xhr, formData, callback);\n  };\n  var supportedTransforms = {\n    width: \"w\",\n    height: \"h\",\n    aspectRatio: \"ar\",\n    quality: \"q\",\n    crop: \"c\",\n    cropMode: \"cm\",\n    focus: \"fo\",\n    x: \"x\",\n    y: \"y\",\n    format: \"f\",\n    radius: \"r\",\n    background: \"bg\",\n    border: \"b\",\n    rotation: \"rt\",\n    rotate: \"rt\",\n    blur: \"bl\",\n    named: \"n\",\n    progressive: \"pr\",\n    lossless: \"lo\",\n    trim: \"t\",\n    metadata: \"md\",\n    colorProfile: \"cp\",\n    defaultImage: \"di\",\n    dpr: \"dpr\",\n    effectSharpen: \"e-sharpen\",\n    effectUSM: \"e-usm\",\n    effectContrast: \"e-contrast\",\n    effectGray: \"e-grayscale\",\n    original: \"orig\",\n    effectShadow: \"e-shadow\",\n    effectGradient: \"e-gradient\",\n    raw: \"raw\"\n  };\n  var DEFAULT_TRANSFORMATION_POSITION = \"path\";\n  var QUERY_TRANSFORMATION_POSITION = \"query\";\n  var VALID_TRANSFORMATION_POSITIONS = [DEFAULT_TRANSFORMATION_POSITION, QUERY_TRANSFORMATION_POSITION];\n  var CHAIN_TRANSFORM_DELIMITER = \":\";\n  var TRANSFORM_DELIMITER = \",\";\n  var TRANSFORM_KEY_VALUE_DELIMITER = \"-\";\n  var transformationUtils = {\n    getDefault: function getDefault() {\n      return DEFAULT_TRANSFORMATION_POSITION;\n    },\n    addAsQueryParameter: function addAsQueryParameter(options) {\n      return options.transformationPosition === QUERY_TRANSFORMATION_POSITION;\n    },\n    validParameters: function validParameters(options) {\n      if (typeof options.transformationPosition == \"undefined\") return false;\n      return VALID_TRANSFORMATION_POSITIONS.indexOf(options.transformationPosition) != -1;\n    },\n    getTransformKey: function getTransformKey(transform) {\n      if (!transform) {\n        return \"\";\n      }\n      return supportedTransforms[transform] || supportedTransforms[transform.toLowerCase()] || \"\";\n    },\n    getChainTransformDelimiter: function getChainTransformDelimiter() {\n      return CHAIN_TRANSFORM_DELIMITER;\n    },\n    getTransformDelimiter: function getTransformDelimiter() {\n      return TRANSFORM_DELIMITER;\n    },\n    getTransformKeyValueDelimiter: function getTransformKeyValueDelimiter() {\n      return TRANSFORM_KEY_VALUE_DELIMITER;\n    }\n  };\n  var TRANSFORMATION_PARAMETER = \"tr\";\n  function removeTrailingSlash(str) {\n    if (typeof str == \"string\" && str[str.length - 1] == \"/\") {\n      str = str.substring(0, str.length - 1);\n    }\n    return str;\n  }\n  function removeLeadingSlash(str) {\n    if (typeof str == \"string\" && str[0] == \"/\") {\n      str = str.slice(1);\n    }\n    return str;\n  }\n  function pathJoin(parts, sep) {\n    var separator = sep || \"/\";\n    var replace = new RegExp(separator + \"{1,}\", \"g\");\n    return parts.join(separator).replace(replace, separator);\n  }\n  var buildURL = function buildURL(opts) {\n    if (!opts.path && !opts.src) {\n      return \"\";\n    }\n    var urlObj, isSrcParameterUsedForURL, urlEndpointPattern;\n    try {\n      if (opts.path) {\n        urlEndpointPattern = new URL(opts.urlEndpoint).pathname;\n        urlObj = new URL(pathJoin([opts.urlEndpoint.replace(urlEndpointPattern, \"\"), opts.path]));\n      } else {\n        urlObj = new URL(opts.src);\n        isSrcParameterUsedForURL = true;\n      }\n    } catch (e) {\n      console.error(e);\n      return \"\";\n    }\n    for (var i in opts.queryParameters) {\n      urlObj.searchParams.append(i, String(opts.queryParameters[i]));\n    }\n    var transformationString = constructTransformationString(opts.transformation);\n    if (transformationString && transformationString.length) {\n      if (transformationUtils.addAsQueryParameter(opts) || isSrcParameterUsedForURL) {\n        urlObj.searchParams.append(TRANSFORMATION_PARAMETER, transformationString);\n      } else {\n        urlObj.pathname = pathJoin([TRANSFORMATION_PARAMETER + transformationUtils.getChainTransformDelimiter() + transformationString, urlObj.pathname]);\n      }\n    }\n    if (urlEndpointPattern) {\n      urlObj.pathname = pathJoin([urlEndpointPattern, urlObj.pathname]);\n    } else {\n      urlObj.pathname = pathJoin([urlObj.pathname]);\n    }\n    return urlObj.href;\n  };\n  function constructTransformationString(transformation) {\n    if (!Array.isArray(transformation)) {\n      return \"\";\n    }\n    var parsedTransforms = [];\n    for (var i = 0, l = transformation.length; i < l; i++) {\n      var parsedTransformStep = [];\n      for (var key in transformation[i]) {\n        if (transformation[i][key] === undefined || transformation[i][key] === null) continue;\n        var transformKey = transformationUtils.getTransformKey(key);\n        if (!transformKey) {\n          transformKey = key;\n        }\n        if (transformation[i][key] === \"-\") {\n          parsedTransformStep.push(transformKey);\n        } else if (key === \"raw\") {\n          parsedTransformStep.push(transformation[i][key]);\n        } else {\n          var value = transformation[i][key];\n          if (transformKey === \"di\") {\n            value = removeTrailingSlash(removeLeadingSlash(value || \"\"));\n            value = value.replace(/\\//g, \"@@\");\n          }\n          parsedTransformStep.push([transformKey, value].join(transformationUtils.getTransformKeyValueDelimiter()));\n        }\n      }\n      parsedTransforms.push(parsedTransformStep.join(transformationUtils.getTransformDelimiter()));\n    }\n    return parsedTransforms.join(transformationUtils.getChainTransformDelimiter());\n  }\n  var url = function url(urlOpts, defaultOptions) {\n    return buildURL(_objectSpread2$1(_objectSpread2$1({}, defaultOptions), urlOpts));\n  };\n  function mandatoryParametersAvailable(options) {\n    return options.urlEndpoint;\n  }\n  var promisify = function promisify(thisContext, fn) {\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      if (args.length === fn.length && typeof args[args.length - 1] !== \"undefined\") {\n        if (typeof args[args.length - 1] !== \"function\") {\n          throw new Error(\"Callback must be a function.\");\n        }\n        fn.call.apply(fn, [thisContext].concat(args));\n      } else {\n        return new Promise(function (resolve, reject) {\n          var callback = function callback(err) {\n            if (err) {\n              return reject(err);\n            } else {\n              for (var _len2 = arguments.length, results = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n                results[_key2 - 1] = arguments[_key2];\n              }\n              resolve(results.length > 1 ? results : results[0]);\n            }\n          };\n          args.pop();\n          args.push(callback);\n          fn.call.apply(fn, [thisContext].concat(args));\n        });\n      }\n    };\n  };\n  var ImageKit = function () {\n    function ImageKit(opts) {\n      _classCallCheck(this, ImageKit);\n      _defineProperty$1(this, \"options\", {\n        sdkVersion: \"javascript-\".concat(version),\n        publicKey: \"\",\n        urlEndpoint: \"\",\n        transformationPosition: transformationUtils.getDefault()\n      });\n      this.options = _objectSpread2$1(_objectSpread2$1({}, this.options), opts || {});\n      if (!mandatoryParametersAvailable(this.options)) {\n        throw errorMessages.MANDATORY_INITIALIZATION_MISSING;\n      }\n      if (!transformationUtils.validParameters(this.options)) {\n        throw errorMessages.INVALID_TRANSFORMATION_POSITION;\n      }\n    }\n    _createClass(ImageKit, [{\n      key: \"url\",\n      value: function url$1(urlOptions) {\n        return url(urlOptions, this.options);\n      }\n    }, {\n      key: \"upload\",\n      value: function upload$1(uploadOptions, callbackOrOptions, options) {\n        var callback;\n        if (typeof callbackOrOptions === 'function') {\n          callback = callbackOrOptions;\n        } else {\n          options = callbackOrOptions || {};\n        }\n        if (!uploadOptions || _typeof$1(uploadOptions) !== \"object\") {\n          return respond(true, errorMessages.INVALID_UPLOAD_OPTIONS, callback);\n        }\n        var mergedOptions = _objectSpread2$1(_objectSpread2$1({}, this.options), options);\n        var _ref = uploadOptions || {},\n          userProvidedXHR = _ref.xhr;\n        delete uploadOptions.xhr;\n        var xhr = userProvidedXHR || new XMLHttpRequest();\n        return promisify(this, upload)(xhr, uploadOptions, mergedOptions, callback);\n      }\n    }]);\n    return ImageKit;\n  }();\n\n  function _iterableToArrayLimit(r, l) {\n    var t = null == r ? null : \"undefined\" != typeof Symbol && r[Symbol.iterator] || r[\"@@iterator\"];\n    if (null != t) {\n      var e,\n        n,\n        i,\n        u,\n        a = [],\n        f = !0,\n        o = !1;\n      try {\n        if (i = (t = t.call(r)).next, 0 === l) {\n          if (Object(t) !== t) return;\n          f = !1;\n        } else for (; !(f = (e = i.call(t)).done) && (a.push(e.value), a.length !== l); f = !0);\n      } catch (r) {\n        o = !0, n = r;\n      } finally {\n        try {\n          if (!f && null != t.return && (u = t.return(), Object(u) !== u)) return;\n        } finally {\n          if (o) throw n;\n        }\n      }\n      return a;\n    }\n  }\n  function ownKeys(e, r) {\n    var t = Object.keys(e);\n    if (Object.getOwnPropertySymbols) {\n      var o = Object.getOwnPropertySymbols(e);\n      r && (o = o.filter(function (r) {\n        return Object.getOwnPropertyDescriptor(e, r).enumerable;\n      })), t.push.apply(t, o);\n    }\n    return t;\n  }\n  function _objectSpread2(e) {\n    for (var r = 1; r < arguments.length; r++) {\n      var t = null != arguments[r] ? arguments[r] : {};\n      r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n        _defineProperty(e, r, t[r]);\n      }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n        Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n      });\n    }\n    return e;\n  }\n  function _regeneratorRuntime() {\n    _regeneratorRuntime = function () {\n      return e;\n    };\n    var t,\n      e = {},\n      r = Object.prototype,\n      n = r.hasOwnProperty,\n      o = Object.defineProperty || function (t, e, r) {\n        t[e] = r.value;\n      },\n      i = \"function\" == typeof Symbol ? Symbol : {},\n      a = i.iterator || \"@@iterator\",\n      c = i.asyncIterator || \"@@asyncIterator\",\n      u = i.toStringTag || \"@@toStringTag\";\n    function define(t, e, r) {\n      return Object.defineProperty(t, e, {\n        value: r,\n        enumerable: !0,\n        configurable: !0,\n        writable: !0\n      }), t[e];\n    }\n    try {\n      define({}, \"\");\n    } catch (t) {\n      define = function (t, e, r) {\n        return t[e] = r;\n      };\n    }\n    function wrap(t, e, r, n) {\n      var i = e && e.prototype instanceof Generator ? e : Generator,\n        a = Object.create(i.prototype),\n        c = new Context(n || []);\n      return o(a, \"_invoke\", {\n        value: makeInvokeMethod(t, r, c)\n      }), a;\n    }\n    function tryCatch(t, e, r) {\n      try {\n        return {\n          type: \"normal\",\n          arg: t.call(e, r)\n        };\n      } catch (t) {\n        return {\n          type: \"throw\",\n          arg: t\n        };\n      }\n    }\n    e.wrap = wrap;\n    var h = \"suspendedStart\",\n      l = \"suspendedYield\",\n      f = \"executing\",\n      s = \"completed\",\n      y = {};\n    function Generator() {}\n    function GeneratorFunction() {}\n    function GeneratorFunctionPrototype() {}\n    var p = {};\n    define(p, a, function () {\n      return this;\n    });\n    var d = Object.getPrototypeOf,\n      v = d && d(d(values([])));\n    v && v !== r && n.call(v, a) && (p = v);\n    var g = GeneratorFunctionPrototype.prototype = Generator.prototype = Object.create(p);\n    function defineIteratorMethods(t) {\n      [\"next\", \"throw\", \"return\"].forEach(function (e) {\n        define(t, e, function (t) {\n          return this._invoke(e, t);\n        });\n      });\n    }\n    function AsyncIterator(t, e) {\n      function invoke(r, o, i, a) {\n        var c = tryCatch(t[r], t, o);\n        if (\"throw\" !== c.type) {\n          var u = c.arg,\n            h = u.value;\n          return h && \"object\" == typeof h && n.call(h, \"__await\") ? e.resolve(h.__await).then(function (t) {\n            invoke(\"next\", t, i, a);\n          }, function (t) {\n            invoke(\"throw\", t, i, a);\n          }) : e.resolve(h).then(function (t) {\n            u.value = t, i(u);\n          }, function (t) {\n            return invoke(\"throw\", t, i, a);\n          });\n        }\n        a(c.arg);\n      }\n      var r;\n      o(this, \"_invoke\", {\n        value: function (t, n) {\n          function callInvokeWithMethodAndArg() {\n            return new e(function (e, r) {\n              invoke(t, n, e, r);\n            });\n          }\n          return r = r ? r.then(callInvokeWithMethodAndArg, callInvokeWithMethodAndArg) : callInvokeWithMethodAndArg();\n        }\n      });\n    }\n    function makeInvokeMethod(e, r, n) {\n      var o = h;\n      return function (i, a) {\n        if (o === f) throw new Error(\"Generator is already running\");\n        if (o === s) {\n          if (\"throw\" === i) throw a;\n          return {\n            value: t,\n            done: !0\n          };\n        }\n        for (n.method = i, n.arg = a;;) {\n          var c = n.delegate;\n          if (c) {\n            var u = maybeInvokeDelegate(c, n);\n            if (u) {\n              if (u === y) continue;\n              return u;\n            }\n          }\n          if (\"next\" === n.method) n.sent = n._sent = n.arg;else if (\"throw\" === n.method) {\n            if (o === h) throw o = s, n.arg;\n            n.dispatchException(n.arg);\n          } else \"return\" === n.method && n.abrupt(\"return\", n.arg);\n          o = f;\n          var p = tryCatch(e, r, n);\n          if (\"normal\" === p.type) {\n            if (o = n.done ? s : l, p.arg === y) continue;\n            return {\n              value: p.arg,\n              done: n.done\n            };\n          }\n          \"throw\" === p.type && (o = s, n.method = \"throw\", n.arg = p.arg);\n        }\n      };\n    }\n    function maybeInvokeDelegate(e, r) {\n      var n = r.method,\n        o = e.iterator[n];\n      if (o === t) return r.delegate = null, \"throw\" === n && e.iterator.return && (r.method = \"return\", r.arg = t, maybeInvokeDelegate(e, r), \"throw\" === r.method) || \"return\" !== n && (r.method = \"throw\", r.arg = new TypeError(\"The iterator does not provide a '\" + n + \"' method\")), y;\n      var i = tryCatch(o, e.iterator, r.arg);\n      if (\"throw\" === i.type) return r.method = \"throw\", r.arg = i.arg, r.delegate = null, y;\n      var a = i.arg;\n      return a ? a.done ? (r[e.resultName] = a.value, r.next = e.nextLoc, \"return\" !== r.method && (r.method = \"next\", r.arg = t), r.delegate = null, y) : a : (r.method = \"throw\", r.arg = new TypeError(\"iterator result is not an object\"), r.delegate = null, y);\n    }\n    function pushTryEntry(t) {\n      var e = {\n        tryLoc: t[0]\n      };\n      1 in t && (e.catchLoc = t[1]), 2 in t && (e.finallyLoc = t[2], e.afterLoc = t[3]), this.tryEntries.push(e);\n    }\n    function resetTryEntry(t) {\n      var e = t.completion || {};\n      e.type = \"normal\", delete e.arg, t.completion = e;\n    }\n    function Context(t) {\n      this.tryEntries = [{\n        tryLoc: \"root\"\n      }], t.forEach(pushTryEntry, this), this.reset(!0);\n    }\n    function values(e) {\n      if (e || \"\" === e) {\n        var r = e[a];\n        if (r) return r.call(e);\n        if (\"function\" == typeof e.next) return e;\n        if (!isNaN(e.length)) {\n          var o = -1,\n            i = function next() {\n              for (; ++o < e.length;) if (n.call(e, o)) return next.value = e[o], next.done = !1, next;\n              return next.value = t, next.done = !0, next;\n            };\n          return i.next = i;\n        }\n      }\n      throw new TypeError(typeof e + \" is not iterable\");\n    }\n    return GeneratorFunction.prototype = GeneratorFunctionPrototype, o(g, \"constructor\", {\n      value: GeneratorFunctionPrototype,\n      configurable: !0\n    }), o(GeneratorFunctionPrototype, \"constructor\", {\n      value: GeneratorFunction,\n      configurable: !0\n    }), GeneratorFunction.displayName = define(GeneratorFunctionPrototype, u, \"GeneratorFunction\"), e.isGeneratorFunction = function (t) {\n      var e = \"function\" == typeof t && t.constructor;\n      return !!e && (e === GeneratorFunction || \"GeneratorFunction\" === (e.displayName || e.name));\n    }, e.mark = function (t) {\n      return Object.setPrototypeOf ? Object.setPrototypeOf(t, GeneratorFunctionPrototype) : (t.__proto__ = GeneratorFunctionPrototype, define(t, u, \"GeneratorFunction\")), t.prototype = Object.create(g), t;\n    }, e.awrap = function (t) {\n      return {\n        __await: t\n      };\n    }, defineIteratorMethods(AsyncIterator.prototype), define(AsyncIterator.prototype, c, function () {\n      return this;\n    }), e.AsyncIterator = AsyncIterator, e.async = function (t, r, n, o, i) {\n      void 0 === i && (i = Promise);\n      var a = new AsyncIterator(wrap(t, r, n, o), i);\n      return e.isGeneratorFunction(r) ? a : a.next().then(function (t) {\n        return t.done ? t.value : a.next();\n      });\n    }, defineIteratorMethods(g), define(g, u, \"Generator\"), define(g, a, function () {\n      return this;\n    }), define(g, \"toString\", function () {\n      return \"[object Generator]\";\n    }), e.keys = function (t) {\n      var e = Object(t),\n        r = [];\n      for (var n in e) r.push(n);\n      return r.reverse(), function next() {\n        for (; r.length;) {\n          var t = r.pop();\n          if (t in e) return next.value = t, next.done = !1, next;\n        }\n        return next.done = !0, next;\n      };\n    }, e.values = values, Context.prototype = {\n      constructor: Context,\n      reset: function (e) {\n        if (this.prev = 0, this.next = 0, this.sent = this._sent = t, this.done = !1, this.delegate = null, this.method = \"next\", this.arg = t, this.tryEntries.forEach(resetTryEntry), !e) for (var r in this) \"t\" === r.charAt(0) && n.call(this, r) && !isNaN(+r.slice(1)) && (this[r] = t);\n      },\n      stop: function () {\n        this.done = !0;\n        var t = this.tryEntries[0].completion;\n        if (\"throw\" === t.type) throw t.arg;\n        return this.rval;\n      },\n      dispatchException: function (e) {\n        if (this.done) throw e;\n        var r = this;\n        function handle(n, o) {\n          return a.type = \"throw\", a.arg = e, r.next = n, o && (r.method = \"next\", r.arg = t), !!o;\n        }\n        for (var o = this.tryEntries.length - 1; o >= 0; --o) {\n          var i = this.tryEntries[o],\n            a = i.completion;\n          if (\"root\" === i.tryLoc) return handle(\"end\");\n          if (i.tryLoc <= this.prev) {\n            var c = n.call(i, \"catchLoc\"),\n              u = n.call(i, \"finallyLoc\");\n            if (c && u) {\n              if (this.prev < i.catchLoc) return handle(i.catchLoc, !0);\n              if (this.prev < i.finallyLoc) return handle(i.finallyLoc);\n            } else if (c) {\n              if (this.prev < i.catchLoc) return handle(i.catchLoc, !0);\n            } else {\n              if (!u) throw new Error(\"try statement without catch or finally\");\n              if (this.prev < i.finallyLoc) return handle(i.finallyLoc);\n            }\n          }\n        }\n      },\n      abrupt: function (t, e) {\n        for (var r = this.tryEntries.length - 1; r >= 0; --r) {\n          var o = this.tryEntries[r];\n          if (o.tryLoc <= this.prev && n.call(o, \"finallyLoc\") && this.prev < o.finallyLoc) {\n            var i = o;\n            break;\n          }\n        }\n        i && (\"break\" === t || \"continue\" === t) && i.tryLoc <= e && e <= i.finallyLoc && (i = null);\n        var a = i ? i.completion : {};\n        return a.type = t, a.arg = e, i ? (this.method = \"next\", this.next = i.finallyLoc, y) : this.complete(a);\n      },\n      complete: function (t, e) {\n        if (\"throw\" === t.type) throw t.arg;\n        return \"break\" === t.type || \"continue\" === t.type ? this.next = t.arg : \"return\" === t.type ? (this.rval = this.arg = t.arg, this.method = \"return\", this.next = \"end\") : \"normal\" === t.type && e && (this.next = e), y;\n      },\n      finish: function (t) {\n        for (var e = this.tryEntries.length - 1; e >= 0; --e) {\n          var r = this.tryEntries[e];\n          if (r.finallyLoc === t) return this.complete(r.completion, r.afterLoc), resetTryEntry(r), y;\n        }\n      },\n      catch: function (t) {\n        for (var e = this.tryEntries.length - 1; e >= 0; --e) {\n          var r = this.tryEntries[e];\n          if (r.tryLoc === t) {\n            var n = r.completion;\n            if (\"throw\" === n.type) {\n              var o = n.arg;\n              resetTryEntry(r);\n            }\n            return o;\n          }\n        }\n        throw new Error(\"illegal catch attempt\");\n      },\n      delegateYield: function (e, r, n) {\n        return this.delegate = {\n          iterator: values(e),\n          resultName: r,\n          nextLoc: n\n        }, \"next\" === this.method && (this.arg = t), y;\n      }\n    }, e;\n  }\n  function _toPrimitive(t, r) {\n    if (\"object\" != typeof t || !t) return t;\n    var e = t[Symbol.toPrimitive];\n    if (void 0 !== e) {\n      var i = e.call(t, r || \"default\");\n      if (\"object\" != typeof i) return i;\n      throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n    }\n    return (\"string\" === r ? String : Number)(t);\n  }\n  function _toPropertyKey(t) {\n    var i = _toPrimitive(t, \"string\");\n    return \"symbol\" == typeof i ? i : String(i);\n  }\n  function _typeof(o) {\n    \"@babel/helpers - typeof\";\n\n    return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (o) {\n      return typeof o;\n    } : function (o) {\n      return o && \"function\" == typeof Symbol && o.constructor === Symbol && o !== Symbol.prototype ? \"symbol\" : typeof o;\n    }, _typeof(o);\n  }\n  function asyncGeneratorStep(gen, resolve, reject, _next, _throw, key, arg) {\n    try {\n      var info = gen[key](arg);\n      var value = info.value;\n    } catch (error) {\n      reject(error);\n      return;\n    }\n    if (info.done) {\n      resolve(value);\n    } else {\n      Promise.resolve(value).then(_next, _throw);\n    }\n  }\n  function _asyncToGenerator(fn) {\n    return function () {\n      var self = this,\n        args = arguments;\n      return new Promise(function (resolve, reject) {\n        var gen = fn.apply(self, args);\n        function _next(value) {\n          asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"next\", value);\n        }\n        function _throw(err) {\n          asyncGeneratorStep(gen, resolve, reject, _next, _throw, \"throw\", err);\n        }\n        _next(undefined);\n      });\n    };\n  }\n  function _defineProperty(obj, key, value) {\n    key = _toPropertyKey(key);\n    if (key in obj) {\n      Object.defineProperty(obj, key, {\n        value: value,\n        enumerable: true,\n        configurable: true,\n        writable: true\n      });\n    } else {\n      obj[key] = value;\n    }\n    return obj;\n  }\n  function _extends() {\n    _extends = Object.assign ? Object.assign.bind() : function (target) {\n      for (var i = 1; i < arguments.length; i++) {\n        var source = arguments[i];\n        for (var key in source) {\n          if (Object.prototype.hasOwnProperty.call(source, key)) {\n            target[key] = source[key];\n          }\n        }\n      }\n      return target;\n    };\n    return _extends.apply(this, arguments);\n  }\n  function _objectWithoutPropertiesLoose(source, excluded) {\n    if (source == null) return {};\n    var target = {};\n    var sourceKeys = Object.keys(source);\n    var key, i;\n    for (i = 0; i < sourceKeys.length; i++) {\n      key = sourceKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      target[key] = source[key];\n    }\n    return target;\n  }\n  function _objectWithoutProperties(source, excluded) {\n    if (source == null) return {};\n    var target = _objectWithoutPropertiesLoose(source, excluded);\n    var key, i;\n    if (Object.getOwnPropertySymbols) {\n      var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n      for (i = 0; i < sourceSymbolKeys.length; i++) {\n        key = sourceSymbolKeys[i];\n        if (excluded.indexOf(key) >= 0) continue;\n        if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n        target[key] = source[key];\n      }\n    }\n    return target;\n  }\n  function _slicedToArray(arr, i) {\n    return _arrayWithHoles(arr) || _iterableToArrayLimit(arr, i) || _unsupportedIterableToArray(arr, i) || _nonIterableRest();\n  }\n  function _toConsumableArray(arr) {\n    return _arrayWithoutHoles(arr) || _iterableToArray(arr) || _unsupportedIterableToArray(arr) || _nonIterableSpread();\n  }\n  function _arrayWithoutHoles(arr) {\n    if (Array.isArray(arr)) return _arrayLikeToArray(arr);\n  }\n  function _arrayWithHoles(arr) {\n    if (Array.isArray(arr)) return arr;\n  }\n  function _iterableToArray(iter) {\n    if (typeof Symbol !== \"undefined\" && iter[Symbol.iterator] != null || iter[\"@@iterator\"] != null) return Array.from(iter);\n  }\n  function _unsupportedIterableToArray(o, minLen) {\n    if (!o) return;\n    if (typeof o === \"string\") return _arrayLikeToArray(o, minLen);\n    var n = Object.prototype.toString.call(o).slice(8, -1);\n    if (n === \"Object\" && o.constructor) n = o.constructor.name;\n    if (n === \"Map\" || n === \"Set\") return Array.from(o);\n    if (n === \"Arguments\" || /^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)) return _arrayLikeToArray(o, minLen);\n  }\n  function _arrayLikeToArray(arr, len) {\n    if (len == null || len > arr.length) len = arr.length;\n    for (var i = 0, arr2 = new Array(len); i < len; i++) arr2[i] = arr[i];\n    return arr2;\n  }\n  function _nonIterableSpread() {\n    throw new TypeError(\"Invalid attempt to spread non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n  function _nonIterableRest() {\n    throw new TypeError(\"Invalid attempt to destructure non-iterable instance.\\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.\");\n  }\n\n  function createCommonjsModule(fn, basedir, module) {\n  \treturn module = {\n  \t\tpath: basedir,\n  \t\texports: {},\n  \t\trequire: function (path, base) {\n  \t\t\treturn commonjsRequire(path, (base === undefined || base === null) ? module.path : base);\n  \t\t}\n  \t}, fn(module, module.exports), module.exports;\n  }\n\n  function commonjsRequire () {\n  \tthrow new Error('Dynamic requires are not currently supported by @rollup/plugin-commonjs');\n  }\n\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  var ReactPropTypesSecret$1 = 'SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED';\n  var ReactPropTypesSecret_1 = ReactPropTypesSecret$1;\n\n  var ReactPropTypesSecret = ReactPropTypesSecret_1;\n\n  function emptyFunction() {}\n  function emptyFunctionWithReset() {}\n  emptyFunctionWithReset.resetWarningCache = emptyFunction;\n  var factoryWithThrowingShims = function () {\n    function shim(props, propName, componentName, location, propFullName, secret) {\n      if (secret === ReactPropTypesSecret) {\n        // It is still safe when called from React.\n        return;\n      }\n      var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use PropTypes.checkPropTypes() to call them. ' + 'Read more at http://fb.me/use-check-prop-types');\n      err.name = 'Invariant Violation';\n      throw err;\n    }\n    shim.isRequired = shim;\n    function getShim() {\n      return shim;\n    }\n    // Important!\n    // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n    var ReactPropTypes = {\n      array: shim,\n      bigint: shim,\n      bool: shim,\n      func: shim,\n      number: shim,\n      object: shim,\n      string: shim,\n      symbol: shim,\n      any: shim,\n      arrayOf: getShim,\n      element: shim,\n      elementType: shim,\n      instanceOf: getShim,\n      node: shim,\n      objectOf: getShim,\n      oneOf: getShim,\n      oneOfType: getShim,\n      shape: getShim,\n      exact: getShim,\n      checkPropTypes: emptyFunctionWithReset,\n      resetWarningCache: emptyFunction\n    };\n    ReactPropTypes.PropTypes = ReactPropTypes;\n    return ReactPropTypes;\n  };\n\n  var require$$0 = factoryWithThrowingShims;\n\n  var propTypes = createCommonjsModule(function (module) {\n  /**\n   * Copyright (c) 2013-present, Facebook, Inc.\n   *\n   * This source code is licensed under the MIT license found in the\n   * LICENSE file in the root directory of this source tree.\n   */\n\n  {\n    // By explicitly using `prop-types` you are opting into new production behavior.\n    // http://fb.me/prop-types-in-prod\n    module.exports = require$$0();\n  }\n  });\n\n  var PropTypes = propTypes;\n\n  var Props$2 = {\n    publicKey: PropTypes.string,\n    urlEndpoint: PropTypes.string,\n    authenticator: PropTypes.func\n  };\n  var IKContextProps = _objectSpread2(_objectSpread2({}, Props$2), {}, {\n    transformationPosition: PropTypes.oneOf(['path', 'query'])\n  });\n  var IKContextExtractedProps = _objectSpread2(_objectSpread2({}, IKContextProps), {}, {\n    ikClient: PropTypes.instanceOf(ImageKit)\n  });\n\n  // Create the context\n  var ImageKitContext = /*#__PURE__*/React.createContext({});\n\n  /**\n   * Provides a container for ImageKit components. Any option set in IKContext will be passed to the children.\n   *\n   * @example\n   *<IKContext  publicKey=\"<public key>\" urlEndpoint=\"url link\">\n   *    <!-- other tags -->\n   *    <Image src={link}/>\n   *</IKContext>\n   */\n  var IKContext = function IKContext(props) {\n    var extractContextOptions = function extractContextOptions(mergedOptions) {\n      var result = {};\n      var propKeys = Object.keys(IKContextExtractedProps);\n      for (var i = 0; i < propKeys.length; i++) {\n        var key = propKeys[i];\n        var value = mergedOptions[key];\n        if (value) {\n          result[key] = value;\n        }\n      }\n      return result;\n    };\n    var mergedOptions = _objectSpread2({}, props);\n    var contextOptionsExtracted = extractContextOptions(mergedOptions);\n    if (contextOptionsExtracted.urlEndpoint && contextOptionsExtracted.urlEndpoint.trim() !== \"\") {\n      contextOptionsExtracted.ikClient = new ImageKit({\n        urlEndpoint: contextOptionsExtracted.urlEndpoint,\n        // @ts-ignore\n        sdkVersion: \"\"\n      });\n    }\n    return /*#__PURE__*/React__default[\"default\"].createElement(ImageKitContext.Provider, {\n      value: contextOptionsExtracted\n    }, props.children);\n  };\n\n  var Props$1 = {\n    loading: PropTypes.oneOf(['lazy']),\n    lqip: PropTypes.shape({\n      active: PropTypes.bool,\n      quality: PropTypes.number,\n      threshold: PropTypes.number,\n      blur: PropTypes.number,\n      raw: PropTypes.string\n    }),\n    path: PropTypes.string,\n    src: PropTypes.string,\n    queryParameters: PropTypes.objectOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired),\n    transformation: PropTypes.arrayOf(PropTypes.object.isRequired),\n    transformationPosition: PropTypes.oneOf(['path', 'query'])\n  };\n\n  var COMBINED_IMAGE_PROP_TYPES$1 = _objectSpread2(_objectSpread2({}, Props$2), Props$1);\n\n  var fetchEffectiveConnection = function fetchEffectiveConnection() {\n    try {\n      return navigator.connection.effectiveType;\n    } catch (ex) {\n      return \"4g\";\n    }\n  };\n  var getSrc = function getSrc(_ref, ikClient, contextOptions) {\n    var urlEndpoint = _ref.urlEndpoint,\n      lqip = _ref.lqip,\n      src = _ref.src,\n      path = _ref.path,\n      transformation = _ref.transformation,\n      transformationPosition = _ref.transformationPosition,\n      queryParameters = _ref.queryParameters;\n    var options;\n    if (src) {\n      options = {\n        urlEndpoint: urlEndpoint || contextOptions.urlEndpoint,\n        src: src,\n        transformation: transformation || undefined,\n        transformationPosition: transformationPosition || contextOptions.transformationPosition || undefined,\n        queryParameters: queryParameters || {}\n      };\n    } else if (path) {\n      options = {\n        urlEndpoint: urlEndpoint || contextOptions.urlEndpoint,\n        path: path,\n        transformation: transformation || undefined,\n        transformationPosition: transformationPosition || contextOptions.transformationPosition || undefined,\n        queryParameters: queryParameters || {}\n      };\n    } else return {\n      originalSrc: \"\"\n    };\n    var result = {\n      originalSrc: ikClient.url(options)\n    };\n    if (lqip && lqip.active) {\n      var quality = Math.round(lqip.quality || lqip.threshold || 20);\n      var blur = Math.round(lqip.blur || 6);\n      var newTransformation = options.transformation ? _toConsumableArray(options.transformation) : [];\n      if (lqip.raw && typeof lqip.raw === \"string\" && lqip.raw.trim() !== \"\") {\n        newTransformation.push({\n          raw: lqip.raw.trim()\n        });\n      } else {\n        newTransformation.push({\n          quality: String(quality),\n          blur: String(blur)\n        });\n      }\n      result.lqipSrc = ikClient.url(_objectSpread2(_objectSpread2({}, options), {}, {\n        transformation: newTransformation\n      }));\n    }\n    return result;\n  };\n  var getIKElementsUrl = function getIKElementsUrl(_ref2, _ref3) {\n    var _ref2$lqip = _ref2.lqip,\n      lqip = _ref2$lqip === void 0 ? null : _ref2$lqip,\n      loading = _ref2.loading;\n    var intersected = _ref3.intersected,\n      originalSrcLoaded = _ref3.originalSrcLoaded,\n      originalSrc = _ref3.originalSrc,\n      lqipSrc = _ref3.lqipSrc;\n    /*\n      No lazy loading no lqip\n        src=originalImage\n      No lazy loading lqip\n        src=lqip\n        src=originalImage (when loaded)\n      lazy loading and no lqip\n        src=''\n        onIntersect:\n        src=originalImage\n      lazy loading and lqip\n        src=lqip\n        onIntersect:\n        src=originalImage (when loaded)\n    */\n    var isLqipActive = function isLqipActive(lqip) {\n      return lqip && lqip.active;\n    };\n    if (loading !== \"lazy\" && !isLqipActive(lqip)) {\n      return originalSrc;\n    } else if (loading !== \"lazy\" && isLqipActive(lqip)) {\n      if (originalSrcLoaded) {\n        return originalSrc;\n      } else {\n        return lqipSrc;\n      }\n    } else if (loading === \"lazy\" && !isLqipActive(lqip)) {\n      if (intersected) {\n        return originalSrc;\n      } else {\n        return \"\";\n      }\n    } else {\n      //  if (loading === \"lazy\" && isLqipActive(lqip))\n      if (intersected && originalSrcLoaded) {\n        return originalSrc;\n      } else {\n        return lqipSrc;\n      }\n    }\n  };\n\n  var useImageKitComponent = function useImageKitComponent(props) {\n    var contextOptions = React.useContext(ImageKitContext);\n    var getIKClient = function getIKClient() {\n      if (contextOptions && contextOptions.ikClient) {\n        return contextOptions.ikClient;\n      }\n      var urlEndpoint = props.urlEndpoint;\n      urlEndpoint = urlEndpoint || contextOptions && contextOptions.urlEndpoint;\n      if (!urlEndpoint || urlEndpoint.trim() === \"\") {\n        throw new Error(\"Missing urlEndpoint during initialization\");\n      }\n      var ikClient = new ImageKit({\n        urlEndpoint: urlEndpoint,\n        // @ts-ignore\n        sdkVersion: \"\"\n      });\n      return ikClient;\n    };\n    return {\n      getIKClient: getIKClient\n    };\n  };\n\n  var _excluded$2 = [\"urlEndpoint\", \"authenticator\", \"publicKey\", \"loading\", \"lqip\", \"path\", \"src\", \"transformation\", \"transformationPosition\", \"queryParameters\"];\n  var IKImage = function IKImage(props) {\n    var imageRef = React.useRef(null);\n    var _useImageKitComponent = useImageKitComponent(_objectSpread2({}, props)),\n      getIKClient = _useImageKitComponent.getIKClient;\n    var contextOptions = React.useContext(ImageKitContext);\n    var _useState = React.useState(undefined),\n      _useState2 = _slicedToArray(_useState, 2),\n      currentUrl = _useState2[0],\n      setCurrentUrl = _useState2[1];\n    var _useState3 = React.useState(\"\"),\n      _useState4 = _slicedToArray(_useState3, 2),\n      originalSrc = _useState4[0],\n      setOriginalSrc = _useState4[1];\n    var _useState5 = React.useState(\"\"),\n      _useState6 = _slicedToArray(_useState5, 2),\n      lqipSrc = _useState6[0],\n      setLqipSrc = _useState6[1];\n    var _useState7 = React.useState(false),\n      _useState8 = _slicedToArray(_useState7, 2),\n      originalSrcLoaded = _useState8[0],\n      setOriginalSrcLoaded = _useState8[1];\n    var _useState9 = React.useState(undefined),\n      _useState10 = _slicedToArray(_useState9, 2),\n      observe = _useState10[0],\n      setObserve = _useState10[1];\n    var _useState11 = React.useState(false),\n      _useState12 = _slicedToArray(_useState11, 2),\n      initialized = _useState12[0],\n      setInitialized = _useState12[1];\n    var _useState13 = React.useState(false),\n      _useState14 = _slicedToArray(_useState13, 2),\n      intersected = _useState14[0],\n      setIntersected = _useState14[1];\n    React.useEffect(function () {\n      var _getSrc = getSrc(props, getIKClient(), contextOptions),\n        newOriginalSrc = _getSrc.originalSrc,\n        newLqipSrc = _getSrc.lqipSrc;\n      setOriginalSrc(newOriginalSrc);\n      setLqipSrc(newLqipSrc ? newLqipSrc : '');\n      setInitialized(true);\n    }, [contextOptions, props]);\n    var updateImageUrl = /*#__PURE__*/function () {\n      var _ref = _asyncToGenerator( /*#__PURE__*/_regeneratorRuntime().mark(function _callee() {\n        var url;\n        return _regeneratorRuntime().wrap(function _callee$(_context) {\n          while (1) switch (_context.prev = _context.next) {\n            case 0:\n              _context.next = 2;\n              return getIKElementsUrl(props, {\n                originalSrc: originalSrc,\n                lqipSrc: lqipSrc,\n                intersected: intersected,\n                contextOptions: contextOptions,\n                initialzeState: initialized,\n                originalSrcLoaded: originalSrcLoaded,\n                observe: observe\n              });\n            case 2:\n              url = _context.sent;\n              // Include intersected state\n              if (url) {\n                setCurrentUrl(url);\n              }\n            case 4:\n            case \"end\":\n              return _context.stop();\n          }\n        }, _callee);\n      }));\n      return function updateImageUrl() {\n        return _ref.apply(this, arguments);\n      };\n    }();\n    var triggerOriginalImageLoad = function triggerOriginalImageLoad() {\n      var img = new Image();\n      img.onload = function () {\n        setOriginalSrcLoaded(true);\n      };\n      img.src = originalSrc;\n    };\n    React.useEffect(function () {\n      if (originalSrcLoaded) updateImageUrl();\n    }, [originalSrcLoaded]);\n    React.useEffect(function () {\n      var image = imageRef.current;\n      var loading = props.loading;\n      if (initialized) {\n        if (window && 'IntersectionObserver' in window && loading === \"lazy\") {\n          var connectionType = fetchEffectiveConnection();\n          var rootMargin = \"1250px\";\n          if (connectionType !== \"4g\") rootMargin = \"2500px\";\n          var imageObserver = new IntersectionObserver(function (entries) {\n            var el = entries[0];\n            if (el && el.isIntersecting && !intersected) {\n              setIntersected(true);\n              setObserve(function (prevObserver) {\n                if (prevObserver) {\n                  prevObserver.disconnect();\n                }\n                return undefined;\n              });\n              triggerOriginalImageLoad();\n              updateImageUrl();\n            }\n          }, {\n            rootMargin: \"\".concat(rootMargin, \" 0px \").concat(rootMargin, \" 0px\")\n          });\n          if (image) {\n            imageObserver.observe(image);\n            setObserve(imageObserver);\n          }\n        } else {\n          setIntersected(true);\n          triggerOriginalImageLoad();\n          updateImageUrl();\n        }\n      }\n      return function () {\n        if (observe) {\n          observe.disconnect();\n        }\n      };\n    }, [props, originalSrc, lqipSrc]);\n    props.urlEndpoint;\n      props.authenticator;\n      props.publicKey;\n      props.loading;\n      props.lqip;\n      props.path;\n      props.src;\n      props.transformation;\n      props.transformationPosition;\n      props.queryParameters;\n      var restProps = _objectWithoutProperties(props, _excluded$2);\n    return /*#__PURE__*/React__default[\"default\"].createElement(\"img\", _extends({\n      alt: props.alt || \"\",\n      src: currentUrl ? currentUrl : undefined,\n      ref: imageRef\n    }, restProps));\n  };\n  IKImage.propTypes = COMBINED_IMAGE_PROP_TYPES$1;\n\n  var Props = {\n    path: PropTypes.string,\n    src: PropTypes.string,\n    queryParameters: PropTypes.objectOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number]).isRequired),\n    transformation: PropTypes.arrayOf(PropTypes.object.isRequired),\n    transformationPosition: PropTypes.oneOf(['path', 'query'])\n  };\n\n  var COMBINED_IMAGE_PROP_TYPES = _objectSpread2(_objectSpread2({}, Props$2), Props);\n\n  var _excluded$1 = [\"urlEndpoint\", \"publicKey\", \"authenticator\", \"path\", \"src\", \"transformation\", \"transformationPosition\", \"queryParameters\"];\n  var IKVideo = function IKVideo(props) {\n    var videoRef = React.useRef(null);\n    var _useState = React.useState({\n        currentUrl: undefined,\n        contextOptions: {}\n      }),\n      _useState2 = _slicedToArray(_useState, 2),\n      state = _useState2[0],\n      setState = _useState2[1];\n    var _useImageKitComponent = useImageKitComponent(_objectSpread2({}, props)),\n      getIKClient = _useImageKitComponent.getIKClient;\n    var contextItems = React.useContext(ImageKitContext);\n    React.useEffect(function () {\n      var _getSrc = getSrc(props, getIKClient(), contextItems),\n        originalSrc = _getSrc.originalSrc;\n      setState(function (prevState) {\n        return _objectSpread2(_objectSpread2({}, prevState), {}, {\n          currentUrl: originalSrc,\n          contextOptions: contextItems\n        });\n      });\n    }, [contextItems, props]);\n    var currentUrl = state.currentUrl;\n    props.urlEndpoint;\n      props.publicKey;\n      props.authenticator;\n      props.path;\n      props.src;\n      props.transformation;\n      props.transformationPosition;\n      props.queryParameters;\n      var restProps = _objectWithoutProperties(props, _excluded$1);\n    return /*#__PURE__*/React__default[\"default\"].createElement(\"video\", _extends({}, restProps, {\n      ref: videoRef,\n      key: currentUrl\n    }), /*#__PURE__*/React__default[\"default\"].createElement(\"source\", {\n      src: currentUrl,\n      type: \"video/mp4\"\n    }));\n  };\n  IKVideo.propTypes = COMBINED_IMAGE_PROP_TYPES;\n\n  var _excluded = [\"publicKey\", \"urlEndpoint\", \"authenticator\", \"fileName\", \"useUniqueFileName\", \"tags\", \"folder\", \"isPrivateFile\", \"customCoordinates\", \"responseFields\", \"onError\", \"onSuccess\", \"onUploadStart\", \"onUploadProgress\", \"validateFile\", \"webhookUrl\", \"overwriteFile\", \"overwriteAITags\", \"overwriteTags\", \"overwriteCustomMetadata\", \"extensions\", \"customMetadata\", \"transformation\", \"checks\", \"overrideParameters\"];\n  var IKUpload = /*#__PURE__*/React.forwardRef(function (props, ref) {\n    var _useState = React.useState({}),\n      _useState2 = _slicedToArray(_useState, 2),\n      state = _useState2[0],\n      setState = _useState2[1];\n    var contextOptions = React.useContext(ImageKitContext);\n    var _useImageKitComponent = useImageKitComponent(_objectSpread2({}, props)),\n      getIKClient = _useImageKitComponent.getIKClient;\n    React.useEffect(function () {\n      var abort = function abort() {\n        if (state.xhr) {\n          state.xhr.abort();\n        }\n      };\n      if (ref && _typeof(ref) === \"object\" && ref.hasOwnProperty(\"current\")) {\n        var refObject = ref;\n        refObject.current.abort = abort;\n      }\n    }, [state.xhr, ref]);\n    props.publicKey;\n      props.urlEndpoint;\n      props.authenticator;\n      var fileName = props.fileName,\n      useUniqueFileName = props.useUniqueFileName,\n      tags = props.tags,\n      folder = props.folder,\n      isPrivateFile = props.isPrivateFile,\n      customCoordinates = props.customCoordinates,\n      responseFields = props.responseFields,\n      onError = props.onError,\n      onSuccess = props.onSuccess;\n      props.onUploadStart;\n      props.onUploadProgress;\n      props.validateFile;\n      var webhookUrl = props.webhookUrl,\n      overwriteFile = props.overwriteFile,\n      overwriteAITags = props.overwriteAITags,\n      overwriteTags = props.overwriteTags,\n      overwriteCustomMetadata = props.overwriteCustomMetadata,\n      extensions = props.extensions,\n      customMetadata = props.customMetadata,\n      transformation = props.transformation,\n      checks = props.checks;\n      props.overrideParameters;\n      var restProps = _objectWithoutProperties(props, _excluded);\n    var uploadFile = function uploadFile(e) {\n      var _e$target$files;\n      var publicKey = props.publicKey || contextOptions.publicKey;\n      var authenticator = props.authenticator || contextOptions.authenticator;\n      var urlEndpoint = props.urlEndpoint || contextOptions.urlEndpoint;\n      if (!publicKey || publicKey.trim() === \"\") {\n        console.error(\"Missing publicKey\");\n        if (onError && typeof onError === \"function\") {\n          onError({\n            message: \"Missing publicKey\"\n          });\n        }\n        return;\n      }\n      if (!authenticator) {\n        console.error(\"The authenticator function is not provided.\");\n        if (onError && typeof onError === \"function\") {\n          onError({\n            message: \"The authenticator function is not provided.\"\n          });\n        }\n        return;\n      }\n      if (typeof authenticator !== 'function') {\n        console.error(\"The provided authenticator is not a function.\");\n        if (onError && typeof onError === \"function\") {\n          onError({\n            message: \"The provided authenticator is not a function.\"\n          });\n        }\n        return;\n      }\n      if (!urlEndpoint || urlEndpoint.trim() === \"\") {\n        console.error(\"Missing urlEndpoint\");\n        if (onError && typeof onError === \"function\") {\n          onError({\n            message: \"Missing urlEndpoint\"\n          });\n        }\n        return;\n      }\n      var ikClient = getIKClient();\n      var file = (_e$target$files = e.target.files) === null || _e$target$files === void 0 ? void 0 : _e$target$files[0];\n      if (!file) {\n        return;\n      }\n      if (props.validateFile && !props.validateFile(file)) {\n        return;\n      }\n      if (props.onUploadStart && typeof props.onUploadStart === \"function\") {\n        props.onUploadStart(e);\n      }\n      var overrideValues = {};\n      if (props.overrideParameters && typeof props.overrideParameters === 'function') {\n        overrideValues = props.overrideParameters(file) || {};\n      }\n      var xhr = new XMLHttpRequest();\n      var progressCb = function progressCb(e) {\n        if (props.onUploadProgress && typeof props.onUploadProgress === 'function') {\n          props.onUploadProgress(e);\n        }\n      };\n      xhr.upload.addEventListener('progress', progressCb);\n      var params = {\n        file: file,\n        fileName: overrideValues.fileName || fileName || file.name,\n        useUniqueFileName: overrideValues.useUniqueFileName || useUniqueFileName,\n        tags: overrideValues.tags || tags,\n        folder: overrideValues.folder || folder,\n        isPrivateFile: overrideValues.isPrivateFile || isPrivateFile,\n        customCoordinates: overrideValues.customCoordinates || customCoordinates,\n        responseFields: responseFields,\n        extensions: overrideValues.extensions || extensions,\n        webhookUrl: overrideValues.webhookUrl || webhookUrl,\n        overwriteFile: overrideValues.overwriteFile || overwriteFile,\n        overwriteAITags: overrideValues.overwriteAITags || overwriteAITags,\n        overwriteTags: overrideValues.overwriteTags || overwriteTags,\n        overwriteCustomMetadata: overrideValues.overwriteCustomMetadata || overwriteCustomMetadata,\n        customMetadata: overrideValues.customMetadata || customMetadata,\n        signature: '',\n        expire: 0,\n        token: '',\n        xhr: xhr,\n        transformation: overrideValues.transformation || transformation,\n        checks: overrideValues.checks || checks\n      };\n      var authPromise = authenticator();\n      if (!(authPromise instanceof Promise)) {\n        if (onError && typeof onError === \"function\") {\n          onError({\n            message: \"The authenticator function is expected to return a Promise instance.\"\n          });\n        }\n        return;\n      }\n      authPromise.then(function (_ref) {\n        var signature = _ref.signature,\n          token = _ref.token,\n          expire = _ref.expire;\n        params['signature'] = signature;\n        params['expire'] = expire;\n        params['token'] = token;\n        ikClient.upload(params, function (err, result) {\n          if (err) {\n            if (onError && typeof onError === \"function\") {\n              console.log(err);\n              onError(err);\n            }\n          } else {\n            if (onSuccess && typeof onSuccess === \"function\") {\n              onSuccess(result);\n            }\n          }\n          xhr.upload.removeEventListener('progress', progressCb);\n        }, {\n          publicKey: publicKey\n        });\n        setState({\n          xhr: xhr\n        });\n      })[\"catch\"](function (data) {\n        var error;\n        if (data instanceof Array) {\n          error = data[0];\n        } else {\n          error = data;\n        }\n        if (onError && typeof onError === \"function\") {\n          onError({\n            message: String(error)\n          });\n        }\n        return;\n      });\n    };\n    return /*#__PURE__*/React__default[\"default\"].createElement(\"input\", _extends({}, restProps, {\n      ref: ref,\n      type: \"file\",\n      onChange: function onChange(e) {\n        if (props.onChange && typeof props.onChange === \"function\") {\n          props.onChange(e);\n        }\n        uploadFile(e);\n      }\n    }));\n  });\n\n  exports.IKContext = IKContext;\n  exports.IKCore = ImageKit;\n  exports.IKImage = IKImage;\n  exports.IKUpload = IKUpload;\n  exports.IKVideo = IKVideo;\n\n  Object.defineProperty(exports, '__esModule', { value: true });\n\n}));\n"], "mappings": "AAAA,CAAC,UAAUA,MAAM,EAAEC,OAAO,EAAE;EAC1B,OAAOC,OAAO,KAAK,QAAQ,IAAI,OAAOC,MAAM,KAAK,WAAW,GAAGF,OAAO,CAACC,OAAO,EAAEE,OAAO,CAAC,OAAO,CAAC,CAAC,GACjG,OAAOC,MAAM,KAAK,UAAU,IAAIA,MAAM,CAACC,GAAG,GAAGD,MAAM,CAAC,CAAC,SAAS,EAAE,OAAO,CAAC,EAAEJ,OAAO,CAAC,IACjFD,MAAM,GAAG,OAAOO,UAAU,KAAK,WAAW,GAAGA,UAAU,GAAGP,MAAM,IAAIQ,IAAI,EAAEP,OAAO,CAACD,MAAM,CAACS,aAAa,GAAG,CAAC,CAAC,EAAET,MAAM,CAACU,KAAK,CAAC,CAAC;AAC9H,CAAC,EAAE,IAAI,EAAG,UAAUR,OAAO,EAAEQ,KAAK,EAAE;EAAE,YAAY;;EAEhD,SAASC,qBAAqBA,CAAEC,CAAC,EAAE;IAAE,OAAOA,CAAC,IAAI,OAAOA,CAAC,KAAK,QAAQ,IAAI,SAAS,IAAIA,CAAC,GAAGA,CAAC,GAAG;MAAE,SAAS,EAAEA;IAAE,CAAC;EAAE;EAEjH,IAAIC,cAAc,GAAG,aAAaF,qBAAqB,CAACD,KAAK,CAAC;EAE9D,SAASI,SAASA,CAACC,GAAG,EAAE;IACtB,yBAAyB;;IAEzB,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAI,OAAOA,MAAM,CAACC,QAAQ,KAAK,QAAQ,EAAE;MACvEH,SAAS,GAAG,SAAAA,CAAUC,GAAG,EAAE;QACzB,OAAO,OAAOA,GAAG;MACnB,CAAC;IACH,CAAC,MAAM;MACLD,SAAS,GAAG,SAAAA,CAAUC,GAAG,EAAE;QACzB,OAAOA,GAAG,IAAI,OAAOC,MAAM,KAAK,UAAU,IAAID,GAAG,CAACG,WAAW,KAAKF,MAAM,IAAID,GAAG,KAAKC,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOJ,GAAG;MAC9H,CAAC;IACH;IACA,OAAOD,SAAS,CAACC,GAAG,CAAC;EACvB;EACA,SAASK,eAAeA,CAACC,QAAQ,EAAEC,WAAW,EAAE;IAC9C,IAAI,EAAED,QAAQ,YAAYC,WAAW,CAAC,EAAE;MACtC,MAAM,IAAIC,SAAS,CAAC,mCAAmC,CAAC;IAC1D;EACF;EACA,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,KAAK,EAAE;IACxC,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,CAACE,MAAM,EAAED,CAAC,EAAE,EAAE;MACrC,IAAIE,UAAU,GAAGH,KAAK,CAACC,CAAC,CAAC;MACzBE,UAAU,CAACC,UAAU,GAAGD,UAAU,CAACC,UAAU,IAAI,KAAK;MACtDD,UAAU,CAACE,YAAY,GAAG,IAAI;MAC9B,IAAI,OAAO,IAAIF,UAAU,EAAEA,UAAU,CAACG,QAAQ,GAAG,IAAI;MACrDC,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEI,UAAU,CAACM,GAAG,EAAEN,UAAU,CAAC;IAC3D;EACF;EACA,SAASO,YAAYA,CAACd,WAAW,EAAEe,UAAU,EAAEC,WAAW,EAAE;IAC1D,IAAID,UAAU,EAAEb,iBAAiB,CAACF,WAAW,CAACH,SAAS,EAAEkB,UAAU,CAAC;IACpE,IAAIC,WAAW,EAAEd,iBAAiB,CAACF,WAAW,EAAEgB,WAAW,CAAC;IAC5D,OAAOhB,WAAW;EACpB;EACA,SAASiB,iBAAiBA,CAACxB,GAAG,EAAEoB,GAAG,EAAEK,KAAK,EAAE;IAC1C,IAAIL,GAAG,IAAIpB,GAAG,EAAE;MACdkB,MAAM,CAACC,cAAc,CAACnB,GAAG,EAAEoB,GAAG,EAAE;QAC9BK,KAAK,EAAEA,KAAK;QACZV,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM;MACLjB,GAAG,CAACoB,GAAG,CAAC,GAAGK,KAAK;IAClB;IACA,OAAOzB,GAAG;EACZ;EACA,SAAS0B,SAASA,CAACC,MAAM,EAAEC,cAAc,EAAE;IACzC,IAAIC,IAAI,GAAGX,MAAM,CAACW,IAAI,CAACF,MAAM,CAAC;IAC9B,IAAIT,MAAM,CAACY,qBAAqB,EAAE;MAChC,IAAIC,OAAO,GAAGb,MAAM,CAACY,qBAAqB,CAACH,MAAM,CAAC;MAClD,IAAIC,cAAc,EAAEG,OAAO,GAAGA,OAAO,CAACC,MAAM,CAAC,UAAUC,GAAG,EAAE;QAC1D,OAAOf,MAAM,CAACgB,wBAAwB,CAACP,MAAM,EAAEM,GAAG,CAAC,CAAClB,UAAU;MAChE,CAAC,CAAC;MACFc,IAAI,CAACM,IAAI,CAACC,KAAK,CAACP,IAAI,EAAEE,OAAO,CAAC;IAChC;IACA,OAAOF,IAAI;EACb;EACA,SAASQ,gBAAgBA,CAAC3B,MAAM,EAAE;IAChC,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,SAAS,CAACzB,MAAM,EAAED,CAAC,EAAE,EAAE;MACzC,IAAI2B,MAAM,GAAGD,SAAS,CAAC1B,CAAC,CAAC,IAAI,IAAI,GAAG0B,SAAS,CAAC1B,CAAC,CAAC,GAAG,CAAC,CAAC;MACrD,IAAIA,CAAC,GAAG,CAAC,EAAE;QACTc,SAAS,CAACR,MAAM,CAACqB,MAAM,CAAC,EAAE,IAAI,CAAC,CAACC,OAAO,CAAC,UAAUpB,GAAG,EAAE;UACrDI,iBAAiB,CAACd,MAAM,EAAEU,GAAG,EAAEmB,MAAM,CAACnB,GAAG,CAAC,CAAC;QAC7C,CAAC,CAAC;MACJ,CAAC,MAAM,IAAIF,MAAM,CAACuB,yBAAyB,EAAE;QAC3CvB,MAAM,CAACwB,gBAAgB,CAAChC,MAAM,EAAEQ,MAAM,CAACuB,yBAAyB,CAACF,MAAM,CAAC,CAAC;MAC3E,CAAC,MAAM;QACLb,SAAS,CAACR,MAAM,CAACqB,MAAM,CAAC,CAAC,CAACC,OAAO,CAAC,UAAUpB,GAAG,EAAE;UAC/CF,MAAM,CAACC,cAAc,CAACT,MAAM,EAAEU,GAAG,EAAEF,MAAM,CAACgB,wBAAwB,CAACK,MAAM,EAAEnB,GAAG,CAAC,CAAC;QAClF,CAAC,CAAC;MACJ;IACF;IACA,OAAOV,MAAM;EACf;EACA,SAASiC,6BAA6BA,CAACC,CAAC,EAAEC,MAAM,EAAE;IAChD,IAAI,CAACD,CAAC,EAAE;IACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOE,mBAAmB,CAACF,CAAC,EAAEC,MAAM,CAAC;IAChE,IAAIE,CAAC,GAAG7B,MAAM,CAACd,SAAS,CAAC4C,QAAQ,CAACC,IAAI,CAACL,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtD,IAAIH,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACzC,WAAW,EAAE4C,CAAC,GAAGH,CAAC,CAACzC,WAAW,CAACgD,IAAI;IAC3D,IAAIJ,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOK,KAAK,CAACC,IAAI,CAACT,CAAC,CAAC;IACpD,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACO,IAAI,CAACP,CAAC,CAAC,EAAE,OAAOD,mBAAmB,CAACF,CAAC,EAAEC,MAAM,CAAC;EACpH;EACA,SAASC,mBAAmBA,CAACS,GAAG,EAAEC,GAAG,EAAE;IACrC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAAC1C,MAAM,EAAE2C,GAAG,GAAGD,GAAG,CAAC1C,MAAM;IACrD,KAAK,IAAID,CAAC,GAAG,CAAC,EAAE6C,IAAI,GAAG,IAAIL,KAAK,CAACI,GAAG,CAAC,EAAE5C,CAAC,GAAG4C,GAAG,EAAE5C,CAAC,EAAE,EAAE6C,IAAI,CAAC7C,CAAC,CAAC,GAAG2C,GAAG,CAAC3C,CAAC,CAAC;IACrE,OAAO6C,IAAI;EACb;EACA,SAASC,0BAA0BA,CAACd,CAAC,EAAEe,cAAc,EAAE;IACrD,IAAIC,EAAE;IACN,IAAI,OAAO3D,MAAM,KAAK,WAAW,IAAI2C,CAAC,CAAC3C,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,EAAE;MAC/D,IAAIkD,KAAK,CAACS,OAAO,CAACjB,CAAC,CAAC,KAAKgB,EAAE,GAAGjB,6BAA6B,CAACC,CAAC,CAAC,CAAC,IAAIe,cAAc,IAAIf,CAAC,IAAI,OAAOA,CAAC,CAAC/B,MAAM,KAAK,QAAQ,EAAE;QACtH,IAAI+C,EAAE,EAAEhB,CAAC,GAAGgB,EAAE;QACd,IAAIhD,CAAC,GAAG,CAAC;QACT,IAAIkD,CAAC,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;QACtB,OAAO;UACLC,CAAC,EAAED,CAAC;UACJf,CAAC,EAAE,SAAAA,CAAA,EAAY;YACb,IAAInC,CAAC,IAAIgC,CAAC,CAAC/B,MAAM,EAAE,OAAO;cACxBmD,IAAI,EAAE;YACR,CAAC;YACD,OAAO;cACLA,IAAI,EAAE,KAAK;cACXvC,KAAK,EAAEmB,CAAC,CAAChC,CAAC,EAAE;YACd,CAAC;UACH,CAAC;UACDf,CAAC,EAAE,SAAAA,CAAUA,CAAC,EAAE;YACd,MAAMA,CAAC;UACT,CAAC;UACDoE,CAAC,EAAEH;QACL,CAAC;MACH;MACA,MAAM,IAAItD,SAAS,CAAC,uIAAuI,CAAC;IAC9J;IACA,IAAI0D,gBAAgB,GAAG,IAAI;MACzBC,MAAM,GAAG,KAAK;MACdC,GAAG;IACL,OAAO;MACLL,CAAC,EAAE,SAAAA,CAAA,EAAY;QACbH,EAAE,GAAGhB,CAAC,CAAC3C,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;MAC3B,CAAC;MACD6C,CAAC,EAAE,SAAAA,CAAA,EAAY;QACb,IAAIsB,IAAI,GAAGT,EAAE,CAACU,IAAI,CAAC,CAAC;QACpBJ,gBAAgB,GAAGG,IAAI,CAACL,IAAI;QAC5B,OAAOK,IAAI;MACb,CAAC;MACDxE,CAAC,EAAE,SAAAA,CAAUA,CAAC,EAAE;QACdsE,MAAM,GAAG,IAAI;QACbC,GAAG,GAAGvE,CAAC;MACT,CAAC;MACDoE,CAAC,EAAE,SAAAA,CAAA,EAAY;QACb,IAAI;UACF,IAAI,CAACC,gBAAgB,IAAIN,EAAE,CAACW,MAAM,IAAI,IAAI,EAAEX,EAAE,CAACW,MAAM,CAAC,CAAC;QACzD,CAAC,SAAS;UACR,IAAIJ,MAAM,EAAE,MAAMC,GAAG;QACvB;MACF;IACF,CAAC;EACH;EACA,IAAII,OAAO,GAAG,OAAO;EACrB,IAAIC,aAAa,GAAG;IAClBC,gCAAgC,EAAE;MAChCC,OAAO,EAAE,+CAA+C;MACxDC,IAAI,EAAE;IACR,CAAC;IACDC,+BAA+B,EAAE;MAC/BF,OAAO,EAAE,0CAA0C;MACnDC,IAAI,EAAE;IACR,CAAC;IACDE,uBAAuB,EAAE;MACvBH,OAAO,EAAE,oDAAoD;MAC7DC,IAAI,EAAE;IACR,CAAC;IACDG,mBAAmB,EAAE;MACnBJ,OAAO,EAAE,yBAAyB;MAClCC,IAAI,EAAE;IACR,CAAC;IACDI,6BAA6B,EAAE;MAC7BL,OAAO,EAAE,mCAAmC;MAC5CC,IAAI,EAAE;IACR,CAAC;IACDK,iCAAiC,EAAE;MACjCN,OAAO,EAAE,uCAAuC;MAChDC,IAAI,EAAE;IACR,CAAC;IACDM,+BAA+B,EAAE;MAC/BP,OAAO,EAAE,4CAA4C;MACrDC,IAAI,EAAE;IACR,CAAC;IACDO,kBAAkB,EAAE;MAClBR,OAAO,EAAE,+BAA+B;MACxCC,IAAI,EAAE;IACR,CAAC;IACDQ,qBAAqB,EAAE;MACrBT,OAAO,EAAE,iEAAiE;MAC1EC,IAAI,EAAE;IACR,CAAC;IACDS,2BAA2B,EAAE;MAC3BV,OAAO,EAAE,+DAA+D;MACxEC,IAAI,EAAE;IACR,CAAC;IACDU,qBAAqB,EAAE;MACrBX,OAAO,EAAE,mIAAmI;MAC5IC,IAAI,EAAE;IACR,CAAC;IACDW,6BAA6B,EAAE;MAC7BZ,OAAO,EAAE,iEAAiE;MAC1EC,IAAI,EAAE;IACR,CAAC;IACDY,sBAAsB,EAAE;MACtBb,OAAO,EAAE,iCAAiC;MAC1CC,IAAI,EAAE;IACR,CAAC;IACDa,iBAAiB,EAAE;MACjBd,OAAO,EAAE,+FAA+F;MACxGC,IAAI,EAAE;IACR,CAAC;IACDc,aAAa,EAAE;MACbf,OAAO,EAAE,2FAA2F;MACpGC,IAAI,EAAE;IACR,CAAC;IACDe,cAAc,EAAE;MACdhB,OAAO,EAAE,4FAA4F;MACrGC,IAAI,EAAE;IACR,CAAC;IACDgB,sBAAsB,EAAE;MACtBjB,OAAO,EAAE,+EAA+E;MACxFC,IAAI,EAAE;IACR,CAAC;IACDiB,0BAA0B,EAAE;MAC1BlB,OAAO,EAAE,uCAAuC;MAChDC,IAAI,EAAE;IACR,CAAC;IACDkB,2BAA2B,EAAE;MAC3BnB,OAAO,EAAE,wCAAwC;MACjDC,IAAI,EAAE;IACR;EACF,CAAC;EACD,SAASmB,OAAOA,CAACC,OAAO,EAAEC,QAAQ,EAAEC,QAAQ,EAAE;IAC5C,IAAI,OAAOA,QAAQ,IAAI,UAAU,EAAE;MACjC,IAAIF,OAAO,EAAE;QACXE,QAAQ,CAACD,QAAQ,EAAE,IAAI,CAAC;MAC1B,CAAC,MAAM;QACLC,QAAQ,CAAC,IAAI,EAAED,QAAQ,CAAC;MAC1B;IACF;EACF;EACA,SAASE,oBAAoBA,CAACC,GAAG,EAAE;IACjC,IAAIC,OAAO,GAAG,CAAC,CAAC;IAChB,IAAIC,eAAe,GAAGF,GAAG,CAACG,qBAAqB,CAAC,CAAC;IACjD,IAAIrF,MAAM,CAACW,IAAI,CAACyE,eAAe,CAAC,CAACzF,MAAM,EAAE;MACvCyF,eAAe,CAACE,IAAI,CAAC,CAAC,CAACC,KAAK,CAAC,SAAS,CAAC,CAACC,GAAG,CAAC,UAAUjF,KAAK,EAAE;QAC3D,OAAOA,KAAK,CAACgF,KAAK,CAAC,IAAI,CAAC;MAC1B,CAAC,CAAC,CAACjE,OAAO,CAAC,UAAUmE,QAAQ,EAAE;QAC7BN,OAAO,CAACM,QAAQ,CAAC,CAAC,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC,GAAGG,QAAQ,CAAC,CAAC,CAAC,CAACH,IAAI,CAAC,CAAC;MAClD,CAAC,CAAC;IACJ;IACA,OAAOH,OAAO;EAChB;EACA,IAAIO,yBAAyB,GAAG,SAASA,yBAAyBA,CAACC,IAAI,EAAET,GAAG,EAAE;IAC5E,IAAIH,QAAQ,GAAG5D,gBAAgB,CAAC,CAAC,CAAC,EAAEwE,IAAI,CAAC;IACzC,IAAIC,gBAAgB,GAAG;MACrBC,UAAU,EAAEX,GAAG,CAACY,MAAM;MACtBX,OAAO,EAAEF,oBAAoB,CAACC,GAAG;IACnC,CAAC;IACDlF,MAAM,CAACC,cAAc,CAAC8E,QAAQ,EAAE,mBAAmB,EAAE;MACnDxE,KAAK,EAAEqF,gBAAgB;MACvB/F,UAAU,EAAE,KAAK;MACjBE,QAAQ,EAAE;IACZ,CAAC,CAAC;IACF,OAAOgF,QAAQ;EACjB,CAAC;EACD,IAAIgB,OAAO,GAAG,SAASA,OAAOA,CAACC,aAAa,EAAEC,QAAQ,EAAEjB,QAAQ,EAAE;IAChEkB,UAAU,CAACF,aAAa,EAAEC,QAAQ,CAAC,CAACE,IAAI,CAAC,UAAUC,MAAM,EAAE;MACzD,OAAOvB,OAAO,CAAC,KAAK,EAAEuB,MAAM,EAAEpB,QAAQ,CAAC;IACzC,CAAC,EAAE,UAAUqB,EAAE,EAAE;MACf,OAAOxB,OAAO,CAAC,IAAI,EAAEwB,EAAE,EAAErB,QAAQ,CAAC;IACpC,CAAC,CAAC;EACJ,CAAC;EACD,IAAIkB,UAAU,GAAG,SAASA,UAAUA,CAACF,aAAa,EAAEC,QAAQ,EAAE;IAC5D,OAAO,IAAIK,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;MAC5CR,aAAa,CAACS,IAAI,CAAC,MAAM,EAAE,gDAAgD,CAAC;MAC5ET,aAAa,CAACU,OAAO,GAAG,UAAU/H,CAAC,EAAE;QACnC,OAAO6H,MAAM,CAACjD,aAAa,CAACc,6BAA6B,CAAC;MAC5D,CAAC;MACD2B,aAAa,CAACW,MAAM,GAAG,YAAY;QACjC,IAAIX,aAAa,CAACF,MAAM,KAAK,GAAG,EAAE;UAChC,IAAI;YACF,IAAIH,IAAI,GAAGiB,IAAI,CAACC,KAAK,CAACb,aAAa,CAACc,YAAY,CAAC;YACjD,IAAIC,cAAc,GAAGrB,yBAAyB,CAACC,IAAI,EAAEK,aAAa,CAAC;YACnE,OAAOO,OAAO,CAACQ,cAAc,CAAC;UAChC,CAAC,CAAC,OAAOV,EAAE,EAAE;YACX,OAAOG,MAAM,CAACH,EAAE,CAAC;UACnB;QACF,CAAC,MAAM;UACL,IAAI;YACF,IAAIV,IAAI,GAAGiB,IAAI,CAACC,KAAK,CAACb,aAAa,CAACc,YAAY,CAAC;YACjD,IAAIE,WAAW,GAAGtB,yBAAyB,CAACC,IAAI,EAAEK,aAAa,CAAC;YAChE,OAAOQ,MAAM,CAACQ,WAAW,CAAC;UAC5B,CAAC,CAAC,OAAOX,EAAE,EAAE;YACX,OAAOG,MAAM,CAACH,EAAE,CAAC;UACnB;QACF;MACF,CAAC;MACDL,aAAa,CAACiB,IAAI,CAAChB,QAAQ,CAAC;IAC9B,CAAC,CAAC;EACJ,CAAC;EACD,IAAIiB,MAAM,GAAG,SAASA,MAAMA,CAAChC,GAAG,EAAEiC,aAAa,EAAEC,OAAO,EAAEpC,QAAQ,EAAE;IAClE,IAAI,CAACmC,aAAa,CAACE,IAAI,EAAE;MACvBxC,OAAO,CAAC,IAAI,EAAEtB,aAAa,CAACO,6BAA6B,EAAEkB,QAAQ,CAAC;MACpE;IACF;IACA,IAAI,CAACmC,aAAa,CAACG,QAAQ,EAAE;MAC3BzC,OAAO,CAAC,IAAI,EAAEtB,aAAa,CAACQ,iCAAiC,EAAEiB,QAAQ,CAAC;MACxE;IACF;IACA,IAAI,CAACoC,OAAO,CAACG,SAAS,EAAE;MACtB1C,OAAO,CAAC,IAAI,EAAEtB,aAAa,CAACU,kBAAkB,EAAEe,QAAQ,CAAC;MACzD;IACF;IACA,IAAI,CAACmC,aAAa,CAACK,KAAK,EAAE;MACxB3C,OAAO,CAAC,IAAI,EAAEtB,aAAa,CAACiB,aAAa,EAAEQ,QAAQ,CAAC;MACpD;IACF;IACA,IAAI,CAACmC,aAAa,CAACM,SAAS,EAAE;MAC5B5C,OAAO,CAAC,IAAI,EAAEtB,aAAa,CAACgB,iBAAiB,EAAES,QAAQ,CAAC;MACxD;IACF;IACA,IAAI,CAACmC,aAAa,CAACO,MAAM,EAAE;MACzB7C,OAAO,CAAC,IAAI,EAAEtB,aAAa,CAACkB,cAAc,EAAEO,QAAQ,CAAC;MACrD;IACF;IACA,IAAImC,aAAa,CAACQ,cAAc,EAAE;MAChC,IAAI,EAAE3H,MAAM,CAACW,IAAI,CAACwG,aAAa,CAACQ,cAAc,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAI5H,MAAM,CAACW,IAAI,CAACwG,aAAa,CAACQ,cAAc,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE;QAC9H/C,OAAO,CAAC,IAAI,EAAEtB,aAAa,CAACmB,sBAAsB,EAAEM,QAAQ,CAAC;QAC7D;MACF;MACA,IAAIhF,MAAM,CAACW,IAAI,CAACwG,aAAa,CAACQ,cAAc,CAAC,CAACC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAACT,aAAa,CAACQ,cAAc,CAACE,GAAG,EAAE;QAClGhD,OAAO,CAAC,IAAI,EAAEtB,aAAa,CAACoB,0BAA0B,EAAEK,QAAQ,CAAC;QACjE;MACF;MACA,IAAIhF,MAAM,CAACW,IAAI,CAACwG,aAAa,CAACQ,cAAc,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,EAAE;QAC9D,IAAI1F,KAAK,CAACS,OAAO,CAACwE,aAAa,CAACQ,cAAc,CAACG,IAAI,CAAC,EAAE;UACpD,IAAIC,SAAS,GAAGvF,0BAA0B,CAAC2E,aAAa,CAACQ,cAAc,CAACG,IAAI,CAAC;YAC3EE,KAAK;UACP,IAAI;YACF,KAAKD,SAAS,CAAClF,CAAC,CAAC,CAAC,EAAE,CAAC,CAACmF,KAAK,GAAGD,SAAS,CAAClG,CAAC,CAAC,CAAC,EAAEiB,IAAI,GAAG;cAClD,IAAI6E,cAAc,GAAGK,KAAK,CAACzH,KAAK;cAChC,IAAIoH,cAAc,CAACM,IAAI,KAAK,KAAK,IAAI,EAAEN,cAAc,CAACO,QAAQ,IAAIP,cAAc,CAACpH,KAAK,CAAC,EAAE;gBACvFsE,OAAO,CAAC,IAAI,EAAEtB,aAAa,CAACqB,2BAA2B,EAAEI,QAAQ,CAAC;gBAClE;cACF,CAAC,MAAM,IAAI2C,cAAc,CAACM,IAAI,KAAK,gBAAgB,IAAI,CAACN,cAAc,CAACpH,KAAK,EAAE;gBAC5EsE,OAAO,CAAC,IAAI,EAAEtB,aAAa,CAACqB,2BAA2B,EAAEI,QAAQ,CAAC;gBAClE;cACF;YACF;UACF,CAAC,CAAC,OAAO9B,GAAG,EAAE;YACZ6E,SAAS,CAACpJ,CAAC,CAACuE,GAAG,CAAC;UAClB,CAAC,SAAS;YACR6E,SAAS,CAAChF,CAAC,CAAC,CAAC;UACf;QACF,CAAC,MAAM;UACL8B,OAAO,CAAC,IAAI,EAAEtB,aAAa,CAACqB,2BAA2B,EAAEI,QAAQ,CAAC;UAClE;QACF;MACF;IACF;IACA,IAAIiB,QAAQ,GAAG,IAAIkC,QAAQ,CAAC,CAAC;IAC7B,IAAIjI,GAAG;IACP,KAAKA,GAAG,IAAIiH,aAAa,EAAE;MACzB,IAAIjH,GAAG,EAAE;QACP,IAAIA,GAAG,KAAK,MAAM,IAAI,OAAOiH,aAAa,CAACE,IAAI,IAAI,QAAQ,EAAE;UAC3DpB,QAAQ,CAACmC,MAAM,CAAC,MAAM,EAAEjB,aAAa,CAACE,IAAI,EAAEgB,MAAM,CAAClB,aAAa,CAACG,QAAQ,CAAC,CAAC;QAC7E,CAAC,MAAM,IAAIpH,GAAG,KAAK,MAAM,IAAIgC,KAAK,CAACS,OAAO,CAACwE,aAAa,CAACmB,IAAI,CAAC,EAAE;UAC9DrC,QAAQ,CAACmC,MAAM,CAAC,MAAM,EAAEjB,aAAa,CAACmB,IAAI,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC;QACvD,CAAC,MAAM,IAAIrI,GAAG,KAAK,WAAW,EAAE;UAC9B+F,QAAQ,CAACmC,MAAM,CAAC,WAAW,EAAEjB,aAAa,CAACM,SAAS,CAAC;QACvD,CAAC,MAAM,IAAIvH,GAAG,KAAK,QAAQ,EAAE;UAC3B+F,QAAQ,CAACmC,MAAM,CAAC,QAAQ,EAAEC,MAAM,CAAClB,aAAa,CAACO,MAAM,CAAC,CAAC;QACzD,CAAC,MAAM,IAAIxH,GAAG,KAAK,OAAO,EAAE;UAC1B+F,QAAQ,CAACmC,MAAM,CAAC,OAAO,EAAEjB,aAAa,CAACK,KAAK,CAAC;QAC/C,CAAC,MAAM,IAAItH,GAAG,KAAK,gBAAgB,IAAIgC,KAAK,CAACS,OAAO,CAACwE,aAAa,CAACqB,cAAc,CAAC,EAAE;UAClFvC,QAAQ,CAACmC,MAAM,CAAC,gBAAgB,EAAEjB,aAAa,CAACqB,cAAc,CAACD,IAAI,CAAC,GAAG,CAAC,CAAC;QAC3E,CAAC,MAAM,IAAIrI,GAAG,KAAK,YAAY,IAAIgC,KAAK,CAACS,OAAO,CAACwE,aAAa,CAACsB,UAAU,CAAC,EAAE;UAC1ExC,QAAQ,CAACmC,MAAM,CAAC,YAAY,EAAExB,IAAI,CAAC8B,SAAS,CAACvB,aAAa,CAACsB,UAAU,CAAC,CAAC;QACzE,CAAC,MAAM,IAAIvI,GAAG,KAAK,gBAAgB,IAAIrB,SAAS,CAACsI,aAAa,CAACwB,cAAc,CAAC,KAAK,QAAQ,IAAI,CAACzG,KAAK,CAACS,OAAO,CAACwE,aAAa,CAACwB,cAAc,CAAC,IAAIxB,aAAa,CAACwB,cAAc,KAAK,IAAI,EAAE;UACpL1C,QAAQ,CAACmC,MAAM,CAAC,gBAAgB,EAAExB,IAAI,CAAC8B,SAAS,CAACvB,aAAa,CAACwB,cAAc,CAAC,CAAC;QACjF,CAAC,MAAM,IAAIzI,GAAG,KAAK,gBAAgB,IAAIrB,SAAS,CAACsI,aAAa,CAACQ,cAAc,CAAC,KAAK,QAAQ,IAAIR,aAAa,CAACQ,cAAc,KAAK,IAAI,EAAE;UACpI1B,QAAQ,CAACmC,MAAM,CAAClI,GAAG,EAAE0G,IAAI,CAAC8B,SAAS,CAACvB,aAAa,CAACQ,cAAc,CAAC,CAAC;QACpE,CAAC,MAAM,IAAIzH,GAAG,KAAK,QAAQ,IAAIiH,aAAa,CAACyB,MAAM,EAAE;UACnD3C,QAAQ,CAACmC,MAAM,CAAC,QAAQ,EAAEjB,aAAa,CAACyB,MAAM,CAAC;QACjD,CAAC,MAAM,IAAIzB,aAAa,CAACjH,GAAG,CAAC,KAAK2I,SAAS,EAAE;UAC3C5C,QAAQ,CAACmC,MAAM,CAAClI,GAAG,EAAEmI,MAAM,CAAClB,aAAa,CAACjH,GAAG,CAAC,CAAC,CAAC;QAClD;MACF;IACF;IACA+F,QAAQ,CAACmC,MAAM,CAAC,WAAW,EAAEhB,OAAO,CAACG,SAAS,CAAC;IAC/CxB,OAAO,CAACb,GAAG,EAAEe,QAAQ,EAAEjB,QAAQ,CAAC;EAClC,CAAC;EACD,IAAI8D,mBAAmB,GAAG;IACxBC,KAAK,EAAE,GAAG;IACVC,MAAM,EAAE,GAAG;IACXC,WAAW,EAAE,IAAI;IACjBC,OAAO,EAAE,GAAG;IACZC,IAAI,EAAE,GAAG;IACTC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAE,IAAI;IACXC,CAAC,EAAE,GAAG;IACNC,CAAC,EAAE,GAAG;IACNC,MAAM,EAAE,GAAG;IACXC,MAAM,EAAE,GAAG;IACXC,UAAU,EAAE,IAAI;IAChBC,MAAM,EAAE,GAAG;IACXC,QAAQ,EAAE,IAAI;IACdC,MAAM,EAAE,IAAI;IACZC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,GAAG;IACVC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,IAAI;IACd3E,IAAI,EAAE,GAAG;IACT4E,QAAQ,EAAE,IAAI;IACdC,YAAY,EAAE,IAAI;IAClBC,YAAY,EAAE,IAAI;IAClBC,GAAG,EAAE,KAAK;IACVC,aAAa,EAAE,WAAW;IAC1BC,SAAS,EAAE,OAAO;IAClBC,cAAc,EAAE,YAAY;IAC5BC,UAAU,EAAE,aAAa;IACzBC,QAAQ,EAAE,MAAM;IAChBC,YAAY,EAAE,UAAU;IACxBC,cAAc,EAAE,YAAY;IAC5BC,GAAG,EAAE;EACP,CAAC;EACD,IAAIC,+BAA+B,GAAG,MAAM;EAC5C,IAAIC,6BAA6B,GAAG,OAAO;EAC3C,IAAIC,8BAA8B,GAAG,CAACF,+BAA+B,EAAEC,6BAA6B,CAAC;EACrG,IAAIE,yBAAyB,GAAG,GAAG;EACnC,IAAIC,mBAAmB,GAAG,GAAG;EAC7B,IAAIC,6BAA6B,GAAG,GAAG;EACvC,IAAIC,mBAAmB,GAAG;IACxBC,UAAU,EAAE,SAASA,UAAUA,CAAA,EAAG;MAChC,OAAOP,+BAA+B;IACxC,CAAC;IACDQ,mBAAmB,EAAE,SAASA,mBAAmBA,CAAClE,OAAO,EAAE;MACzD,OAAOA,OAAO,CAACmE,sBAAsB,KAAKR,6BAA6B;IACzE,CAAC;IACDS,eAAe,EAAE,SAASA,eAAeA,CAACpE,OAAO,EAAE;MACjD,IAAI,OAAOA,OAAO,CAACmE,sBAAsB,IAAI,WAAW,EAAE,OAAO,KAAK;MACtE,OAAOP,8BAA8B,CAACS,OAAO,CAACrE,OAAO,CAACmE,sBAAsB,CAAC,IAAI,CAAC,CAAC;IACrF,CAAC;IACDG,eAAe,EAAE,SAASA,eAAeA,CAACC,SAAS,EAAE;MACnD,IAAI,CAACA,SAAS,EAAE;QACd,OAAO,EAAE;MACX;MACA,OAAO7C,mBAAmB,CAAC6C,SAAS,CAAC,IAAI7C,mBAAmB,CAAC6C,SAAS,CAACC,WAAW,CAAC,CAAC,CAAC,IAAI,EAAE;IAC7F,CAAC;IACDC,0BAA0B,EAAE,SAASA,0BAA0BA,CAAA,EAAG;MAChE,OAAOZ,yBAAyB;IAClC,CAAC;IACDa,qBAAqB,EAAE,SAASA,qBAAqBA,CAAA,EAAG;MACtD,OAAOZ,mBAAmB;IAC5B,CAAC;IACDa,6BAA6B,EAAE,SAASA,6BAA6BA,CAAA,EAAG;MACtE,OAAOZ,6BAA6B;IACtC;EACF,CAAC;EACD,IAAIa,wBAAwB,GAAG,IAAI;EACnC,SAASC,mBAAmBA,CAACC,GAAG,EAAE;IAChC,IAAI,OAAOA,GAAG,IAAI,QAAQ,IAAIA,GAAG,CAACA,GAAG,CAACvM,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,EAAE;MACxDuM,GAAG,GAAGA,GAAG,CAACC,SAAS,CAAC,CAAC,EAAED,GAAG,CAACvM,MAAM,GAAG,CAAC,CAAC;IACxC;IACA,OAAOuM,GAAG;EACZ;EACA,SAASE,kBAAkBA,CAACF,GAAG,EAAE;IAC/B,IAAI,OAAOA,GAAG,IAAI,QAAQ,IAAIA,GAAG,CAAC,CAAC,CAAC,IAAI,GAAG,EAAE;MAC3CA,GAAG,GAAGA,GAAG,CAAClK,KAAK,CAAC,CAAC,CAAC;IACpB;IACA,OAAOkK,GAAG;EACZ;EACA,SAASG,QAAQA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC5B,IAAIC,SAAS,GAAGD,GAAG,IAAI,GAAG;IAC1B,IAAIE,OAAO,GAAG,IAAIC,MAAM,CAACF,SAAS,GAAG,MAAM,EAAE,GAAG,CAAC;IACjD,OAAOF,KAAK,CAAC/D,IAAI,CAACiE,SAAS,CAAC,CAACC,OAAO,CAACA,OAAO,EAAED,SAAS,CAAC;EAC1D;EACA,IAAIG,QAAQ,GAAG,SAASA,QAAQA,CAACC,IAAI,EAAE;IACrC,IAAI,CAACA,IAAI,CAACC,IAAI,IAAI,CAACD,IAAI,CAACE,GAAG,EAAE;MAC3B,OAAO,EAAE;IACX;IACA,IAAIC,MAAM,EAAEC,wBAAwB,EAAEC,kBAAkB;IACxD,IAAI;MACF,IAAIL,IAAI,CAACC,IAAI,EAAE;QACbI,kBAAkB,GAAG,IAAIC,GAAG,CAACN,IAAI,CAACO,WAAW,CAAC,CAACC,QAAQ;QACvDL,MAAM,GAAG,IAAIG,GAAG,CAACb,QAAQ,CAAC,CAACO,IAAI,CAACO,WAAW,CAACV,OAAO,CAACQ,kBAAkB,EAAE,EAAE,CAAC,EAAEL,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;MAC3F,CAAC,MAAM;QACLE,MAAM,GAAG,IAAIG,GAAG,CAACN,IAAI,CAACE,GAAG,CAAC;QAC1BE,wBAAwB,GAAG,IAAI;MACjC;IACF,CAAC,CAAC,OAAOrO,CAAC,EAAE;MACV0O,OAAO,CAACC,KAAK,CAAC3O,CAAC,CAAC;MAChB,OAAO,EAAE;IACX;IACA,KAAK,IAAIe,CAAC,IAAIkN,IAAI,CAACW,eAAe,EAAE;MAClCR,MAAM,CAACS,YAAY,CAACpF,MAAM,CAAC1I,CAAC,EAAE2I,MAAM,CAACuE,IAAI,CAACW,eAAe,CAAC7N,CAAC,CAAC,CAAC,CAAC;IAChE;IACA,IAAI+N,oBAAoB,GAAGC,6BAA6B,CAACd,IAAI,CAACjF,cAAc,CAAC;IAC7E,IAAI8F,oBAAoB,IAAIA,oBAAoB,CAAC9N,MAAM,EAAE;MACvD,IAAIyL,mBAAmB,CAACE,mBAAmB,CAACsB,IAAI,CAAC,IAAII,wBAAwB,EAAE;QAC7ED,MAAM,CAACS,YAAY,CAACpF,MAAM,CAAC4D,wBAAwB,EAAEyB,oBAAoB,CAAC;MAC5E,CAAC,MAAM;QACLV,MAAM,CAACK,QAAQ,GAAGf,QAAQ,CAAC,CAACL,wBAAwB,GAAGZ,mBAAmB,CAACS,0BAA0B,CAAC,CAAC,GAAG4B,oBAAoB,EAAEV,MAAM,CAACK,QAAQ,CAAC,CAAC;MACnJ;IACF;IACA,IAAIH,kBAAkB,EAAE;MACtBF,MAAM,CAACK,QAAQ,GAAGf,QAAQ,CAAC,CAACY,kBAAkB,EAAEF,MAAM,CAACK,QAAQ,CAAC,CAAC;IACnE,CAAC,MAAM;MACLL,MAAM,CAACK,QAAQ,GAAGf,QAAQ,CAAC,CAACU,MAAM,CAACK,QAAQ,CAAC,CAAC;IAC/C;IACA,OAAOL,MAAM,CAACY,IAAI;EACpB,CAAC;EACD,SAASD,6BAA6BA,CAAC/F,cAAc,EAAE;IACrD,IAAI,CAACzF,KAAK,CAACS,OAAO,CAACgF,cAAc,CAAC,EAAE;MAClC,OAAO,EAAE;IACX;IACA,IAAIiG,gBAAgB,GAAG,EAAE;IACzB,KAAK,IAAIlO,CAAC,GAAG,CAAC,EAAEmO,CAAC,GAAGlG,cAAc,CAAChI,MAAM,EAAED,CAAC,GAAGmO,CAAC,EAAEnO,CAAC,EAAE,EAAE;MACrD,IAAIoO,mBAAmB,GAAG,EAAE;MAC5B,KAAK,IAAI5N,GAAG,IAAIyH,cAAc,CAACjI,CAAC,CAAC,EAAE;QACjC,IAAIiI,cAAc,CAACjI,CAAC,CAAC,CAACQ,GAAG,CAAC,KAAK2I,SAAS,IAAIlB,cAAc,CAACjI,CAAC,CAAC,CAACQ,GAAG,CAAC,KAAK,IAAI,EAAE;QAC7E,IAAI6N,YAAY,GAAG3C,mBAAmB,CAACM,eAAe,CAACxL,GAAG,CAAC;QAC3D,IAAI,CAAC6N,YAAY,EAAE;UACjBA,YAAY,GAAG7N,GAAG;QACpB;QACA,IAAIyH,cAAc,CAACjI,CAAC,CAAC,CAACQ,GAAG,CAAC,KAAK,GAAG,EAAE;UAClC4N,mBAAmB,CAAC7M,IAAI,CAAC8M,YAAY,CAAC;QACxC,CAAC,MAAM,IAAI7N,GAAG,KAAK,KAAK,EAAE;UACxB4N,mBAAmB,CAAC7M,IAAI,CAAC0G,cAAc,CAACjI,CAAC,CAAC,CAACQ,GAAG,CAAC,CAAC;QAClD,CAAC,MAAM;UACL,IAAIK,KAAK,GAAGoH,cAAc,CAACjI,CAAC,CAAC,CAACQ,GAAG,CAAC;UAClC,IAAI6N,YAAY,KAAK,IAAI,EAAE;YACzBxN,KAAK,GAAG0L,mBAAmB,CAACG,kBAAkB,CAAC7L,KAAK,IAAI,EAAE,CAAC,CAAC;YAC5DA,KAAK,GAAGA,KAAK,CAACkM,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;UACpC;UACAqB,mBAAmB,CAAC7M,IAAI,CAAC,CAAC8M,YAAY,EAAExN,KAAK,CAAC,CAACgI,IAAI,CAAC6C,mBAAmB,CAACW,6BAA6B,CAAC,CAAC,CAAC,CAAC;QAC3G;MACF;MACA6B,gBAAgB,CAAC3M,IAAI,CAAC6M,mBAAmB,CAACvF,IAAI,CAAC6C,mBAAmB,CAACU,qBAAqB,CAAC,CAAC,CAAC,CAAC;IAC9F;IACA,OAAO8B,gBAAgB,CAACrF,IAAI,CAAC6C,mBAAmB,CAACS,0BAA0B,CAAC,CAAC,CAAC;EAChF;EACA,IAAImC,GAAG,GAAG,SAASA,GAAGA,CAACC,OAAO,EAAEC,cAAc,EAAE;IAC9C,OAAOvB,QAAQ,CAACxL,gBAAgB,CAACA,gBAAgB,CAAC,CAAC,CAAC,EAAE+M,cAAc,CAAC,EAAED,OAAO,CAAC,CAAC;EAClF,CAAC;EACD,SAASE,4BAA4BA,CAAC/G,OAAO,EAAE;IAC7C,OAAOA,OAAO,CAAC+F,WAAW;EAC5B;EACA,IAAIiB,SAAS,GAAG,SAASA,SAASA,CAACC,WAAW,EAAEC,EAAE,EAAE;IAClD,OAAO,YAAY;MACjB,KAAK,IAAIC,IAAI,GAAGnN,SAAS,CAACzB,MAAM,EAAE6O,IAAI,GAAG,IAAItM,KAAK,CAACqM,IAAI,CAAC,EAAEE,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGF,IAAI,EAAEE,IAAI,EAAE,EAAE;QACvFD,IAAI,CAACC,IAAI,CAAC,GAAGrN,SAAS,CAACqN,IAAI,CAAC;MAC9B;MACA,IAAID,IAAI,CAAC7O,MAAM,KAAK2O,EAAE,CAAC3O,MAAM,IAAI,OAAO6O,IAAI,CAACA,IAAI,CAAC7O,MAAM,GAAG,CAAC,CAAC,KAAK,WAAW,EAAE;QAC7E,IAAI,OAAO6O,IAAI,CAACA,IAAI,CAAC7O,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,EAAE;UAC/C,MAAM,IAAI+O,KAAK,CAAC,8BAA8B,CAAC;QACjD;QACAJ,EAAE,CAACvM,IAAI,CAACb,KAAK,CAACoN,EAAE,EAAE,CAACD,WAAW,CAAC,CAACM,MAAM,CAACH,IAAI,CAAC,CAAC;MAC/C,CAAC,MAAM;QACL,OAAO,IAAIlI,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;UAC5C,IAAIxB,QAAQ,GAAG,SAASA,QAAQA,CAAC9B,GAAG,EAAE;YACpC,IAAIA,GAAG,EAAE;cACP,OAAOsD,MAAM,CAACtD,GAAG,CAAC;YACpB,CAAC,MAAM;cACL,KAAK,IAAI0L,KAAK,GAAGxN,SAAS,CAACzB,MAAM,EAAEkP,OAAO,GAAG,IAAI3M,KAAK,CAAC0M,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;gBACpHD,OAAO,CAACC,KAAK,GAAG,CAAC,CAAC,GAAG1N,SAAS,CAAC0N,KAAK,CAAC;cACvC;cACAvI,OAAO,CAACsI,OAAO,CAAClP,MAAM,GAAG,CAAC,GAAGkP,OAAO,GAAGA,OAAO,CAAC,CAAC,CAAC,CAAC;YACpD;UACF,CAAC;UACDL,IAAI,CAACO,GAAG,CAAC,CAAC;UACVP,IAAI,CAACvN,IAAI,CAAC+D,QAAQ,CAAC;UACnBsJ,EAAE,CAACvM,IAAI,CAACb,KAAK,CAACoN,EAAE,EAAE,CAACD,WAAW,CAAC,CAACM,MAAM,CAACH,IAAI,CAAC,CAAC;QAC/C,CAAC,CAAC;MACJ;IACF,CAAC;EACH,CAAC;EACD,IAAIQ,QAAQ,GAAG,YAAY;IACzB,SAASA,QAAQA,CAACpC,IAAI,EAAE;MACtBzN,eAAe,CAAC,IAAI,EAAE6P,QAAQ,CAAC;MAC/B1O,iBAAiB,CAAC,IAAI,EAAE,SAAS,EAAE;QACjC2O,UAAU,EAAE,aAAa,CAACN,MAAM,CAACrL,OAAO,CAAC;QACzCiE,SAAS,EAAE,EAAE;QACb4F,WAAW,EAAE,EAAE;QACf5B,sBAAsB,EAAEH,mBAAmB,CAACC,UAAU,CAAC;MACzD,CAAC,CAAC;MACF,IAAI,CAACjE,OAAO,GAAGjG,gBAAgB,CAACA,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiG,OAAO,CAAC,EAAEwF,IAAI,IAAI,CAAC,CAAC,CAAC;MAC/E,IAAI,CAACuB,4BAA4B,CAAC,IAAI,CAAC/G,OAAO,CAAC,EAAE;QAC/C,MAAM7D,aAAa,CAACC,gCAAgC;MACtD;MACA,IAAI,CAAC4H,mBAAmB,CAACI,eAAe,CAAC,IAAI,CAACpE,OAAO,CAAC,EAAE;QACtD,MAAM7D,aAAa,CAACI,+BAA+B;MACrD;IACF;IACAxD,YAAY,CAAC6O,QAAQ,EAAE,CAAC;MACtB9O,GAAG,EAAE,KAAK;MACVK,KAAK,EAAE,SAAS2O,KAAKA,CAACC,UAAU,EAAE;QAChC,OAAOnB,GAAG,CAACmB,UAAU,EAAE,IAAI,CAAC/H,OAAO,CAAC;MACtC;IACF,CAAC,EAAE;MACDlH,GAAG,EAAE,QAAQ;MACbK,KAAK,EAAE,SAAS6O,QAAQA,CAACjI,aAAa,EAAEkI,iBAAiB,EAAEjI,OAAO,EAAE;QAClE,IAAIpC,QAAQ;QACZ,IAAI,OAAOqK,iBAAiB,KAAK,UAAU,EAAE;UAC3CrK,QAAQ,GAAGqK,iBAAiB;QAC9B,CAAC,MAAM;UACLjI,OAAO,GAAGiI,iBAAiB,IAAI,CAAC,CAAC;QACnC;QACA,IAAI,CAAClI,aAAa,IAAItI,SAAS,CAACsI,aAAa,CAAC,KAAK,QAAQ,EAAE;UAC3D,OAAOtC,OAAO,CAAC,IAAI,EAAEtB,aAAa,CAACe,sBAAsB,EAAEU,QAAQ,CAAC;QACtE;QACA,IAAIsK,aAAa,GAAGnO,gBAAgB,CAACA,gBAAgB,CAAC,CAAC,CAAC,EAAE,IAAI,CAACiG,OAAO,CAAC,EAAEA,OAAO,CAAC;QACjF,IAAImI,IAAI,GAAGpI,aAAa,IAAI,CAAC,CAAC;UAC5BqI,eAAe,GAAGD,IAAI,CAACrK,GAAG;QAC5B,OAAOiC,aAAa,CAACjC,GAAG;QACxB,IAAIA,GAAG,GAAGsK,eAAe,IAAI,IAAIC,cAAc,CAAC,CAAC;QACjD,OAAOrB,SAAS,CAAC,IAAI,EAAElH,MAAM,CAAC,CAAChC,GAAG,EAAEiC,aAAa,EAAEmI,aAAa,EAAEtK,QAAQ,CAAC;MAC7E;IACF,CAAC,CAAC,CAAC;IACH,OAAOgK,QAAQ;EACjB,CAAC,CAAC,CAAC;EAEH,SAASU,qBAAqBA,CAACC,CAAC,EAAE9B,CAAC,EAAE;IACnC,IAAI+B,CAAC,GAAG,IAAI,IAAID,CAAC,GAAG,IAAI,GAAG,WAAW,IAAI,OAAO5Q,MAAM,IAAI4Q,CAAC,CAAC5Q,MAAM,CAACC,QAAQ,CAAC,IAAI2Q,CAAC,CAAC,YAAY,CAAC;IAChG,IAAI,IAAI,IAAIC,CAAC,EAAE;MACb,IAAIjR,CAAC;QACHkD,CAAC;QACDnC,CAAC;QACDmQ,CAAC;QACDC,CAAC,GAAG,EAAE;QACN/M,CAAC,GAAG,CAAC,CAAC;QACNrB,CAAC,GAAG,CAAC,CAAC;MACR,IAAI;QACF,IAAIhC,CAAC,GAAG,CAACkQ,CAAC,GAAGA,CAAC,CAAC7N,IAAI,CAAC4N,CAAC,CAAC,EAAEvM,IAAI,EAAE,CAAC,KAAKyK,CAAC,EAAE;UACrC,IAAI7N,MAAM,CAAC4P,CAAC,CAAC,KAAKA,CAAC,EAAE;UACrB7M,CAAC,GAAG,CAAC,CAAC;QACR,CAAC,MAAM,OAAO,EAAEA,CAAC,GAAG,CAACpE,CAAC,GAAGe,CAAC,CAACqC,IAAI,CAAC6N,CAAC,CAAC,EAAE9M,IAAI,CAAC,KAAKgN,CAAC,CAAC7O,IAAI,CAACtC,CAAC,CAAC4B,KAAK,CAAC,EAAEuP,CAAC,CAACnQ,MAAM,KAAKkO,CAAC,CAAC,EAAE9K,CAAC,GAAG,CAAC,CAAC,CAAC;MACzF,CAAC,CAAC,OAAO4M,CAAC,EAAE;QACVjO,CAAC,GAAG,CAAC,CAAC,EAAEG,CAAC,GAAG8N,CAAC;MACf,CAAC,SAAS;QACR,IAAI;UACF,IAAI,CAAC5M,CAAC,IAAI,IAAI,IAAI6M,CAAC,CAACvM,MAAM,KAAKwM,CAAC,GAAGD,CAAC,CAACvM,MAAM,CAAC,CAAC,EAAErD,MAAM,CAAC6P,CAAC,CAAC,KAAKA,CAAC,CAAC,EAAE;QACnE,CAAC,SAAS;UACR,IAAInO,CAAC,EAAE,MAAMG,CAAC;QAChB;MACF;MACA,OAAOiO,CAAC;IACV;EACF;EACA,SAASC,OAAOA,CAACpR,CAAC,EAAEgR,CAAC,EAAE;IACrB,IAAIC,CAAC,GAAG5P,MAAM,CAACW,IAAI,CAAChC,CAAC,CAAC;IACtB,IAAIqB,MAAM,CAACY,qBAAqB,EAAE;MAChC,IAAIc,CAAC,GAAG1B,MAAM,CAACY,qBAAqB,CAACjC,CAAC,CAAC;MACvCgR,CAAC,KAAKjO,CAAC,GAAGA,CAAC,CAACZ,MAAM,CAAC,UAAU6O,CAAC,EAAE;QAC9B,OAAO3P,MAAM,CAACgB,wBAAwB,CAACrC,CAAC,EAAEgR,CAAC,CAAC,CAAC9P,UAAU;MACzD,CAAC,CAAC,CAAC,EAAE+P,CAAC,CAAC3O,IAAI,CAACC,KAAK,CAAC0O,CAAC,EAAElO,CAAC,CAAC;IACzB;IACA,OAAOkO,CAAC;EACV;EACA,SAASI,cAAcA,CAACrR,CAAC,EAAE;IACzB,KAAK,IAAIgR,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGvO,SAAS,CAACzB,MAAM,EAAEgQ,CAAC,EAAE,EAAE;MACzC,IAAIC,CAAC,GAAG,IAAI,IAAIxO,SAAS,CAACuO,CAAC,CAAC,GAAGvO,SAAS,CAACuO,CAAC,CAAC,GAAG,CAAC,CAAC;MAChDA,CAAC,GAAG,CAAC,GAAGI,OAAO,CAAC/P,MAAM,CAAC4P,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAACtO,OAAO,CAAC,UAAUqO,CAAC,EAAE;QAClDM,eAAe,CAACtR,CAAC,EAAEgR,CAAC,EAAEC,CAAC,CAACD,CAAC,CAAC,CAAC;MAC7B,CAAC,CAAC,GAAG3P,MAAM,CAACuB,yBAAyB,GAAGvB,MAAM,CAACwB,gBAAgB,CAAC7C,CAAC,EAAEqB,MAAM,CAACuB,yBAAyB,CAACqO,CAAC,CAAC,CAAC,GAAGG,OAAO,CAAC/P,MAAM,CAAC4P,CAAC,CAAC,CAAC,CAACtO,OAAO,CAAC,UAAUqO,CAAC,EAAE;QAChJ3P,MAAM,CAACC,cAAc,CAACtB,CAAC,EAAEgR,CAAC,EAAE3P,MAAM,CAACgB,wBAAwB,CAAC4O,CAAC,EAAED,CAAC,CAAC,CAAC;MACpE,CAAC,CAAC;IACJ;IACA,OAAOhR,CAAC;EACV;EACA,SAASuR,mBAAmBA,CAAA,EAAG;IAC7BA,mBAAmB,GAAG,SAAAA,CAAA,EAAY;MAChC,OAAOvR,CAAC;IACV,CAAC;IACD,IAAIiR,CAAC;MACHjR,CAAC,GAAG,CAAC,CAAC;MACNgR,CAAC,GAAG3P,MAAM,CAACd,SAAS;MACpB2C,CAAC,GAAG8N,CAAC,CAACQ,cAAc;MACpBzO,CAAC,GAAG1B,MAAM,CAACC,cAAc,IAAI,UAAU2P,CAAC,EAAEjR,CAAC,EAAEgR,CAAC,EAAE;QAC9CC,CAAC,CAACjR,CAAC,CAAC,GAAGgR,CAAC,CAACpP,KAAK;MAChB,CAAC;MACDb,CAAC,GAAG,UAAU,IAAI,OAAOX,MAAM,GAAGA,MAAM,GAAG,CAAC,CAAC;MAC7C+Q,CAAC,GAAGpQ,CAAC,CAACV,QAAQ,IAAI,YAAY;MAC9BoR,CAAC,GAAG1Q,CAAC,CAAC2Q,aAAa,IAAI,iBAAiB;MACxCR,CAAC,GAAGnQ,CAAC,CAAC4Q,WAAW,IAAI,eAAe;IACtC,SAASlS,MAAMA,CAACwR,CAAC,EAAEjR,CAAC,EAAEgR,CAAC,EAAE;MACvB,OAAO3P,MAAM,CAACC,cAAc,CAAC2P,CAAC,EAAEjR,CAAC,EAAE;QACjC4B,KAAK,EAAEoP,CAAC;QACR9P,UAAU,EAAE,CAAC,CAAC;QACdC,YAAY,EAAE,CAAC,CAAC;QAChBC,QAAQ,EAAE,CAAC;MACb,CAAC,CAAC,EAAE6P,CAAC,CAACjR,CAAC,CAAC;IACV;IACA,IAAI;MACFP,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;IAChB,CAAC,CAAC,OAAOwR,CAAC,EAAE;MACVxR,MAAM,GAAG,SAAAA,CAAUwR,CAAC,EAAEjR,CAAC,EAAEgR,CAAC,EAAE;QAC1B,OAAOC,CAAC,CAACjR,CAAC,CAAC,GAAGgR,CAAC;MACjB,CAAC;IACH;IACA,SAASY,IAAIA,CAACX,CAAC,EAAEjR,CAAC,EAAEgR,CAAC,EAAE9N,CAAC,EAAE;MACxB,IAAInC,CAAC,GAAGf,CAAC,IAAIA,CAAC,CAACO,SAAS,YAAYsR,SAAS,GAAG7R,CAAC,GAAG6R,SAAS;QAC3DV,CAAC,GAAG9P,MAAM,CAACyQ,MAAM,CAAC/Q,CAAC,CAACR,SAAS,CAAC;QAC9BkR,CAAC,GAAG,IAAIM,OAAO,CAAC7O,CAAC,IAAI,EAAE,CAAC;MAC1B,OAAOH,CAAC,CAACoO,CAAC,EAAE,SAAS,EAAE;QACrBvP,KAAK,EAAEoQ,gBAAgB,CAACf,CAAC,EAAED,CAAC,EAAES,CAAC;MACjC,CAAC,CAAC,EAAEN,CAAC;IACP;IACA,SAASc,QAAQA,CAAChB,CAAC,EAAEjR,CAAC,EAAEgR,CAAC,EAAE;MACzB,IAAI;QACF,OAAO;UACL1H,IAAI,EAAE,QAAQ;UACd4I,GAAG,EAAEjB,CAAC,CAAC7N,IAAI,CAACpD,CAAC,EAAEgR,CAAC;QAClB,CAAC;MACH,CAAC,CAAC,OAAOC,CAAC,EAAE;QACV,OAAO;UACL3H,IAAI,EAAE,OAAO;UACb4I,GAAG,EAAEjB;QACP,CAAC;MACH;IACF;IACAjR,CAAC,CAAC4R,IAAI,GAAGA,IAAI;IACb,IAAIO,CAAC,GAAG,gBAAgB;MACtBjD,CAAC,GAAG,gBAAgB;MACpB9K,CAAC,GAAG,WAAW;MACfF,CAAC,GAAG,WAAW;MACf0G,CAAC,GAAG,CAAC,CAAC;IACR,SAASiH,SAASA,CAAA,EAAG,CAAC;IACtB,SAASO,iBAAiBA,CAAA,EAAG,CAAC;IAC9B,SAASC,0BAA0BA,CAAA,EAAG,CAAC;IACvC,IAAIC,CAAC,GAAG,CAAC,CAAC;IACV7S,MAAM,CAAC6S,CAAC,EAAEnB,CAAC,EAAE,YAAY;MACvB,OAAO,IAAI;IACb,CAAC,CAAC;IACF,IAAIoB,CAAC,GAAGlR,MAAM,CAACmR,cAAc;MAC3BC,CAAC,GAAGF,CAAC,IAAIA,CAAC,CAACA,CAAC,CAACG,MAAM,CAAC,EAAE,CAAC,CAAC,CAAC;IAC3BD,CAAC,IAAIA,CAAC,KAAKzB,CAAC,IAAI9N,CAAC,CAACE,IAAI,CAACqP,CAAC,EAAEtB,CAAC,CAAC,KAAKmB,CAAC,GAAGG,CAAC,CAAC;IACvC,IAAIE,CAAC,GAAGN,0BAA0B,CAAC9R,SAAS,GAAGsR,SAAS,CAACtR,SAAS,GAAGc,MAAM,CAACyQ,MAAM,CAACQ,CAAC,CAAC;IACrF,SAASM,qBAAqBA,CAAC3B,CAAC,EAAE;MAChC,CAAC,MAAM,EAAE,OAAO,EAAE,QAAQ,CAAC,CAACtO,OAAO,CAAC,UAAU3C,CAAC,EAAE;QAC/CP,MAAM,CAACwR,CAAC,EAAEjR,CAAC,EAAE,UAAUiR,CAAC,EAAE;UACxB,OAAO,IAAI,CAAC4B,OAAO,CAAC7S,CAAC,EAAEiR,CAAC,CAAC;QAC3B,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;IACA,SAAS6B,aAAaA,CAAC7B,CAAC,EAAEjR,CAAC,EAAE;MAC3B,SAAS+S,MAAMA,CAAC/B,CAAC,EAAEjO,CAAC,EAAEhC,CAAC,EAAEoQ,CAAC,EAAE;QAC1B,IAAIM,CAAC,GAAGQ,QAAQ,CAAChB,CAAC,CAACD,CAAC,CAAC,EAAEC,CAAC,EAAElO,CAAC,CAAC;QAC5B,IAAI,OAAO,KAAK0O,CAAC,CAACnI,IAAI,EAAE;UACtB,IAAI4H,CAAC,GAAGO,CAAC,CAACS,GAAG;YACXC,CAAC,GAAGjB,CAAC,CAACtP,KAAK;UACb,OAAOuQ,CAAC,IAAI,QAAQ,IAAI,OAAOA,CAAC,IAAIjP,CAAC,CAACE,IAAI,CAAC+O,CAAC,EAAE,SAAS,CAAC,GAAGnS,CAAC,CAAC4H,OAAO,CAACuK,CAAC,CAACa,OAAO,CAAC,CAACxL,IAAI,CAAC,UAAUyJ,CAAC,EAAE;YAChG8B,MAAM,CAAC,MAAM,EAAE9B,CAAC,EAAElQ,CAAC,EAAEoQ,CAAC,CAAC;UACzB,CAAC,EAAE,UAAUF,CAAC,EAAE;YACd8B,MAAM,CAAC,OAAO,EAAE9B,CAAC,EAAElQ,CAAC,EAAEoQ,CAAC,CAAC;UAC1B,CAAC,CAAC,GAAGnR,CAAC,CAAC4H,OAAO,CAACuK,CAAC,CAAC,CAAC3K,IAAI,CAAC,UAAUyJ,CAAC,EAAE;YAClCC,CAAC,CAACtP,KAAK,GAAGqP,CAAC,EAAElQ,CAAC,CAACmQ,CAAC,CAAC;UACnB,CAAC,EAAE,UAAUD,CAAC,EAAE;YACd,OAAO8B,MAAM,CAAC,OAAO,EAAE9B,CAAC,EAAElQ,CAAC,EAAEoQ,CAAC,CAAC;UACjC,CAAC,CAAC;QACJ;QACAA,CAAC,CAACM,CAAC,CAACS,GAAG,CAAC;MACV;MACA,IAAIlB,CAAC;MACLjO,CAAC,CAAC,IAAI,EAAE,SAAS,EAAE;QACjBnB,KAAK,EAAE,SAAAA,CAAUqP,CAAC,EAAE/N,CAAC,EAAE;UACrB,SAAS+P,0BAA0BA,CAAA,EAAG;YACpC,OAAO,IAAIjT,CAAC,CAAC,UAAUA,CAAC,EAAEgR,CAAC,EAAE;cAC3B+B,MAAM,CAAC9B,CAAC,EAAE/N,CAAC,EAAElD,CAAC,EAAEgR,CAAC,CAAC;YACpB,CAAC,CAAC;UACJ;UACA,OAAOA,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAACxJ,IAAI,CAACyL,0BAA0B,EAAEA,0BAA0B,CAAC,GAAGA,0BAA0B,CAAC,CAAC;QAC9G;MACF,CAAC,CAAC;IACJ;IACA,SAASjB,gBAAgBA,CAAChS,CAAC,EAAEgR,CAAC,EAAE9N,CAAC,EAAE;MACjC,IAAIH,CAAC,GAAGoP,CAAC;MACT,OAAO,UAAUpR,CAAC,EAAEoQ,CAAC,EAAE;QACrB,IAAIpO,CAAC,KAAKqB,CAAC,EAAE,MAAM,IAAI2L,KAAK,CAAC,8BAA8B,CAAC;QAC5D,IAAIhN,CAAC,KAAKmB,CAAC,EAAE;UACX,IAAI,OAAO,KAAKnD,CAAC,EAAE,MAAMoQ,CAAC;UAC1B,OAAO;YACLvP,KAAK,EAAEqP,CAAC;YACR9M,IAAI,EAAE,CAAC;UACT,CAAC;QACH;QACA,KAAKjB,CAAC,CAACgQ,MAAM,GAAGnS,CAAC,EAAEmC,CAAC,CAACgP,GAAG,GAAGf,CAAC,IAAI;UAC9B,IAAIM,CAAC,GAAGvO,CAAC,CAACiQ,QAAQ;UAClB,IAAI1B,CAAC,EAAE;YACL,IAAIP,CAAC,GAAGkC,mBAAmB,CAAC3B,CAAC,EAAEvO,CAAC,CAAC;YACjC,IAAIgO,CAAC,EAAE;cACL,IAAIA,CAAC,KAAKtG,CAAC,EAAE;cACb,OAAOsG,CAAC;YACV;UACF;UACA,IAAI,MAAM,KAAKhO,CAAC,CAACgQ,MAAM,EAAEhQ,CAAC,CAACmQ,IAAI,GAAGnQ,CAAC,CAACoQ,KAAK,GAAGpQ,CAAC,CAACgP,GAAG,CAAC,KAAK,IAAI,OAAO,KAAKhP,CAAC,CAACgQ,MAAM,EAAE;YAC/E,IAAInQ,CAAC,KAAKoP,CAAC,EAAE,MAAMpP,CAAC,GAAGmB,CAAC,EAAEhB,CAAC,CAACgP,GAAG;YAC/BhP,CAAC,CAACqQ,iBAAiB,CAACrQ,CAAC,CAACgP,GAAG,CAAC;UAC5B,CAAC,MAAM,QAAQ,KAAKhP,CAAC,CAACgQ,MAAM,IAAIhQ,CAAC,CAACsQ,MAAM,CAAC,QAAQ,EAAEtQ,CAAC,CAACgP,GAAG,CAAC;UACzDnP,CAAC,GAAGqB,CAAC;UACL,IAAIkO,CAAC,GAAGL,QAAQ,CAACjS,CAAC,EAAEgR,CAAC,EAAE9N,CAAC,CAAC;UACzB,IAAI,QAAQ,KAAKoP,CAAC,CAAChJ,IAAI,EAAE;YACvB,IAAIvG,CAAC,GAAGG,CAAC,CAACiB,IAAI,GAAGD,CAAC,GAAGgL,CAAC,EAAEoD,CAAC,CAACJ,GAAG,KAAKtH,CAAC,EAAE;YACrC,OAAO;cACLhJ,KAAK,EAAE0Q,CAAC,CAACJ,GAAG;cACZ/N,IAAI,EAAEjB,CAAC,CAACiB;YACV,CAAC;UACH;UACA,OAAO,KAAKmO,CAAC,CAAChJ,IAAI,KAAKvG,CAAC,GAAGmB,CAAC,EAAEhB,CAAC,CAACgQ,MAAM,GAAG,OAAO,EAAEhQ,CAAC,CAACgP,GAAG,GAAGI,CAAC,CAACJ,GAAG,CAAC;QAClE;MACF,CAAC;IACH;IACA,SAASkB,mBAAmBA,CAACpT,CAAC,EAAEgR,CAAC,EAAE;MACjC,IAAI9N,CAAC,GAAG8N,CAAC,CAACkC,MAAM;QACdnQ,CAAC,GAAG/C,CAAC,CAACK,QAAQ,CAAC6C,CAAC,CAAC;MACnB,IAAIH,CAAC,KAAKkO,CAAC,EAAE,OAAOD,CAAC,CAACmC,QAAQ,GAAG,IAAI,EAAE,OAAO,KAAKjQ,CAAC,IAAIlD,CAAC,CAACK,QAAQ,CAACqE,MAAM,KAAKsM,CAAC,CAACkC,MAAM,GAAG,QAAQ,EAAElC,CAAC,CAACkB,GAAG,GAAGjB,CAAC,EAAEmC,mBAAmB,CAACpT,CAAC,EAAEgR,CAAC,CAAC,EAAE,OAAO,KAAKA,CAAC,CAACkC,MAAM,CAAC,IAAI,QAAQ,KAAKhQ,CAAC,KAAK8N,CAAC,CAACkC,MAAM,GAAG,OAAO,EAAElC,CAAC,CAACkB,GAAG,GAAG,IAAIvR,SAAS,CAAC,mCAAmC,GAAGuC,CAAC,GAAG,UAAU,CAAC,CAAC,EAAE0H,CAAC;MACxR,IAAI7J,CAAC,GAAGkR,QAAQ,CAAClP,CAAC,EAAE/C,CAAC,CAACK,QAAQ,EAAE2Q,CAAC,CAACkB,GAAG,CAAC;MACtC,IAAI,OAAO,KAAKnR,CAAC,CAACuI,IAAI,EAAE,OAAO0H,CAAC,CAACkC,MAAM,GAAG,OAAO,EAAElC,CAAC,CAACkB,GAAG,GAAGnR,CAAC,CAACmR,GAAG,EAAElB,CAAC,CAACmC,QAAQ,GAAG,IAAI,EAAEvI,CAAC;MACtF,IAAIuG,CAAC,GAAGpQ,CAAC,CAACmR,GAAG;MACb,OAAOf,CAAC,GAAGA,CAAC,CAAChN,IAAI,IAAI6M,CAAC,CAAChR,CAAC,CAACyT,UAAU,CAAC,GAAGtC,CAAC,CAACvP,KAAK,EAAEoP,CAAC,CAACvM,IAAI,GAAGzE,CAAC,CAAC0T,OAAO,EAAE,QAAQ,KAAK1C,CAAC,CAACkC,MAAM,KAAKlC,CAAC,CAACkC,MAAM,GAAG,MAAM,EAAElC,CAAC,CAACkB,GAAG,GAAGjB,CAAC,CAAC,EAAED,CAAC,CAACmC,QAAQ,GAAG,IAAI,EAAEvI,CAAC,IAAIuG,CAAC,IAAIH,CAAC,CAACkC,MAAM,GAAG,OAAO,EAAElC,CAAC,CAACkB,GAAG,GAAG,IAAIvR,SAAS,CAAC,kCAAkC,CAAC,EAAEqQ,CAAC,CAACmC,QAAQ,GAAG,IAAI,EAAEvI,CAAC,CAAC;IAChQ;IACA,SAAS+I,YAAYA,CAAC1C,CAAC,EAAE;MACvB,IAAIjR,CAAC,GAAG;QACN4T,MAAM,EAAE3C,CAAC,CAAC,CAAC;MACb,CAAC;MACD,CAAC,IAAIA,CAAC,KAAKjR,CAAC,CAAC6T,QAAQ,GAAG5C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,IAAIA,CAAC,KAAKjR,CAAC,CAAC8T,UAAU,GAAG7C,CAAC,CAAC,CAAC,CAAC,EAAEjR,CAAC,CAAC+T,QAAQ,GAAG9C,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE,IAAI,CAAC+C,UAAU,CAAC1R,IAAI,CAACtC,CAAC,CAAC;IAC5G;IACA,SAASiU,aAAaA,CAAChD,CAAC,EAAE;MACxB,IAAIjR,CAAC,GAAGiR,CAAC,CAACiD,UAAU,IAAI,CAAC,CAAC;MAC1BlU,CAAC,CAACsJ,IAAI,GAAG,QAAQ,EAAE,OAAOtJ,CAAC,CAACkS,GAAG,EAAEjB,CAAC,CAACiD,UAAU,GAAGlU,CAAC;IACnD;IACA,SAAS+R,OAAOA,CAACd,CAAC,EAAE;MAClB,IAAI,CAAC+C,UAAU,GAAG,CAAC;QACjBJ,MAAM,EAAE;MACV,CAAC,CAAC,EAAE3C,CAAC,CAACtO,OAAO,CAACgR,YAAY,EAAE,IAAI,CAAC,EAAE,IAAI,CAACQ,KAAK,CAAC,CAAC,CAAC,CAAC;IACnD;IACA,SAASzB,MAAMA,CAAC1S,CAAC,EAAE;MACjB,IAAIA,CAAC,IAAI,EAAE,KAAKA,CAAC,EAAE;QACjB,IAAIgR,CAAC,GAAGhR,CAAC,CAACmR,CAAC,CAAC;QACZ,IAAIH,CAAC,EAAE,OAAOA,CAAC,CAAC5N,IAAI,CAACpD,CAAC,CAAC;QACvB,IAAI,UAAU,IAAI,OAAOA,CAAC,CAACyE,IAAI,EAAE,OAAOzE,CAAC;QACzC,IAAI,CAACoU,KAAK,CAACpU,CAAC,CAACgB,MAAM,CAAC,EAAE;UACpB,IAAI+B,CAAC,GAAG,CAAC,CAAC;YACRhC,CAAC,GAAG,SAAS0D,IAAIA,CAAA,EAAG;cAClB,OAAO,EAAE1B,CAAC,GAAG/C,CAAC,CAACgB,MAAM,GAAG,IAAIkC,CAAC,CAACE,IAAI,CAACpD,CAAC,EAAE+C,CAAC,CAAC,EAAE,OAAO0B,IAAI,CAAC7C,KAAK,GAAG5B,CAAC,CAAC+C,CAAC,CAAC,EAAE0B,IAAI,CAACN,IAAI,GAAG,CAAC,CAAC,EAAEM,IAAI;cACxF,OAAOA,IAAI,CAAC7C,KAAK,GAAGqP,CAAC,EAAExM,IAAI,CAACN,IAAI,GAAG,CAAC,CAAC,EAAEM,IAAI;YAC7C,CAAC;UACH,OAAO1D,CAAC,CAAC0D,IAAI,GAAG1D,CAAC;QACnB;MACF;MACA,MAAM,IAAIJ,SAAS,CAAC,OAAOX,CAAC,GAAG,kBAAkB,CAAC;IACpD;IACA,OAAOoS,iBAAiB,CAAC7R,SAAS,GAAG8R,0BAA0B,EAAEtP,CAAC,CAAC4P,CAAC,EAAE,aAAa,EAAE;MACnF/Q,KAAK,EAAEyQ,0BAA0B;MACjClR,YAAY,EAAE,CAAC;IACjB,CAAC,CAAC,EAAE4B,CAAC,CAACsP,0BAA0B,EAAE,aAAa,EAAE;MAC/CzQ,KAAK,EAAEwQ,iBAAiB;MACxBjR,YAAY,EAAE,CAAC;IACjB,CAAC,CAAC,EAAEiR,iBAAiB,CAACiC,WAAW,GAAG5U,MAAM,CAAC4S,0BAA0B,EAAEnB,CAAC,EAAE,mBAAmB,CAAC,EAAElR,CAAC,CAACsU,mBAAmB,GAAG,UAAUrD,CAAC,EAAE;MACnI,IAAIjR,CAAC,GAAG,UAAU,IAAI,OAAOiR,CAAC,IAAIA,CAAC,CAAC3Q,WAAW;MAC/C,OAAO,CAAC,CAACN,CAAC,KAAKA,CAAC,KAAKoS,iBAAiB,IAAI,mBAAmB,MAAMpS,CAAC,CAACqU,WAAW,IAAIrU,CAAC,CAACsD,IAAI,CAAC,CAAC;IAC9F,CAAC,EAAEtD,CAAC,CAACuU,IAAI,GAAG,UAAUtD,CAAC,EAAE;MACvB,OAAO5P,MAAM,CAACmT,cAAc,GAAGnT,MAAM,CAACmT,cAAc,CAACvD,CAAC,EAAEoB,0BAA0B,CAAC,IAAIpB,CAAC,CAACwD,SAAS,GAAGpC,0BAA0B,EAAE5S,MAAM,CAACwR,CAAC,EAAEC,CAAC,EAAE,mBAAmB,CAAC,CAAC,EAAED,CAAC,CAAC1Q,SAAS,GAAGc,MAAM,CAACyQ,MAAM,CAACa,CAAC,CAAC,EAAE1B,CAAC;IACxM,CAAC,EAAEjR,CAAC,CAAC0U,KAAK,GAAG,UAAUzD,CAAC,EAAE;MACxB,OAAO;QACL+B,OAAO,EAAE/B;MACX,CAAC;IACH,CAAC,EAAE2B,qBAAqB,CAACE,aAAa,CAACvS,SAAS,CAAC,EAAEd,MAAM,CAACqT,aAAa,CAACvS,SAAS,EAAEkR,CAAC,EAAE,YAAY;MAChG,OAAO,IAAI;IACb,CAAC,CAAC,EAAEzR,CAAC,CAAC8S,aAAa,GAAGA,aAAa,EAAE9S,CAAC,CAAC2U,KAAK,GAAG,UAAU1D,CAAC,EAAED,CAAC,EAAE9N,CAAC,EAAEH,CAAC,EAAEhC,CAAC,EAAE;MACtE,KAAK,CAAC,KAAKA,CAAC,KAAKA,CAAC,GAAG4G,OAAO,CAAC;MAC7B,IAAIwJ,CAAC,GAAG,IAAI2B,aAAa,CAAClB,IAAI,CAACX,CAAC,EAAED,CAAC,EAAE9N,CAAC,EAAEH,CAAC,CAAC,EAAEhC,CAAC,CAAC;MAC9C,OAAOf,CAAC,CAACsU,mBAAmB,CAACtD,CAAC,CAAC,GAAGG,CAAC,GAAGA,CAAC,CAAC1M,IAAI,CAAC,CAAC,CAAC+C,IAAI,CAAC,UAAUyJ,CAAC,EAAE;QAC/D,OAAOA,CAAC,CAAC9M,IAAI,GAAG8M,CAAC,CAACrP,KAAK,GAAGuP,CAAC,CAAC1M,IAAI,CAAC,CAAC;MACpC,CAAC,CAAC;IACJ,CAAC,EAAEmO,qBAAqB,CAACD,CAAC,CAAC,EAAElT,MAAM,CAACkT,CAAC,EAAEzB,CAAC,EAAE,WAAW,CAAC,EAAEzR,MAAM,CAACkT,CAAC,EAAExB,CAAC,EAAE,YAAY;MAC/E,OAAO,IAAI;IACb,CAAC,CAAC,EAAE1R,MAAM,CAACkT,CAAC,EAAE,UAAU,EAAE,YAAY;MACpC,OAAO,oBAAoB;IAC7B,CAAC,CAAC,EAAE3S,CAAC,CAACgC,IAAI,GAAG,UAAUiP,CAAC,EAAE;MACxB,IAAIjR,CAAC,GAAGqB,MAAM,CAAC4P,CAAC,CAAC;QACfD,CAAC,GAAG,EAAE;MACR,KAAK,IAAI9N,CAAC,IAAIlD,CAAC,EAAEgR,CAAC,CAAC1O,IAAI,CAACY,CAAC,CAAC;MAC1B,OAAO8N,CAAC,CAAC4D,OAAO,CAAC,CAAC,EAAE,SAASnQ,IAAIA,CAAA,EAAG;QAClC,OAAOuM,CAAC,CAAChQ,MAAM,GAAG;UAChB,IAAIiQ,CAAC,GAAGD,CAAC,CAACZ,GAAG,CAAC,CAAC;UACf,IAAIa,CAAC,IAAIjR,CAAC,EAAE,OAAOyE,IAAI,CAAC7C,KAAK,GAAGqP,CAAC,EAAExM,IAAI,CAACN,IAAI,GAAG,CAAC,CAAC,EAAEM,IAAI;QACzD;QACA,OAAOA,IAAI,CAACN,IAAI,GAAG,CAAC,CAAC,EAAEM,IAAI;MAC7B,CAAC;IACH,CAAC,EAAEzE,CAAC,CAAC0S,MAAM,GAAGA,MAAM,EAAEX,OAAO,CAACxR,SAAS,GAAG;MACxCD,WAAW,EAAEyR,OAAO;MACpBoC,KAAK,EAAE,SAAAA,CAAUnU,CAAC,EAAE;QAClB,IAAI,IAAI,CAAC6U,IAAI,GAAG,CAAC,EAAE,IAAI,CAACpQ,IAAI,GAAG,CAAC,EAAE,IAAI,CAAC4O,IAAI,GAAG,IAAI,CAACC,KAAK,GAAGrC,CAAC,EAAE,IAAI,CAAC9M,IAAI,GAAG,CAAC,CAAC,EAAE,IAAI,CAACgP,QAAQ,GAAG,IAAI,EAAE,IAAI,CAACD,MAAM,GAAG,MAAM,EAAE,IAAI,CAAChB,GAAG,GAAGjB,CAAC,EAAE,IAAI,CAAC+C,UAAU,CAACrR,OAAO,CAACsR,aAAa,CAAC,EAAE,CAACjU,CAAC,EAAE,KAAK,IAAIgR,CAAC,IAAI,IAAI,EAAE,GAAG,KAAKA,CAAC,CAAC8D,MAAM,CAAC,CAAC,CAAC,IAAI5R,CAAC,CAACE,IAAI,CAAC,IAAI,EAAE4N,CAAC,CAAC,IAAI,CAACoD,KAAK,CAAC,CAACpD,CAAC,CAAC3N,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,IAAI,CAAC2N,CAAC,CAAC,GAAGC,CAAC,CAAC;MACxR,CAAC;MACD8D,IAAI,EAAE,SAAAA,CAAA,EAAY;QAChB,IAAI,CAAC5Q,IAAI,GAAG,CAAC,CAAC;QACd,IAAI8M,CAAC,GAAG,IAAI,CAAC+C,UAAU,CAAC,CAAC,CAAC,CAACE,UAAU;QACrC,IAAI,OAAO,KAAKjD,CAAC,CAAC3H,IAAI,EAAE,MAAM2H,CAAC,CAACiB,GAAG;QACnC,OAAO,IAAI,CAAC8C,IAAI;MAClB,CAAC;MACDzB,iBAAiB,EAAE,SAAAA,CAAUvT,CAAC,EAAE;QAC9B,IAAI,IAAI,CAACmE,IAAI,EAAE,MAAMnE,CAAC;QACtB,IAAIgR,CAAC,GAAG,IAAI;QACZ,SAASiE,MAAMA,CAAC/R,CAAC,EAAEH,CAAC,EAAE;UACpB,OAAOoO,CAAC,CAAC7H,IAAI,GAAG,OAAO,EAAE6H,CAAC,CAACe,GAAG,GAAGlS,CAAC,EAAEgR,CAAC,CAACvM,IAAI,GAAGvB,CAAC,EAAEH,CAAC,KAAKiO,CAAC,CAACkC,MAAM,GAAG,MAAM,EAAElC,CAAC,CAACkB,GAAG,GAAGjB,CAAC,CAAC,EAAE,CAAC,CAAClO,CAAC;QAC1F;QACA,KAAK,IAAIA,CAAC,GAAG,IAAI,CAACiR,UAAU,CAAChT,MAAM,GAAG,CAAC,EAAE+B,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;UACpD,IAAIhC,CAAC,GAAG,IAAI,CAACiT,UAAU,CAACjR,CAAC,CAAC;YACxBoO,CAAC,GAAGpQ,CAAC,CAACmT,UAAU;UAClB,IAAI,MAAM,KAAKnT,CAAC,CAAC6S,MAAM,EAAE,OAAOqB,MAAM,CAAC,KAAK,CAAC;UAC7C,IAAIlU,CAAC,CAAC6S,MAAM,IAAI,IAAI,CAACiB,IAAI,EAAE;YACzB,IAAIpD,CAAC,GAAGvO,CAAC,CAACE,IAAI,CAACrC,CAAC,EAAE,UAAU,CAAC;cAC3BmQ,CAAC,GAAGhO,CAAC,CAACE,IAAI,CAACrC,CAAC,EAAE,YAAY,CAAC;YAC7B,IAAI0Q,CAAC,IAAIP,CAAC,EAAE;cACV,IAAI,IAAI,CAAC2D,IAAI,GAAG9T,CAAC,CAAC8S,QAAQ,EAAE,OAAOoB,MAAM,CAAClU,CAAC,CAAC8S,QAAQ,EAAE,CAAC,CAAC,CAAC;cACzD,IAAI,IAAI,CAACgB,IAAI,GAAG9T,CAAC,CAAC+S,UAAU,EAAE,OAAOmB,MAAM,CAAClU,CAAC,CAAC+S,UAAU,CAAC;YAC3D,CAAC,MAAM,IAAIrC,CAAC,EAAE;cACZ,IAAI,IAAI,CAACoD,IAAI,GAAG9T,CAAC,CAAC8S,QAAQ,EAAE,OAAOoB,MAAM,CAAClU,CAAC,CAAC8S,QAAQ,EAAE,CAAC,CAAC,CAAC;YAC3D,CAAC,MAAM;cACL,IAAI,CAAC3C,CAAC,EAAE,MAAM,IAAInB,KAAK,CAAC,wCAAwC,CAAC;cACjE,IAAI,IAAI,CAAC8E,IAAI,GAAG9T,CAAC,CAAC+S,UAAU,EAAE,OAAOmB,MAAM,CAAClU,CAAC,CAAC+S,UAAU,CAAC;YAC3D;UACF;QACF;MACF,CAAC;MACDN,MAAM,EAAE,SAAAA,CAAUvC,CAAC,EAAEjR,CAAC,EAAE;QACtB,KAAK,IAAIgR,CAAC,GAAG,IAAI,CAACgD,UAAU,CAAChT,MAAM,GAAG,CAAC,EAAEgQ,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;UACpD,IAAIjO,CAAC,GAAG,IAAI,CAACiR,UAAU,CAAChD,CAAC,CAAC;UAC1B,IAAIjO,CAAC,CAAC6Q,MAAM,IAAI,IAAI,CAACiB,IAAI,IAAI3R,CAAC,CAACE,IAAI,CAACL,CAAC,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC8R,IAAI,GAAG9R,CAAC,CAAC+Q,UAAU,EAAE;YAChF,IAAI/S,CAAC,GAAGgC,CAAC;YACT;UACF;QACF;QACAhC,CAAC,KAAK,OAAO,KAAKkQ,CAAC,IAAI,UAAU,KAAKA,CAAC,CAAC,IAAIlQ,CAAC,CAAC6S,MAAM,IAAI5T,CAAC,IAAIA,CAAC,IAAIe,CAAC,CAAC+S,UAAU,KAAK/S,CAAC,GAAG,IAAI,CAAC;QAC5F,IAAIoQ,CAAC,GAAGpQ,CAAC,GAAGA,CAAC,CAACmT,UAAU,GAAG,CAAC,CAAC;QAC7B,OAAO/C,CAAC,CAAC7H,IAAI,GAAG2H,CAAC,EAAEE,CAAC,CAACe,GAAG,GAAGlS,CAAC,EAAEe,CAAC,IAAI,IAAI,CAACmS,MAAM,GAAG,MAAM,EAAE,IAAI,CAACzO,IAAI,GAAG1D,CAAC,CAAC+S,UAAU,EAAElJ,CAAC,IAAI,IAAI,CAACsK,QAAQ,CAAC/D,CAAC,CAAC;MAC1G,CAAC;MACD+D,QAAQ,EAAE,SAAAA,CAAUjE,CAAC,EAAEjR,CAAC,EAAE;QACxB,IAAI,OAAO,KAAKiR,CAAC,CAAC3H,IAAI,EAAE,MAAM2H,CAAC,CAACiB,GAAG;QACnC,OAAO,OAAO,KAAKjB,CAAC,CAAC3H,IAAI,IAAI,UAAU,KAAK2H,CAAC,CAAC3H,IAAI,GAAG,IAAI,CAAC7E,IAAI,GAAGwM,CAAC,CAACiB,GAAG,GAAG,QAAQ,KAAKjB,CAAC,CAAC3H,IAAI,IAAI,IAAI,CAAC0L,IAAI,GAAG,IAAI,CAAC9C,GAAG,GAAGjB,CAAC,CAACiB,GAAG,EAAE,IAAI,CAACgB,MAAM,GAAG,QAAQ,EAAE,IAAI,CAACzO,IAAI,GAAG,KAAK,IAAI,QAAQ,KAAKwM,CAAC,CAAC3H,IAAI,IAAItJ,CAAC,KAAK,IAAI,CAACyE,IAAI,GAAGzE,CAAC,CAAC,EAAE4K,CAAC;MAC3N,CAAC;MACDuK,MAAM,EAAE,SAAAA,CAAUlE,CAAC,EAAE;QACnB,KAAK,IAAIjR,CAAC,GAAG,IAAI,CAACgU,UAAU,CAAChT,MAAM,GAAG,CAAC,EAAEhB,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;UACpD,IAAIgR,CAAC,GAAG,IAAI,CAACgD,UAAU,CAAChU,CAAC,CAAC;UAC1B,IAAIgR,CAAC,CAAC8C,UAAU,KAAK7C,CAAC,EAAE,OAAO,IAAI,CAACiE,QAAQ,CAAClE,CAAC,CAACkD,UAAU,EAAElD,CAAC,CAAC+C,QAAQ,CAAC,EAAEE,aAAa,CAACjD,CAAC,CAAC,EAAEpG,CAAC;QAC7F;MACF,CAAC;MACDwK,KAAK,EAAE,SAAAA,CAAUnE,CAAC,EAAE;QAClB,KAAK,IAAIjR,CAAC,GAAG,IAAI,CAACgU,UAAU,CAAChT,MAAM,GAAG,CAAC,EAAEhB,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;UACpD,IAAIgR,CAAC,GAAG,IAAI,CAACgD,UAAU,CAAChU,CAAC,CAAC;UAC1B,IAAIgR,CAAC,CAAC4C,MAAM,KAAK3C,CAAC,EAAE;YAClB,IAAI/N,CAAC,GAAG8N,CAAC,CAACkD,UAAU;YACpB,IAAI,OAAO,KAAKhR,CAAC,CAACoG,IAAI,EAAE;cACtB,IAAIvG,CAAC,GAAGG,CAAC,CAACgP,GAAG;cACb+B,aAAa,CAACjD,CAAC,CAAC;YAClB;YACA,OAAOjO,CAAC;UACV;QACF;QACA,MAAM,IAAIgN,KAAK,CAAC,uBAAuB,CAAC;MAC1C,CAAC;MACDsF,aAAa,EAAE,SAAAA,CAAUrV,CAAC,EAAEgR,CAAC,EAAE9N,CAAC,EAAE;QAChC,OAAO,IAAI,CAACiQ,QAAQ,GAAG;UACrB9S,QAAQ,EAAEqS,MAAM,CAAC1S,CAAC,CAAC;UACnByT,UAAU,EAAEzC,CAAC;UACb0C,OAAO,EAAExQ;QACX,CAAC,EAAE,MAAM,KAAK,IAAI,CAACgQ,MAAM,KAAK,IAAI,CAAChB,GAAG,GAAGjB,CAAC,CAAC,EAAErG,CAAC;MAChD;IACF,CAAC,EAAE5K,CAAC;EACN;EACA,SAASsV,YAAYA,CAACrE,CAAC,EAAED,CAAC,EAAE;IAC1B,IAAI,QAAQ,IAAI,OAAOC,CAAC,IAAI,CAACA,CAAC,EAAE,OAAOA,CAAC;IACxC,IAAIjR,CAAC,GAAGiR,CAAC,CAAC7Q,MAAM,CAACmV,WAAW,CAAC;IAC7B,IAAI,KAAK,CAAC,KAAKvV,CAAC,EAAE;MAChB,IAAIe,CAAC,GAAGf,CAAC,CAACoD,IAAI,CAAC6N,CAAC,EAAED,CAAC,IAAI,SAAS,CAAC;MACjC,IAAI,QAAQ,IAAI,OAAOjQ,CAAC,EAAE,OAAOA,CAAC;MAClC,MAAM,IAAIJ,SAAS,CAAC,8CAA8C,CAAC;IACrE;IACA,OAAO,CAAC,QAAQ,KAAKqQ,CAAC,GAAGtH,MAAM,GAAG8L,MAAM,EAAEvE,CAAC,CAAC;EAC9C;EACA,SAASwE,cAAcA,CAACxE,CAAC,EAAE;IACzB,IAAIlQ,CAAC,GAAGuU,YAAY,CAACrE,CAAC,EAAE,QAAQ,CAAC;IACjC,OAAO,QAAQ,IAAI,OAAOlQ,CAAC,GAAGA,CAAC,GAAG2I,MAAM,CAAC3I,CAAC,CAAC;EAC7C;EACA,SAAS2U,OAAOA,CAAC3S,CAAC,EAAE;IAClB,yBAAyB;;IAEzB,OAAO2S,OAAO,GAAG,UAAU,IAAI,OAAOtV,MAAM,IAAI,QAAQ,IAAI,OAAOA,MAAM,CAACC,QAAQ,GAAG,UAAU0C,CAAC,EAAE;MAChG,OAAO,OAAOA,CAAC;IACjB,CAAC,GAAG,UAAUA,CAAC,EAAE;MACf,OAAOA,CAAC,IAAI,UAAU,IAAI,OAAO3C,MAAM,IAAI2C,CAAC,CAACzC,WAAW,KAAKF,MAAM,IAAI2C,CAAC,KAAK3C,MAAM,CAACG,SAAS,GAAG,QAAQ,GAAG,OAAOwC,CAAC;IACrH,CAAC,EAAE2S,OAAO,CAAC3S,CAAC,CAAC;EACf;EACA,SAAS4S,kBAAkBA,CAACC,GAAG,EAAEhO,OAAO,EAAEC,MAAM,EAAEgO,KAAK,EAAEC,MAAM,EAAEvU,GAAG,EAAE2Q,GAAG,EAAE;IACzE,IAAI;MACF,IAAI6D,IAAI,GAAGH,GAAG,CAACrU,GAAG,CAAC,CAAC2Q,GAAG,CAAC;MACxB,IAAItQ,KAAK,GAAGmU,IAAI,CAACnU,KAAK;IACxB,CAAC,CAAC,OAAO+M,KAAK,EAAE;MACd9G,MAAM,CAAC8G,KAAK,CAAC;MACb;IACF;IACA,IAAIoH,IAAI,CAAC5R,IAAI,EAAE;MACbyD,OAAO,CAAChG,KAAK,CAAC;IAChB,CAAC,MAAM;MACL+F,OAAO,CAACC,OAAO,CAAChG,KAAK,CAAC,CAAC4F,IAAI,CAACqO,KAAK,EAAEC,MAAM,CAAC;IAC5C;EACF;EACA,SAASE,iBAAiBA,CAACrG,EAAE,EAAE;IAC7B,OAAO,YAAY;MACjB,IAAI/P,IAAI,GAAG,IAAI;QACbiQ,IAAI,GAAGpN,SAAS;MAClB,OAAO,IAAIkF,OAAO,CAAC,UAAUC,OAAO,EAAEC,MAAM,EAAE;QAC5C,IAAI+N,GAAG,GAAGjG,EAAE,CAACpN,KAAK,CAAC3C,IAAI,EAAEiQ,IAAI,CAAC;QAC9B,SAASgG,KAAKA,CAACjU,KAAK,EAAE;UACpB+T,kBAAkB,CAACC,GAAG,EAAEhO,OAAO,EAAEC,MAAM,EAAEgO,KAAK,EAAEC,MAAM,EAAE,MAAM,EAAElU,KAAK,CAAC;QACxE;QACA,SAASkU,MAAMA,CAACvR,GAAG,EAAE;UACnBoR,kBAAkB,CAACC,GAAG,EAAEhO,OAAO,EAAEC,MAAM,EAAEgO,KAAK,EAAEC,MAAM,EAAE,OAAO,EAAEvR,GAAG,CAAC;QACvE;QACAsR,KAAK,CAAC3L,SAAS,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC;EACH;EACA,SAASoH,eAAeA,CAACnR,GAAG,EAAEoB,GAAG,EAAEK,KAAK,EAAE;IACxCL,GAAG,GAAGkU,cAAc,CAAClU,GAAG,CAAC;IACzB,IAAIA,GAAG,IAAIpB,GAAG,EAAE;MACdkB,MAAM,CAACC,cAAc,CAACnB,GAAG,EAAEoB,GAAG,EAAE;QAC9BK,KAAK,EAAEA,KAAK;QACZV,UAAU,EAAE,IAAI;QAChBC,YAAY,EAAE,IAAI;QAClBC,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ,CAAC,MAAM;MACLjB,GAAG,CAACoB,GAAG,CAAC,GAAGK,KAAK;IAClB;IACA,OAAOzB,GAAG;EACZ;EACA,SAAS8V,QAAQA,CAAA,EAAG;IAClBA,QAAQ,GAAG5U,MAAM,CAAC6U,MAAM,GAAG7U,MAAM,CAAC6U,MAAM,CAACC,IAAI,CAAC,CAAC,GAAG,UAAUtV,MAAM,EAAE;MAClE,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0B,SAAS,CAACzB,MAAM,EAAED,CAAC,EAAE,EAAE;QACzC,IAAI2B,MAAM,GAAGD,SAAS,CAAC1B,CAAC,CAAC;QACzB,KAAK,IAAIQ,GAAG,IAAImB,MAAM,EAAE;UACtB,IAAIrB,MAAM,CAACd,SAAS,CAACiR,cAAc,CAACpO,IAAI,CAACV,MAAM,EAAEnB,GAAG,CAAC,EAAE;YACrDV,MAAM,CAACU,GAAG,CAAC,GAAGmB,MAAM,CAACnB,GAAG,CAAC;UAC3B;QACF;MACF;MACA,OAAOV,MAAM;IACf,CAAC;IACD,OAAOoV,QAAQ,CAAC1T,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;EACxC;EACA,SAAS2T,6BAA6BA,CAAC1T,MAAM,EAAE2T,QAAQ,EAAE;IACvD,IAAI3T,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;IAC7B,IAAI7B,MAAM,GAAG,CAAC,CAAC;IACf,IAAIyV,UAAU,GAAGjV,MAAM,CAACW,IAAI,CAACU,MAAM,CAAC;IACpC,IAAInB,GAAG,EAAER,CAAC;IACV,KAAKA,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuV,UAAU,CAACtV,MAAM,EAAED,CAAC,EAAE,EAAE;MACtCQ,GAAG,GAAG+U,UAAU,CAACvV,CAAC,CAAC;MACnB,IAAIsV,QAAQ,CAACvJ,OAAO,CAACvL,GAAG,CAAC,IAAI,CAAC,EAAE;MAChCV,MAAM,CAACU,GAAG,CAAC,GAAGmB,MAAM,CAACnB,GAAG,CAAC;IAC3B;IACA,OAAOV,MAAM;EACf;EACA,SAAS0V,wBAAwBA,CAAC7T,MAAM,EAAE2T,QAAQ,EAAE;IAClD,IAAI3T,MAAM,IAAI,IAAI,EAAE,OAAO,CAAC,CAAC;IAC7B,IAAI7B,MAAM,GAAGuV,6BAA6B,CAAC1T,MAAM,EAAE2T,QAAQ,CAAC;IAC5D,IAAI9U,GAAG,EAAER,CAAC;IACV,IAAIM,MAAM,CAACY,qBAAqB,EAAE;MAChC,IAAIuU,gBAAgB,GAAGnV,MAAM,CAACY,qBAAqB,CAACS,MAAM,CAAC;MAC3D,KAAK3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyV,gBAAgB,CAACxV,MAAM,EAAED,CAAC,EAAE,EAAE;QAC5CQ,GAAG,GAAGiV,gBAAgB,CAACzV,CAAC,CAAC;QACzB,IAAIsV,QAAQ,CAACvJ,OAAO,CAACvL,GAAG,CAAC,IAAI,CAAC,EAAE;QAChC,IAAI,CAACF,MAAM,CAACd,SAAS,CAACkW,oBAAoB,CAACrT,IAAI,CAACV,MAAM,EAAEnB,GAAG,CAAC,EAAE;QAC9DV,MAAM,CAACU,GAAG,CAAC,GAAGmB,MAAM,CAACnB,GAAG,CAAC;MAC3B;IACF;IACA,OAAOV,MAAM;EACf;EACA,SAAS6V,cAAcA,CAAChT,GAAG,EAAE3C,CAAC,EAAE;IAC9B,OAAO4V,eAAe,CAACjT,GAAG,CAAC,IAAIqN,qBAAqB,CAACrN,GAAG,EAAE3C,CAAC,CAAC,IAAI6V,2BAA2B,CAAClT,GAAG,EAAE3C,CAAC,CAAC,IAAI8V,gBAAgB,CAAC,CAAC;EAC3H;EACA,SAASC,kBAAkBA,CAACpT,GAAG,EAAE;IAC/B,OAAOqT,kBAAkB,CAACrT,GAAG,CAAC,IAAIsT,gBAAgB,CAACtT,GAAG,CAAC,IAAIkT,2BAA2B,CAAClT,GAAG,CAAC,IAAIuT,kBAAkB,CAAC,CAAC;EACrH;EACA,SAASF,kBAAkBA,CAACrT,GAAG,EAAE;IAC/B,IAAIH,KAAK,CAACS,OAAO,CAACN,GAAG,CAAC,EAAE,OAAOwT,iBAAiB,CAACxT,GAAG,CAAC;EACvD;EACA,SAASiT,eAAeA,CAACjT,GAAG,EAAE;IAC5B,IAAIH,KAAK,CAACS,OAAO,CAACN,GAAG,CAAC,EAAE,OAAOA,GAAG;EACpC;EACA,SAASsT,gBAAgBA,CAACG,IAAI,EAAE;IAC9B,IAAI,OAAO/W,MAAM,KAAK,WAAW,IAAI+W,IAAI,CAAC/W,MAAM,CAACC,QAAQ,CAAC,IAAI,IAAI,IAAI8W,IAAI,CAAC,YAAY,CAAC,IAAI,IAAI,EAAE,OAAO5T,KAAK,CAACC,IAAI,CAAC2T,IAAI,CAAC;EAC3H;EACA,SAASP,2BAA2BA,CAAC7T,CAAC,EAAEC,MAAM,EAAE;IAC9C,IAAI,CAACD,CAAC,EAAE;IACR,IAAI,OAAOA,CAAC,KAAK,QAAQ,EAAE,OAAOmU,iBAAiB,CAACnU,CAAC,EAAEC,MAAM,CAAC;IAC9D,IAAIE,CAAC,GAAG7B,MAAM,CAACd,SAAS,CAAC4C,QAAQ,CAACC,IAAI,CAACL,CAAC,CAAC,CAACM,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACtD,IAAIH,CAAC,KAAK,QAAQ,IAAIH,CAAC,CAACzC,WAAW,EAAE4C,CAAC,GAAGH,CAAC,CAACzC,WAAW,CAACgD,IAAI;IAC3D,IAAIJ,CAAC,KAAK,KAAK,IAAIA,CAAC,KAAK,KAAK,EAAE,OAAOK,KAAK,CAACC,IAAI,CAACT,CAAC,CAAC;IACpD,IAAIG,CAAC,KAAK,WAAW,IAAI,0CAA0C,CAACO,IAAI,CAACP,CAAC,CAAC,EAAE,OAAOgU,iBAAiB,CAACnU,CAAC,EAAEC,MAAM,CAAC;EAClH;EACA,SAASkU,iBAAiBA,CAACxT,GAAG,EAAEC,GAAG,EAAE;IACnC,IAAIA,GAAG,IAAI,IAAI,IAAIA,GAAG,GAAGD,GAAG,CAAC1C,MAAM,EAAE2C,GAAG,GAAGD,GAAG,CAAC1C,MAAM;IACrD,KAAK,IAAID,CAAC,GAAG,CAAC,EAAE6C,IAAI,GAAG,IAAIL,KAAK,CAACI,GAAG,CAAC,EAAE5C,CAAC,GAAG4C,GAAG,EAAE5C,CAAC,EAAE,EAAE6C,IAAI,CAAC7C,CAAC,CAAC,GAAG2C,GAAG,CAAC3C,CAAC,CAAC;IACrE,OAAO6C,IAAI;EACb;EACA,SAASqT,kBAAkBA,CAAA,EAAG;IAC5B,MAAM,IAAItW,SAAS,CAAC,sIAAsI,CAAC;EAC7J;EACA,SAASkW,gBAAgBA,CAAA,EAAG;IAC1B,MAAM,IAAIlW,SAAS,CAAC,2IAA2I,CAAC;EAClK;EAEA,SAASyW,oBAAoBA,CAACzH,EAAE,EAAE0H,OAAO,EAAE9X,MAAM,EAAE;IAClD,OAAOA,MAAM,GAAG;MACf2O,IAAI,EAAEmJ,OAAO;MACb/X,OAAO,EAAE,CAAC,CAAC;MACXE,OAAO,EAAE,SAAAA,CAAU0O,IAAI,EAAEoJ,IAAI,EAAE;QAC9B,OAAOC,eAAe,CAACrJ,IAAI,EAAGoJ,IAAI,KAAKpN,SAAS,IAAIoN,IAAI,KAAK,IAAI,GAAI/X,MAAM,CAAC2O,IAAI,GAAGoJ,IAAI,CAAC;MACzF;IACD,CAAC,EAAE3H,EAAE,CAACpQ,MAAM,EAAEA,MAAM,CAACD,OAAO,CAAC,EAAEC,MAAM,CAACD,OAAO;EAC9C;EAEA,SAASiY,eAAeA,CAAA,EAAI;IAC3B,MAAM,IAAIxH,KAAK,CAAC,yEAAyE,CAAC;EAC3F;;EAEA;AACF;AACA;AACA;AACA;AACA;;EAEE,IAAIyH,sBAAsB,GAAG,8CAA8C;EAC3E,IAAIC,sBAAsB,GAAGD,sBAAsB;EAEnD,IAAIE,oBAAoB,GAAGD,sBAAsB;EAEjD,SAASE,aAAaA,CAAA,EAAG,CAAC;EAC1B,SAASC,sBAAsBA,CAAA,EAAG,CAAC;EACnCA,sBAAsB,CAACC,iBAAiB,GAAGF,aAAa;EACxD,IAAIG,wBAAwB,GAAG,SAAAA,CAAA,EAAY;IACzC,SAASC,IAAIA,CAACjX,KAAK,EAAEkX,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAE;MAC5E,IAAIA,MAAM,KAAKV,oBAAoB,EAAE;QACnC;QACA;MACF;MACA,IAAInT,GAAG,GAAG,IAAIwL,KAAK,CAAC,sFAAsF,GAAG,+CAA+C,GAAG,gDAAgD,CAAC;MAChNxL,GAAG,CAACjB,IAAI,GAAG,qBAAqB;MAChC,MAAMiB,GAAG;IACX;IACAwT,IAAI,CAACM,UAAU,GAAGN,IAAI;IACtB,SAASO,OAAOA,CAAA,EAAG;MACjB,OAAOP,IAAI;IACb;IACA;IACA;IACA,IAAIQ,cAAc,GAAG;MACnBC,KAAK,EAAET,IAAI;MACXU,MAAM,EAAEV,IAAI;MACZW,IAAI,EAAEX,IAAI;MACVY,IAAI,EAAEZ,IAAI;MACVa,MAAM,EAAEb,IAAI;MACZjW,MAAM,EAAEiW,IAAI;MACZc,MAAM,EAAEd,IAAI;MACZe,MAAM,EAAEf,IAAI;MACZgB,GAAG,EAAEhB,IAAI;MACTiB,OAAO,EAAEV,OAAO;MAChBW,OAAO,EAAElB,IAAI;MACbmB,WAAW,EAAEnB,IAAI;MACjBoB,UAAU,EAAEb,OAAO;MACnBc,IAAI,EAAErB,IAAI;MACVsB,QAAQ,EAAEf,OAAO;MACjBgB,KAAK,EAAEhB,OAAO;MACdiB,SAAS,EAAEjB,OAAO;MAClBkB,KAAK,EAAElB,OAAO;MACdmB,KAAK,EAAEnB,OAAO;MACdoB,cAAc,EAAE9B,sBAAsB;MACtCC,iBAAiB,EAAEF;IACrB,CAAC;IACDY,cAAc,CAACoB,SAAS,GAAGpB,cAAc;IACzC,OAAOA,cAAc;EACvB,CAAC;EAED,IAAIqB,UAAU,GAAG9B,wBAAwB;EAEzC,IAAI+B,SAAS,GAAGzC,oBAAoB,CAAC,UAAU7X,MAAM,EAAE;IACvD;AACF;AACA;AACA;AACA;AACA;;IAEE;MACE;MACA;MACAA,MAAM,CAACD,OAAO,GAAGsa,UAAU,CAAC,CAAC;IAC/B;EACA,CAAC,CAAC;EAEF,IAAID,SAAS,GAAGE,SAAS;EAEzB,IAAIC,OAAO,GAAG;IACZlR,SAAS,EAAE+Q,SAAS,CAACd,MAAM;IAC3BrK,WAAW,EAAEmL,SAAS,CAACd,MAAM;IAC7BkB,aAAa,EAAEJ,SAAS,CAAChB;EAC3B,CAAC;EACD,IAAIqB,cAAc,GAAG3I,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyI,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;IACnElN,sBAAsB,EAAE+M,SAAS,CAACL,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC;EAC3D,CAAC,CAAC;EACF,IAAIW,uBAAuB,GAAG5I,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE2I,cAAc,CAAC,EAAE,CAAC,CAAC,EAAE;IACnFE,QAAQ,EAAEP,SAAS,CAACR,UAAU,CAAC9I,QAAQ;EACzC,CAAC,CAAC;;EAEF;EACA,IAAI8J,eAAe,GAAG,aAAara,KAAK,CAACsa,aAAa,CAAC,CAAC,CAAC,CAAC;;EAE1D;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAIC,SAAS,GAAG,SAASA,SAASA,CAACvZ,KAAK,EAAE;IACxC,IAAIwZ,qBAAqB,GAAG,SAASA,qBAAqBA,CAAC3J,aAAa,EAAE;MACxE,IAAIlJ,MAAM,GAAG,CAAC,CAAC;MACf,IAAI8S,QAAQ,GAAGlZ,MAAM,CAACW,IAAI,CAACiY,uBAAuB,CAAC;MACnD,KAAK,IAAIlZ,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwZ,QAAQ,CAACvZ,MAAM,EAAED,CAAC,EAAE,EAAE;QACxC,IAAIQ,GAAG,GAAGgZ,QAAQ,CAACxZ,CAAC,CAAC;QACrB,IAAIa,KAAK,GAAG+O,aAAa,CAACpP,GAAG,CAAC;QAC9B,IAAIK,KAAK,EAAE;UACT6F,MAAM,CAAClG,GAAG,CAAC,GAAGK,KAAK;QACrB;MACF;MACA,OAAO6F,MAAM;IACf,CAAC;IACD,IAAIkJ,aAAa,GAAGU,cAAc,CAAC,CAAC,CAAC,EAAEvQ,KAAK,CAAC;IAC7C,IAAI0Z,uBAAuB,GAAGF,qBAAqB,CAAC3J,aAAa,CAAC;IAClE,IAAI6J,uBAAuB,CAAChM,WAAW,IAAIgM,uBAAuB,CAAChM,WAAW,CAAC7H,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;MAC5F6T,uBAAuB,CAACN,QAAQ,GAAG,IAAI7J,QAAQ,CAAC;QAC9C7B,WAAW,EAAEgM,uBAAuB,CAAChM,WAAW;QAChD;QACA8B,UAAU,EAAE;MACd,CAAC,CAAC;IACJ;IACA,OAAO,aAAarQ,cAAc,CAAC,SAAS,CAAC,CAACwa,aAAa,CAACN,eAAe,CAACO,QAAQ,EAAE;MACpF9Y,KAAK,EAAE4Y;IACT,CAAC,EAAE1Z,KAAK,CAAC6Z,QAAQ,CAAC;EACpB,CAAC;EAED,IAAIC,OAAO,GAAG;IACZC,OAAO,EAAElB,SAAS,CAACL,KAAK,CAAC,CAAC,MAAM,CAAC,CAAC;IAClCwB,IAAI,EAAEnB,SAAS,CAACH,KAAK,CAAC;MACpBuB,MAAM,EAAEpB,SAAS,CAACjB,IAAI;MACtBnO,OAAO,EAAEoP,SAAS,CAACf,MAAM;MACzBoC,SAAS,EAAErB,SAAS,CAACf,MAAM;MAC3BzN,IAAI,EAAEwO,SAAS,CAACf,MAAM;MACtB1M,GAAG,EAAEyN,SAAS,CAACd;IACjB,CAAC,CAAC;IACF3K,IAAI,EAAEyL,SAAS,CAACd,MAAM;IACtB1K,GAAG,EAAEwL,SAAS,CAACd,MAAM;IACrBjK,eAAe,EAAE+K,SAAS,CAACN,QAAQ,CAACM,SAAS,CAACJ,SAAS,CAAC,CAACI,SAAS,CAACd,MAAM,EAAEc,SAAS,CAACf,MAAM,CAAC,CAAC,CAACP,UAAU,CAAC;IACzGrP,cAAc,EAAE2Q,SAAS,CAACX,OAAO,CAACW,SAAS,CAAC7X,MAAM,CAACuW,UAAU,CAAC;IAC9DzL,sBAAsB,EAAE+M,SAAS,CAACL,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC;EAC3D,CAAC;EAED,IAAI2B,2BAA2B,GAAG5J,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyI,OAAO,CAAC,EAAEc,OAAO,CAAC;EAEtF,IAAIM,wBAAwB,GAAG,SAASA,wBAAwBA,CAAA,EAAG;IACjE,IAAI;MACF,OAAOC,SAAS,CAACC,UAAU,CAACC,aAAa;IAC3C,CAAC,CAAC,OAAO3T,EAAE,EAAE;MACX,OAAO,IAAI;IACb;EACF,CAAC;EACD,IAAI4T,MAAM,GAAG,SAASA,MAAMA,CAAC1K,IAAI,EAAEsJ,QAAQ,EAAEqB,cAAc,EAAE;IAC3D,IAAI/M,WAAW,GAAGoC,IAAI,CAACpC,WAAW;MAChCsM,IAAI,GAAGlK,IAAI,CAACkK,IAAI;MAChB3M,GAAG,GAAGyC,IAAI,CAACzC,GAAG;MACdD,IAAI,GAAG0C,IAAI,CAAC1C,IAAI;MAChBlF,cAAc,GAAG4H,IAAI,CAAC5H,cAAc;MACpC4D,sBAAsB,GAAGgE,IAAI,CAAChE,sBAAsB;MACpDgC,eAAe,GAAGgC,IAAI,CAAChC,eAAe;IACxC,IAAInG,OAAO;IACX,IAAI0F,GAAG,EAAE;MACP1F,OAAO,GAAG;QACR+F,WAAW,EAAEA,WAAW,IAAI+M,cAAc,CAAC/M,WAAW;QACtDL,GAAG,EAAEA,GAAG;QACRnF,cAAc,EAAEA,cAAc,IAAIkB,SAAS;QAC3C0C,sBAAsB,EAAEA,sBAAsB,IAAI2O,cAAc,CAAC3O,sBAAsB,IAAI1C,SAAS;QACpG0E,eAAe,EAAEA,eAAe,IAAI,CAAC;MACvC,CAAC;IACH,CAAC,MAAM,IAAIV,IAAI,EAAE;MACfzF,OAAO,GAAG;QACR+F,WAAW,EAAEA,WAAW,IAAI+M,cAAc,CAAC/M,WAAW;QACtDN,IAAI,EAAEA,IAAI;QACVlF,cAAc,EAAEA,cAAc,IAAIkB,SAAS;QAC3C0C,sBAAsB,EAAEA,sBAAsB,IAAI2O,cAAc,CAAC3O,sBAAsB,IAAI1C,SAAS;QACpG0E,eAAe,EAAEA,eAAe,IAAI,CAAC;MACvC,CAAC;IACH,CAAC,MAAM,OAAO;MACZ4M,WAAW,EAAE;IACf,CAAC;IACD,IAAI/T,MAAM,GAAG;MACX+T,WAAW,EAAEtB,QAAQ,CAAC7K,GAAG,CAAC5G,OAAO;IACnC,CAAC;IACD,IAAIqS,IAAI,IAAIA,IAAI,CAACC,MAAM,EAAE;MACvB,IAAIxQ,OAAO,GAAGkR,IAAI,CAACC,KAAK,CAACZ,IAAI,CAACvQ,OAAO,IAAIuQ,IAAI,CAACE,SAAS,IAAI,EAAE,CAAC;MAC9D,IAAI7P,IAAI,GAAGsQ,IAAI,CAACC,KAAK,CAACZ,IAAI,CAAC3P,IAAI,IAAI,CAAC,CAAC;MACrC,IAAIwQ,iBAAiB,GAAGlT,OAAO,CAACO,cAAc,GAAG8N,kBAAkB,CAACrO,OAAO,CAACO,cAAc,CAAC,GAAG,EAAE;MAChG,IAAI8R,IAAI,CAAC5O,GAAG,IAAI,OAAO4O,IAAI,CAAC5O,GAAG,KAAK,QAAQ,IAAI4O,IAAI,CAAC5O,GAAG,CAACvF,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACtEgV,iBAAiB,CAACrZ,IAAI,CAAC;UACrB4J,GAAG,EAAE4O,IAAI,CAAC5O,GAAG,CAACvF,IAAI,CAAC;QACrB,CAAC,CAAC;MACJ,CAAC,MAAM;QACLgV,iBAAiB,CAACrZ,IAAI,CAAC;UACrBiI,OAAO,EAAEb,MAAM,CAACa,OAAO,CAAC;UACxBY,IAAI,EAAEzB,MAAM,CAACyB,IAAI;QACnB,CAAC,CAAC;MACJ;MACA1D,MAAM,CAACmU,OAAO,GAAG1B,QAAQ,CAAC7K,GAAG,CAACgC,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAE5I,OAAO,CAAC,EAAE,CAAC,CAAC,EAAE;QAC5EO,cAAc,EAAE2S;MAClB,CAAC,CAAC,CAAC;IACL;IACA,OAAOlU,MAAM;EACf,CAAC;EACD,IAAIoU,gBAAgB,GAAG,SAASA,gBAAgBA,CAACC,KAAK,EAAEC,KAAK,EAAE;IAC7D,IAAIC,UAAU,GAAGF,KAAK,CAAChB,IAAI;MACzBA,IAAI,GAAGkB,UAAU,KAAK,KAAK,CAAC,GAAG,IAAI,GAAGA,UAAU;MAChDnB,OAAO,GAAGiB,KAAK,CAACjB,OAAO;IACzB,IAAIoB,WAAW,GAAGF,KAAK,CAACE,WAAW;MACjCC,iBAAiB,GAAGH,KAAK,CAACG,iBAAiB;MAC3CV,WAAW,GAAGO,KAAK,CAACP,WAAW;MAC/BI,OAAO,GAAGG,KAAK,CAACH,OAAO;IACzB;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACI,IAAIO,YAAY,GAAG,SAASA,YAAYA,CAACrB,IAAI,EAAE;MAC7C,OAAOA,IAAI,IAAIA,IAAI,CAACC,MAAM;IAC5B,CAAC;IACD,IAAIF,OAAO,KAAK,MAAM,IAAI,CAACsB,YAAY,CAACrB,IAAI,CAAC,EAAE;MAC7C,OAAOU,WAAW;IACpB,CAAC,MAAM,IAAIX,OAAO,KAAK,MAAM,IAAIsB,YAAY,CAACrB,IAAI,CAAC,EAAE;MACnD,IAAIoB,iBAAiB,EAAE;QACrB,OAAOV,WAAW;MACpB,CAAC,MAAM;QACL,OAAOI,OAAO;MAChB;IACF,CAAC,MAAM,IAAIf,OAAO,KAAK,MAAM,IAAI,CAACsB,YAAY,CAACrB,IAAI,CAAC,EAAE;MACpD,IAAImB,WAAW,EAAE;QACf,OAAOT,WAAW;MACpB,CAAC,MAAM;QACL,OAAO,EAAE;MACX;IACF,CAAC,MAAM;MACL;MACA,IAAIS,WAAW,IAAIC,iBAAiB,EAAE;QACpC,OAAOV,WAAW;MACpB,CAAC,MAAM;QACL,OAAOI,OAAO;MAChB;IACF;EACF,CAAC;EAED,IAAIQ,oBAAoB,GAAG,SAASA,oBAAoBA,CAACtb,KAAK,EAAE;IAC9D,IAAIya,cAAc,GAAGzb,KAAK,CAACuc,UAAU,CAAClC,eAAe,CAAC;IACtD,IAAImC,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;MACvC,IAAIf,cAAc,IAAIA,cAAc,CAACrB,QAAQ,EAAE;QAC7C,OAAOqB,cAAc,CAACrB,QAAQ;MAChC;MACA,IAAI1L,WAAW,GAAG1N,KAAK,CAAC0N,WAAW;MACnCA,WAAW,GAAGA,WAAW,IAAI+M,cAAc,IAAIA,cAAc,CAAC/M,WAAW;MACzE,IAAI,CAACA,WAAW,IAAIA,WAAW,CAAC7H,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC7C,MAAM,IAAIoJ,KAAK,CAAC,2CAA2C,CAAC;MAC9D;MACA,IAAImK,QAAQ,GAAG,IAAI7J,QAAQ,CAAC;QAC1B7B,WAAW,EAAEA,WAAW;QACxB;QACA8B,UAAU,EAAE;MACd,CAAC,CAAC;MACF,OAAO4J,QAAQ;IACjB,CAAC;IACD,OAAO;MACLoC,WAAW,EAAEA;IACf,CAAC;EACH,CAAC;EAED,IAAIC,WAAW,GAAG,CAAC,aAAa,EAAE,eAAe,EAAE,WAAW,EAAE,SAAS,EAAE,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,wBAAwB,EAAE,iBAAiB,CAAC;EAChK,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAAC1b,KAAK,EAAE;IACpC,IAAI2b,QAAQ,GAAG3c,KAAK,CAAC4c,MAAM,CAAC,IAAI,CAAC;IACjC,IAAIC,qBAAqB,GAAGP,oBAAoB,CAAC/K,cAAc,CAAC,CAAC,CAAC,EAAEvQ,KAAK,CAAC,CAAC;MACzEwb,WAAW,GAAGK,qBAAqB,CAACL,WAAW;IACjD,IAAIf,cAAc,GAAGzb,KAAK,CAACuc,UAAU,CAAClC,eAAe,CAAC;IACtD,IAAIyC,SAAS,GAAG9c,KAAK,CAAC+c,QAAQ,CAAC3S,SAAS,CAAC;MACvC4S,UAAU,GAAGpG,cAAc,CAACkG,SAAS,EAAE,CAAC,CAAC;MACzCG,UAAU,GAAGD,UAAU,CAAC,CAAC,CAAC;MAC1BE,aAAa,GAAGF,UAAU,CAAC,CAAC,CAAC;IAC/B,IAAIG,UAAU,GAAGnd,KAAK,CAAC+c,QAAQ,CAAC,EAAE,CAAC;MACjCK,UAAU,GAAGxG,cAAc,CAACuG,UAAU,EAAE,CAAC,CAAC;MAC1CzB,WAAW,GAAG0B,UAAU,CAAC,CAAC,CAAC;MAC3BC,cAAc,GAAGD,UAAU,CAAC,CAAC,CAAC;IAChC,IAAIE,UAAU,GAAGtd,KAAK,CAAC+c,QAAQ,CAAC,EAAE,CAAC;MACjCQ,UAAU,GAAG3G,cAAc,CAAC0G,UAAU,EAAE,CAAC,CAAC;MAC1CxB,OAAO,GAAGyB,UAAU,CAAC,CAAC,CAAC;MACvBC,UAAU,GAAGD,UAAU,CAAC,CAAC,CAAC;IAC5B,IAAIE,UAAU,GAAGzd,KAAK,CAAC+c,QAAQ,CAAC,KAAK,CAAC;MACpCW,UAAU,GAAG9G,cAAc,CAAC6G,UAAU,EAAE,CAAC,CAAC;MAC1CrB,iBAAiB,GAAGsB,UAAU,CAAC,CAAC,CAAC;MACjCC,oBAAoB,GAAGD,UAAU,CAAC,CAAC,CAAC;IACtC,IAAIE,UAAU,GAAG5d,KAAK,CAAC+c,QAAQ,CAAC3S,SAAS,CAAC;MACxCyT,WAAW,GAAGjH,cAAc,CAACgH,UAAU,EAAE,CAAC,CAAC;MAC3CE,OAAO,GAAGD,WAAW,CAAC,CAAC,CAAC;MACxBE,UAAU,GAAGF,WAAW,CAAC,CAAC,CAAC;IAC7B,IAAIG,WAAW,GAAGhe,KAAK,CAAC+c,QAAQ,CAAC,KAAK,CAAC;MACrCkB,WAAW,GAAGrH,cAAc,CAACoH,WAAW,EAAE,CAAC,CAAC;MAC5CE,WAAW,GAAGD,WAAW,CAAC,CAAC,CAAC;MAC5BE,cAAc,GAAGF,WAAW,CAAC,CAAC,CAAC;IACjC,IAAIG,WAAW,GAAGpe,KAAK,CAAC+c,QAAQ,CAAC,KAAK,CAAC;MACrCsB,WAAW,GAAGzH,cAAc,CAACwH,WAAW,EAAE,CAAC,CAAC;MAC5CjC,WAAW,GAAGkC,WAAW,CAAC,CAAC,CAAC;MAC5BC,cAAc,GAAGD,WAAW,CAAC,CAAC,CAAC;IACjCre,KAAK,CAACue,SAAS,CAAC,YAAY;MAC1B,IAAIC,OAAO,GAAGhD,MAAM,CAACxa,KAAK,EAAEwb,WAAW,CAAC,CAAC,EAAEf,cAAc,CAAC;QACxDgD,cAAc,GAAGD,OAAO,CAAC9C,WAAW;QACpCgD,UAAU,GAAGF,OAAO,CAAC1C,OAAO;MAC9BuB,cAAc,CAACoB,cAAc,CAAC;MAC9BjB,UAAU,CAACkB,UAAU,GAAGA,UAAU,GAAG,EAAE,CAAC;MACxCP,cAAc,CAAC,IAAI,CAAC;IACtB,CAAC,EAAE,CAAC1C,cAAc,EAAEza,KAAK,CAAC,CAAC;IAC3B,IAAI2d,cAAc,GAAG,aAAa,YAAY;MAC5C,IAAI7N,IAAI,GAAGoF,iBAAiB,EAAE,aAAazE,mBAAmB,CAAC,CAAC,CAACgD,IAAI,CAAC,SAASmK,OAAOA,CAAA,EAAG;QACvF,IAAIrP,GAAG;QACP,OAAOkC,mBAAmB,CAAC,CAAC,CAACK,IAAI,CAAC,SAAS+M,QAAQA,CAACC,QAAQ,EAAE;UAC5D,OAAO,CAAC,EAAE,QAAQA,QAAQ,CAAC/J,IAAI,GAAG+J,QAAQ,CAACna,IAAI;YAC7C,KAAK,CAAC;cACJma,QAAQ,CAACna,IAAI,GAAG,CAAC;cACjB,OAAOoX,gBAAgB,CAAC/a,KAAK,EAAE;gBAC7B0a,WAAW,EAAEA,WAAW;gBACxBI,OAAO,EAAEA,OAAO;gBAChBK,WAAW,EAAEA,WAAW;gBACxBV,cAAc,EAAEA,cAAc;gBAC9BsD,cAAc,EAAEb,WAAW;gBAC3B9B,iBAAiB,EAAEA,iBAAiB;gBACpC0B,OAAO,EAAEA;cACX,CAAC,CAAC;YACJ,KAAK,CAAC;cACJvO,GAAG,GAAGuP,QAAQ,CAACvL,IAAI;cACnB;cACA,IAAIhE,GAAG,EAAE;gBACP2N,aAAa,CAAC3N,GAAG,CAAC;cACpB;YACF,KAAK,CAAC;YACN,KAAK,KAAK;cACR,OAAOuP,QAAQ,CAAC7J,IAAI,CAAC,CAAC;UAC1B;QACF,CAAC,EAAE2J,OAAO,CAAC;MACb,CAAC,CAAC,CAAC;MACH,OAAO,SAASD,cAAcA,CAAA,EAAG;QAC/B,OAAO7N,IAAI,CAACrO,KAAK,CAAC,IAAI,EAAEE,SAAS,CAAC;MACpC,CAAC;IACH,CAAC,CAAC,CAAC;IACH,IAAIqc,wBAAwB,GAAG,SAASA,wBAAwBA,CAAA,EAAG;MACjE,IAAIC,GAAG,GAAG,IAAIC,KAAK,CAAC,CAAC;MACrBD,GAAG,CAAC/W,MAAM,GAAG,YAAY;QACvByV,oBAAoB,CAAC,IAAI,CAAC;MAC5B,CAAC;MACDsB,GAAG,CAAC5Q,GAAG,GAAGqN,WAAW;IACvB,CAAC;IACD1b,KAAK,CAACue,SAAS,CAAC,YAAY;MAC1B,IAAInC,iBAAiB,EAAEuC,cAAc,CAAC,CAAC;IACzC,CAAC,EAAE,CAACvC,iBAAiB,CAAC,CAAC;IACvBpc,KAAK,CAACue,SAAS,CAAC,YAAY;MAC1B,IAAIY,KAAK,GAAGxC,QAAQ,CAACyC,OAAO;MAC5B,IAAIrE,OAAO,GAAG/Z,KAAK,CAAC+Z,OAAO;MAC3B,IAAImD,WAAW,EAAE;QACf,IAAImB,MAAM,IAAI,sBAAsB,IAAIA,MAAM,IAAItE,OAAO,KAAK,MAAM,EAAE;UACpE,IAAIuE,cAAc,GAAGlE,wBAAwB,CAAC,CAAC;UAC/C,IAAImE,UAAU,GAAG,QAAQ;UACzB,IAAID,cAAc,KAAK,IAAI,EAAEC,UAAU,GAAG,QAAQ;UAClD,IAAIC,aAAa,GAAG,IAAIC,oBAAoB,CAAC,UAAUC,OAAO,EAAE;YAC9D,IAAIC,EAAE,GAAGD,OAAO,CAAC,CAAC,CAAC;YACnB,IAAIC,EAAE,IAAIA,EAAE,CAACC,cAAc,IAAI,CAACzD,WAAW,EAAE;cAC3CmC,cAAc,CAAC,IAAI,CAAC;cACpBP,UAAU,CAAC,UAAU8B,YAAY,EAAE;gBACjC,IAAIA,YAAY,EAAE;kBAChBA,YAAY,CAACC,UAAU,CAAC,CAAC;gBAC3B;gBACA,OAAO1V,SAAS;cAClB,CAAC,CAAC;cACF4U,wBAAwB,CAAC,CAAC;cAC1BL,cAAc,CAAC,CAAC;YAClB;UACF,CAAC,EAAE;YACDY,UAAU,EAAE,EAAE,CAACrP,MAAM,CAACqP,UAAU,EAAE,OAAO,CAAC,CAACrP,MAAM,CAACqP,UAAU,EAAE,MAAM;UACtE,CAAC,CAAC;UACF,IAAIJ,KAAK,EAAE;YACTK,aAAa,CAAC1B,OAAO,CAACqB,KAAK,CAAC;YAC5BpB,UAAU,CAACyB,aAAa,CAAC;UAC3B;QACF,CAAC,MAAM;UACLlB,cAAc,CAAC,IAAI,CAAC;UACpBU,wBAAwB,CAAC,CAAC;UAC1BL,cAAc,CAAC,CAAC;QAClB;MACF;MACA,OAAO,YAAY;QACjB,IAAIb,OAAO,EAAE;UACXA,OAAO,CAACgC,UAAU,CAAC,CAAC;QACtB;MACF,CAAC;IACH,CAAC,EAAE,CAAC9e,KAAK,EAAE0a,WAAW,EAAEI,OAAO,CAAC,CAAC;IACjC9a,KAAK,CAAC0N,WAAW;IACf1N,KAAK,CAACiZ,aAAa;IACnBjZ,KAAK,CAAC8H,SAAS;IACf9H,KAAK,CAAC+Z,OAAO;IACb/Z,KAAK,CAACga,IAAI;IACVha,KAAK,CAACoN,IAAI;IACVpN,KAAK,CAACqN,GAAG;IACTrN,KAAK,CAACkI,cAAc;IACpBlI,KAAK,CAAC8L,sBAAsB;IAC5B9L,KAAK,CAAC8N,eAAe;IACrB,IAAIiR,SAAS,GAAGtJ,wBAAwB,CAACzV,KAAK,EAAEyb,WAAW,CAAC;IAC9D,OAAO,aAAatc,cAAc,CAAC,SAAS,CAAC,CAACwa,aAAa,CAAC,KAAK,EAAExE,QAAQ,CAAC;MAC1E6J,GAAG,EAAEhf,KAAK,CAACgf,GAAG,IAAI,EAAE;MACpB3R,GAAG,EAAE4O,UAAU,GAAGA,UAAU,GAAG7S,SAAS;MACxC6V,GAAG,EAAEtD;IACP,CAAC,EAAEoD,SAAS,CAAC,CAAC;EAChB,CAAC;EACDrD,OAAO,CAAC3C,SAAS,GAAGoB,2BAA2B;EAE/C,IAAI+E,KAAK,GAAG;IACV9R,IAAI,EAAEyL,SAAS,CAACd,MAAM;IACtB1K,GAAG,EAAEwL,SAAS,CAACd,MAAM;IACrBjK,eAAe,EAAE+K,SAAS,CAACN,QAAQ,CAACM,SAAS,CAACJ,SAAS,CAAC,CAACI,SAAS,CAACd,MAAM,EAAEc,SAAS,CAACf,MAAM,CAAC,CAAC,CAACP,UAAU,CAAC;IACzGrP,cAAc,EAAE2Q,SAAS,CAACX,OAAO,CAACW,SAAS,CAAC7X,MAAM,CAACuW,UAAU,CAAC;IAC9DzL,sBAAsB,EAAE+M,SAAS,CAACL,KAAK,CAAC,CAAC,MAAM,EAAE,OAAO,CAAC;EAC3D,CAAC;EAED,IAAI2G,yBAAyB,GAAG5O,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEyI,OAAO,CAAC,EAAEkG,KAAK,CAAC;EAElF,IAAIE,WAAW,GAAG,CAAC,aAAa,EAAE,WAAW,EAAE,eAAe,EAAE,MAAM,EAAE,KAAK,EAAE,gBAAgB,EAAE,wBAAwB,EAAE,iBAAiB,CAAC;EAC7I,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACrf,KAAK,EAAE;IACpC,IAAIsf,QAAQ,GAAGtgB,KAAK,CAAC4c,MAAM,CAAC,IAAI,CAAC;IACjC,IAAIE,SAAS,GAAG9c,KAAK,CAAC+c,QAAQ,CAAC;QAC3BE,UAAU,EAAE7S,SAAS;QACrBqR,cAAc,EAAE,CAAC;MACnB,CAAC,CAAC;MACFuB,UAAU,GAAGpG,cAAc,CAACkG,SAAS,EAAE,CAAC,CAAC;MACzCyD,KAAK,GAAGvD,UAAU,CAAC,CAAC,CAAC;MACrBwD,QAAQ,GAAGxD,UAAU,CAAC,CAAC,CAAC;IAC1B,IAAIH,qBAAqB,GAAGP,oBAAoB,CAAC/K,cAAc,CAAC,CAAC,CAAC,EAAEvQ,KAAK,CAAC,CAAC;MACzEwb,WAAW,GAAGK,qBAAqB,CAACL,WAAW;IACjD,IAAIiE,YAAY,GAAGzgB,KAAK,CAACuc,UAAU,CAAClC,eAAe,CAAC;IACpDra,KAAK,CAACue,SAAS,CAAC,YAAY;MAC1B,IAAIC,OAAO,GAAGhD,MAAM,CAACxa,KAAK,EAAEwb,WAAW,CAAC,CAAC,EAAEiE,YAAY,CAAC;QACtD/E,WAAW,GAAG8C,OAAO,CAAC9C,WAAW;MACnC8E,QAAQ,CAAC,UAAUE,SAAS,EAAE;QAC5B,OAAOnP,cAAc,CAACA,cAAc,CAAC,CAAC,CAAC,EAAEmP,SAAS,CAAC,EAAE,CAAC,CAAC,EAAE;UACvDzD,UAAU,EAAEvB,WAAW;UACvBD,cAAc,EAAEgF;QAClB,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ,CAAC,EAAE,CAACA,YAAY,EAAEzf,KAAK,CAAC,CAAC;IACzB,IAAIic,UAAU,GAAGsD,KAAK,CAACtD,UAAU;IACjCjc,KAAK,CAAC0N,WAAW;IACf1N,KAAK,CAAC8H,SAAS;IACf9H,KAAK,CAACiZ,aAAa;IACnBjZ,KAAK,CAACoN,IAAI;IACVpN,KAAK,CAACqN,GAAG;IACTrN,KAAK,CAACkI,cAAc;IACpBlI,KAAK,CAAC8L,sBAAsB;IAC5B9L,KAAK,CAAC8N,eAAe;IACrB,IAAIiR,SAAS,GAAGtJ,wBAAwB,CAACzV,KAAK,EAAEof,WAAW,CAAC;IAC9D,OAAO,aAAajgB,cAAc,CAAC,SAAS,CAAC,CAACwa,aAAa,CAAC,OAAO,EAAExE,QAAQ,CAAC,CAAC,CAAC,EAAE4J,SAAS,EAAE;MAC3FE,GAAG,EAAEK,QAAQ;MACb7e,GAAG,EAAEwb;IACP,CAAC,CAAC,EAAE,aAAa9c,cAAc,CAAC,SAAS,CAAC,CAACwa,aAAa,CAAC,QAAQ,EAAE;MACjEtM,GAAG,EAAE4O,UAAU;MACfzT,IAAI,EAAE;IACR,CAAC,CAAC,CAAC;EACL,CAAC;EACD6W,OAAO,CAACtG,SAAS,GAAGoG,yBAAyB;EAE7C,IAAIQ,SAAS,GAAG,CAAC,WAAW,EAAE,aAAa,EAAE,eAAe,EAAE,UAAU,EAAE,mBAAmB,EAAE,MAAM,EAAE,QAAQ,EAAE,eAAe,EAAE,mBAAmB,EAAE,gBAAgB,EAAE,SAAS,EAAE,WAAW,EAAE,eAAe,EAAE,kBAAkB,EAAE,cAAc,EAAE,YAAY,EAAE,eAAe,EAAE,iBAAiB,EAAE,eAAe,EAAE,yBAAyB,EAAE,YAAY,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,QAAQ,EAAE,oBAAoB,CAAC;EACra,IAAIC,QAAQ,GAAG,aAAa5gB,KAAK,CAAC6gB,UAAU,CAAC,UAAU7f,KAAK,EAAEif,GAAG,EAAE;IACjE,IAAInD,SAAS,GAAG9c,KAAK,CAAC+c,QAAQ,CAAC,CAAC,CAAC,CAAC;MAChCC,UAAU,GAAGpG,cAAc,CAACkG,SAAS,EAAE,CAAC,CAAC;MACzCyD,KAAK,GAAGvD,UAAU,CAAC,CAAC,CAAC;MACrBwD,QAAQ,GAAGxD,UAAU,CAAC,CAAC,CAAC;IAC1B,IAAIvB,cAAc,GAAGzb,KAAK,CAACuc,UAAU,CAAClC,eAAe,CAAC;IACtD,IAAIwC,qBAAqB,GAAGP,oBAAoB,CAAC/K,cAAc,CAAC,CAAC,CAAC,EAAEvQ,KAAK,CAAC,CAAC;MACzEwb,WAAW,GAAGK,qBAAqB,CAACL,WAAW;IACjDxc,KAAK,CAACue,SAAS,CAAC,YAAY;MAC1B,IAAIuC,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;QAC3B,IAAIP,KAAK,CAAC9Z,GAAG,EAAE;UACb8Z,KAAK,CAAC9Z,GAAG,CAACqa,KAAK,CAAC,CAAC;QACnB;MACF,CAAC;MACD,IAAIb,GAAG,IAAIrK,OAAO,CAACqK,GAAG,CAAC,KAAK,QAAQ,IAAIA,GAAG,CAACvO,cAAc,CAAC,SAAS,CAAC,EAAE;QACrE,IAAIqP,SAAS,GAAGd,GAAG;QACnBc,SAAS,CAAC3B,OAAO,CAAC0B,KAAK,GAAGA,KAAK;MACjC;IACF,CAAC,EAAE,CAACP,KAAK,CAAC9Z,GAAG,EAAEwZ,GAAG,CAAC,CAAC;IACpBjf,KAAK,CAAC8H,SAAS;IACb9H,KAAK,CAAC0N,WAAW;IACjB1N,KAAK,CAACiZ,aAAa;IACnB,IAAIpR,QAAQ,GAAG7H,KAAK,CAAC6H,QAAQ;MAC7BmY,iBAAiB,GAAGhgB,KAAK,CAACggB,iBAAiB;MAC3CnX,IAAI,GAAG7I,KAAK,CAAC6I,IAAI;MACjBoX,MAAM,GAAGjgB,KAAK,CAACigB,MAAM;MACrBC,aAAa,GAAGlgB,KAAK,CAACkgB,aAAa;MACnCC,iBAAiB,GAAGngB,KAAK,CAACmgB,iBAAiB;MAC3CpX,cAAc,GAAG/I,KAAK,CAAC+I,cAAc;MACrCqX,OAAO,GAAGpgB,KAAK,CAACogB,OAAO;MACvBC,SAAS,GAAGrgB,KAAK,CAACqgB,SAAS;IAC3BrgB,KAAK,CAACsgB,aAAa;IACnBtgB,KAAK,CAACugB,gBAAgB;IACtBvgB,KAAK,CAACwgB,YAAY;IAClB,IAAIC,UAAU,GAAGzgB,KAAK,CAACygB,UAAU;MACjCC,aAAa,GAAG1gB,KAAK,CAAC0gB,aAAa;MACnCC,eAAe,GAAG3gB,KAAK,CAAC2gB,eAAe;MACvCC,aAAa,GAAG5gB,KAAK,CAAC4gB,aAAa;MACnCC,uBAAuB,GAAG7gB,KAAK,CAAC6gB,uBAAuB;MACvD7X,UAAU,GAAGhJ,KAAK,CAACgJ,UAAU;MAC7BE,cAAc,GAAGlJ,KAAK,CAACkJ,cAAc;MACrChB,cAAc,GAAGlI,KAAK,CAACkI,cAAc;MACrCiB,MAAM,GAAGnJ,KAAK,CAACmJ,MAAM;IACrBnJ,KAAK,CAAC8gB,kBAAkB;IACxB,IAAI/B,SAAS,GAAGtJ,wBAAwB,CAACzV,KAAK,EAAE2f,SAAS,CAAC;IAC5D,IAAIlZ,UAAU,GAAG,SAASA,UAAUA,CAACvH,CAAC,EAAE;MACtC,IAAI6hB,eAAe;MACnB,IAAIjZ,SAAS,GAAG9H,KAAK,CAAC8H,SAAS,IAAI2S,cAAc,CAAC3S,SAAS;MAC3D,IAAImR,aAAa,GAAGjZ,KAAK,CAACiZ,aAAa,IAAIwB,cAAc,CAACxB,aAAa;MACvE,IAAIvL,WAAW,GAAG1N,KAAK,CAAC0N,WAAW,IAAI+M,cAAc,CAAC/M,WAAW;MACjE,IAAI,CAAC5F,SAAS,IAAIA,SAAS,CAACjC,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QACzC+H,OAAO,CAACC,KAAK,CAAC,mBAAmB,CAAC;QAClC,IAAIuS,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;UAC5CA,OAAO,CAAC;YACNpc,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;QACA;MACF;MACA,IAAI,CAACiV,aAAa,EAAE;QAClBrL,OAAO,CAACC,KAAK,CAAC,6CAA6C,CAAC;QAC5D,IAAIuS,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;UAC5CA,OAAO,CAAC;YACNpc,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;QACA;MACF;MACA,IAAI,OAAOiV,aAAa,KAAK,UAAU,EAAE;QACvCrL,OAAO,CAACC,KAAK,CAAC,+CAA+C,CAAC;QAC9D,IAAIuS,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;UAC5CA,OAAO,CAAC;YACNpc,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;QACA;MACF;MACA,IAAI,CAAC0J,WAAW,IAAIA,WAAW,CAAC7H,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;QAC7C+H,OAAO,CAACC,KAAK,CAAC,qBAAqB,CAAC;QACpC,IAAIuS,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;UAC5CA,OAAO,CAAC;YACNpc,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;QACA;MACF;MACA,IAAIoV,QAAQ,GAAGoC,WAAW,CAAC,CAAC;MAC5B,IAAI5T,IAAI,GAAG,CAACmZ,eAAe,GAAG7hB,CAAC,CAACa,MAAM,CAACihB,KAAK,MAAM,IAAI,IAAID,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,eAAe,CAAC,CAAC,CAAC;MAClH,IAAI,CAACnZ,IAAI,EAAE;QACT;MACF;MACA,IAAI5H,KAAK,CAACwgB,YAAY,IAAI,CAACxgB,KAAK,CAACwgB,YAAY,CAAC5Y,IAAI,CAAC,EAAE;QACnD;MACF;MACA,IAAI5H,KAAK,CAACsgB,aAAa,IAAI,OAAOtgB,KAAK,CAACsgB,aAAa,KAAK,UAAU,EAAE;QACpEtgB,KAAK,CAACsgB,aAAa,CAACphB,CAAC,CAAC;MACxB;MACA,IAAI+hB,cAAc,GAAG,CAAC,CAAC;MACvB,IAAIjhB,KAAK,CAAC8gB,kBAAkB,IAAI,OAAO9gB,KAAK,CAAC8gB,kBAAkB,KAAK,UAAU,EAAE;QAC9EG,cAAc,GAAGjhB,KAAK,CAAC8gB,kBAAkB,CAAClZ,IAAI,CAAC,IAAI,CAAC,CAAC;MACvD;MACA,IAAInC,GAAG,GAAG,IAAIuK,cAAc,CAAC,CAAC;MAC9B,IAAIkR,UAAU,GAAG,SAASA,UAAUA,CAAChiB,CAAC,EAAE;QACtC,IAAIc,KAAK,CAACugB,gBAAgB,IAAI,OAAOvgB,KAAK,CAACugB,gBAAgB,KAAK,UAAU,EAAE;UAC1EvgB,KAAK,CAACugB,gBAAgB,CAACrhB,CAAC,CAAC;QAC3B;MACF,CAAC;MACDuG,GAAG,CAACgC,MAAM,CAAC0Z,gBAAgB,CAAC,UAAU,EAAED,UAAU,CAAC;MACnD,IAAIE,MAAM,GAAG;QACXxZ,IAAI,EAAEA,IAAI;QACVC,QAAQ,EAAEoZ,cAAc,CAACpZ,QAAQ,IAAIA,QAAQ,IAAID,IAAI,CAACpF,IAAI;QAC1Dwd,iBAAiB,EAAEiB,cAAc,CAACjB,iBAAiB,IAAIA,iBAAiB;QACxEnX,IAAI,EAAEoY,cAAc,CAACpY,IAAI,IAAIA,IAAI;QACjCoX,MAAM,EAAEgB,cAAc,CAAChB,MAAM,IAAIA,MAAM;QACvCC,aAAa,EAAEe,cAAc,CAACf,aAAa,IAAIA,aAAa;QAC5DC,iBAAiB,EAAEc,cAAc,CAACd,iBAAiB,IAAIA,iBAAiB;QACxEpX,cAAc,EAAEA,cAAc;QAC9BC,UAAU,EAAEiY,cAAc,CAACjY,UAAU,IAAIA,UAAU;QACnDyX,UAAU,EAAEQ,cAAc,CAACR,UAAU,IAAIA,UAAU;QACnDC,aAAa,EAAEO,cAAc,CAACP,aAAa,IAAIA,aAAa;QAC5DC,eAAe,EAAEM,cAAc,CAACN,eAAe,IAAIA,eAAe;QAClEC,aAAa,EAAEK,cAAc,CAACL,aAAa,IAAIA,aAAa;QAC5DC,uBAAuB,EAAEI,cAAc,CAACJ,uBAAuB,IAAIA,uBAAuB;QAC1F3X,cAAc,EAAE+X,cAAc,CAAC/X,cAAc,IAAIA,cAAc;QAC/DlB,SAAS,EAAE,EAAE;QACbC,MAAM,EAAE,CAAC;QACTF,KAAK,EAAE,EAAE;QACTtC,GAAG,EAAEA,GAAG;QACRyC,cAAc,EAAE+Y,cAAc,CAAC/Y,cAAc,IAAIA,cAAc;QAC/DiB,MAAM,EAAE8X,cAAc,CAAC9X,MAAM,IAAIA;MACnC,CAAC;MACD,IAAIkY,WAAW,GAAGpI,aAAa,CAAC,CAAC;MACjC,IAAI,EAAEoI,WAAW,YAAYxa,OAAO,CAAC,EAAE;QACrC,IAAIuZ,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;UAC5CA,OAAO,CAAC;YACNpc,OAAO,EAAE;UACX,CAAC,CAAC;QACJ;QACA;MACF;MACAqd,WAAW,CAAC3a,IAAI,CAAC,UAAUoJ,IAAI,EAAE;QAC/B,IAAI9H,SAAS,GAAG8H,IAAI,CAAC9H,SAAS;UAC5BD,KAAK,GAAG+H,IAAI,CAAC/H,KAAK;UAClBE,MAAM,GAAG6H,IAAI,CAAC7H,MAAM;QACtBmZ,MAAM,CAAC,WAAW,CAAC,GAAGpZ,SAAS;QAC/BoZ,MAAM,CAAC,QAAQ,CAAC,GAAGnZ,MAAM;QACzBmZ,MAAM,CAAC,OAAO,CAAC,GAAGrZ,KAAK;QACvBqR,QAAQ,CAAC3R,MAAM,CAAC2Z,MAAM,EAAE,UAAU3d,GAAG,EAAEkD,MAAM,EAAE;UAC7C,IAAIlD,GAAG,EAAE;YACP,IAAI2c,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;cAC5CxS,OAAO,CAAC0T,GAAG,CAAC7d,GAAG,CAAC;cAChB2c,OAAO,CAAC3c,GAAG,CAAC;YACd;UACF,CAAC,MAAM;YACL,IAAI4c,SAAS,IAAI,OAAOA,SAAS,KAAK,UAAU,EAAE;cAChDA,SAAS,CAAC1Z,MAAM,CAAC;YACnB;UACF;UACAlB,GAAG,CAACgC,MAAM,CAAC8Z,mBAAmB,CAAC,UAAU,EAAEL,UAAU,CAAC;QACxD,CAAC,EAAE;UACDpZ,SAAS,EAAEA;QACb,CAAC,CAAC;QACF0X,QAAQ,CAAC;UACP/Z,GAAG,EAAEA;QACP,CAAC,CAAC;MACJ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,UAAU+b,IAAI,EAAE;QAC1B,IAAI3T,KAAK;QACT,IAAI2T,IAAI,YAAY/e,KAAK,EAAE;UACzBoL,KAAK,GAAG2T,IAAI,CAAC,CAAC,CAAC;QACjB,CAAC,MAAM;UACL3T,KAAK,GAAG2T,IAAI;QACd;QACA,IAAIpB,OAAO,IAAI,OAAOA,OAAO,KAAK,UAAU,EAAE;UAC5CA,OAAO,CAAC;YACNpc,OAAO,EAAE4E,MAAM,CAACiF,KAAK;UACvB,CAAC,CAAC;QACJ;QACA;MACF,CAAC,CAAC;IACJ,CAAC;IACD,OAAO,aAAa1O,cAAc,CAAC,SAAS,CAAC,CAACwa,aAAa,CAAC,OAAO,EAAExE,QAAQ,CAAC,CAAC,CAAC,EAAE4J,SAAS,EAAE;MAC3FE,GAAG,EAAEA,GAAG;MACRzW,IAAI,EAAE,MAAM;MACZiZ,QAAQ,EAAE,SAASA,QAAQA,CAACviB,CAAC,EAAE;QAC7B,IAAIc,KAAK,CAACyhB,QAAQ,IAAI,OAAOzhB,KAAK,CAACyhB,QAAQ,KAAK,UAAU,EAAE;UAC1DzhB,KAAK,CAACyhB,QAAQ,CAACviB,CAAC,CAAC;QACnB;QACAuH,UAAU,CAACvH,CAAC,CAAC;MACf;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EAEFV,OAAO,CAAC+a,SAAS,GAAGA,SAAS;EAC7B/a,OAAO,CAACkjB,MAAM,GAAGnS,QAAQ;EACzB/Q,OAAO,CAACkd,OAAO,GAAGA,OAAO;EACzBld,OAAO,CAACohB,QAAQ,GAAGA,QAAQ;EAC3BphB,OAAO,CAAC6gB,OAAO,GAAGA,OAAO;EAEzB9e,MAAM,CAACC,cAAc,CAAChC,OAAO,EAAE,YAAY,EAAE;IAAEsC,KAAK,EAAE;EAAK,CAAC,CAAC;AAE/D,CAAE,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}