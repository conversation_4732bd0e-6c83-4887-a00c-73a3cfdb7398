{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\n// https://github.com/sindresorhus/is-plain-obj/blob/main/index.js\nexport function isPlainObject(item) {\n  if (typeof item !== 'object' || item === null) {\n    return false;\n  }\n  const prototype = Object.getPrototypeOf(item);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in item) && !(Symbol.iterator in item);\n}\nfunction deepClone(source) {\n  if (!isPlainObject(source)) {\n    return source;\n  }\n  const output = {};\n  Object.keys(source).forEach(key => {\n    output[key] = deepClone(source[key]);\n  });\n  return output;\n}\nexport default function deepmerge(target, source) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n    clone: true\n  };\n  const output = options.clone ? _extends({}, target) : target;\n  if (isPlainObject(target) && isPlainObject(source)) {\n    Object.keys(source).forEach(key => {\n      // Avoid prototype pollution\n      if (key === '__proto__') {\n        return;\n      }\n      if (isPlainObject(source[key]) && key in target && isPlainObject(target[key])) {\n        // Since `output` is a clone of `target` and we have narrowed `target` in this block we can cast to the same type.\n        output[key] = deepmerge(target[key], source[key], options);\n      } else if (options.clone) {\n        output[key] = isPlainObject(source[key]) ? deepClone(source[key]) : source[key];\n      } else {\n        output[key] = source[key];\n      }\n    });\n  }\n  return output;\n}", "map": {"version": 3, "names": ["_extends", "isPlainObject", "item", "prototype", "Object", "getPrototypeOf", "Symbol", "toStringTag", "iterator", "deepClone", "source", "output", "keys", "for<PERSON>ach", "key", "deepmerge", "target", "options", "arguments", "length", "undefined", "clone"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/deepmerge/deepmerge.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\n// https://github.com/sindresorhus/is-plain-obj/blob/main/index.js\nexport function isPlainObject(item) {\n  if (typeof item !== 'object' || item === null) {\n    return false;\n  }\n  const prototype = Object.getPrototypeOf(item);\n  return (prototype === null || prototype === Object.prototype || Object.getPrototypeOf(prototype) === null) && !(Symbol.toStringTag in item) && !(Symbol.iterator in item);\n}\nfunction deepClone(source) {\n  if (!isPlainObject(source)) {\n    return source;\n  }\n  const output = {};\n  Object.keys(source).forEach(key => {\n    output[key] = deepClone(source[key]);\n  });\n  return output;\n}\nexport default function deepmerge(target, source, options = {\n  clone: true\n}) {\n  const output = options.clone ? _extends({}, target) : target;\n  if (isPlainObject(target) && isPlainObject(source)) {\n    Object.keys(source).forEach(key => {\n      // Avoid prototype pollution\n      if (key === '__proto__') {\n        return;\n      }\n      if (isPlainObject(source[key]) && key in target && isPlainObject(target[key])) {\n        // Since `output` is a clone of `target` and we have narrowed `target` in this block we can cast to the same type.\n        output[key] = deepmerge(target[key], source[key], options);\n      } else if (options.clone) {\n        output[key] = isPlainObject(source[key]) ? deepClone(source[key]) : source[key];\n      } else {\n        output[key] = source[key];\n      }\n    });\n  }\n  return output;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD;AACA,OAAO,SAASC,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,IAAI,EAAE;IAC7C,OAAO,KAAK;EACd;EACA,MAAMC,SAAS,GAAGC,MAAM,CAACC,cAAc,CAACH,IAAI,CAAC;EAC7C,OAAO,CAACC,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAKC,MAAM,CAACD,SAAS,IAAIC,MAAM,CAACC,cAAc,CAACF,SAAS,CAAC,KAAK,IAAI,KAAK,EAAEG,MAAM,CAACC,WAAW,IAAIL,IAAI,CAAC,IAAI,EAAEI,MAAM,CAACE,QAAQ,IAAIN,IAAI,CAAC;AAC3K;AACA,SAASO,SAASA,CAACC,MAAM,EAAE;EACzB,IAAI,CAACT,aAAa,CAACS,MAAM,CAAC,EAAE;IAC1B,OAAOA,MAAM;EACf;EACA,MAAMC,MAAM,GAAG,CAAC,CAAC;EACjBP,MAAM,CAACQ,IAAI,CAACF,MAAM,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;IACjCH,MAAM,CAACG,GAAG,CAAC,GAAGL,SAAS,CAACC,MAAM,CAACI,GAAG,CAAC,CAAC;EACtC,CAAC,CAAC;EACF,OAAOH,MAAM;AACf;AACA,eAAe,SAASI,SAASA,CAACC,MAAM,EAAEN,MAAM,EAE7C;EAAA,IAF+CO,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG;IAC1DG,KAAK,EAAE;EACT,CAAC;EACC,MAAMV,MAAM,GAAGM,OAAO,CAACI,KAAK,GAAGrB,QAAQ,CAAC,CAAC,CAAC,EAAEgB,MAAM,CAAC,GAAGA,MAAM;EAC5D,IAAIf,aAAa,CAACe,MAAM,CAAC,IAAIf,aAAa,CAACS,MAAM,CAAC,EAAE;IAClDN,MAAM,CAACQ,IAAI,CAACF,MAAM,CAAC,CAACG,OAAO,CAACC,GAAG,IAAI;MACjC;MACA,IAAIA,GAAG,KAAK,WAAW,EAAE;QACvB;MACF;MACA,IAAIb,aAAa,CAACS,MAAM,CAACI,GAAG,CAAC,CAAC,IAAIA,GAAG,IAAIE,MAAM,IAAIf,aAAa,CAACe,MAAM,CAACF,GAAG,CAAC,CAAC,EAAE;QAC7E;QACAH,MAAM,CAACG,GAAG,CAAC,GAAGC,SAAS,CAACC,MAAM,CAACF,GAAG,CAAC,EAAEJ,MAAM,CAACI,GAAG,CAAC,EAAEG,OAAO,CAAC;MAC5D,CAAC,MAAM,IAAIA,OAAO,CAACI,KAAK,EAAE;QACxBV,MAAM,CAACG,GAAG,CAAC,GAAGb,aAAa,CAACS,MAAM,CAACI,GAAG,CAAC,CAAC,GAAGL,SAAS,CAACC,MAAM,CAACI,GAAG,CAAC,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;MACjF,CAAC,MAAM;QACLH,MAAM,CAACG,GAAG,CAAC,GAAGJ,MAAM,CAACI,GAAG,CAAC;MAC3B;IACF,CAAC,CAAC;EACJ;EACA,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}