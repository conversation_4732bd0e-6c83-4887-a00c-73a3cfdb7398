{"ast": null, "code": "export { default } from './getValidReactChildren';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/getValidReactChildren/index.js"], "sourcesContent": ["export { default } from './getValidReactChildren';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}