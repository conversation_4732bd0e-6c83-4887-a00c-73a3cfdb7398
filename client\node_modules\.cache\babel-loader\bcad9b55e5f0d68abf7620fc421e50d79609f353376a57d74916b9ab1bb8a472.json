{"ast": null, "code": "export { ClickAwayListener as default } from '@mui/base/ClickAwayListener';", "map": {"version": 3, "names": ["ClickAwayListener", "default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/ClickAwayListener/index.js"], "sourcesContent": ["export { ClickAwayListener as default } from '@mui/base/ClickAwayListener';"], "mappings": "AAAA,SAASA,iBAAiB,IAAIC,OAAO,QAAQ,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}