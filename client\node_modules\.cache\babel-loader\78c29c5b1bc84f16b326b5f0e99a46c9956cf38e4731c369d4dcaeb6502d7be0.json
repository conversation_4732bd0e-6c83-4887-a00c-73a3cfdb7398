{"ast": null, "code": "'use client';\n\nimport React from 'react';\n\n/**\n * @internal\n */\nconst SkeletonThemeContext = React.createContext({});\n\n/* eslint-disable react/no-array-index-key */\nconst defaultEnableAnimation = true;\n// For performance & cleanliness, don't add any inline styles unless we have to\nfunction styleOptionsToCssProperties(_ref) {\n  let {\n    baseColor,\n    highlightColor,\n    width,\n    height,\n    borderRadius,\n    circle,\n    direction,\n    duration,\n    enableAnimation = defaultEnableAnimation\n  } = _ref;\n  const style = {};\n  if (direction === 'rtl') style['--animation-direction'] = 'reverse';\n  if (typeof duration === 'number') style['--animation-duration'] = \"\".concat(duration, \"s\");\n  if (!enableAnimation) style['--pseudo-element-display'] = 'none';\n  if (typeof width === 'string' || typeof width === 'number') style.width = width;\n  if (typeof height === 'string' || typeof height === 'number') style.height = height;\n  if (typeof borderRadius === 'string' || typeof borderRadius === 'number') style.borderRadius = borderRadius;\n  if (circle) style.borderRadius = '50%';\n  if (typeof baseColor !== 'undefined') style['--base-color'] = baseColor;\n  if (typeof highlightColor !== 'undefined') style['--highlight-color'] = highlightColor;\n  return style;\n}\nfunction Skeleton(_ref2) {\n  let {\n    count = 1,\n    wrapper: Wrapper,\n    className: customClassName,\n    containerClassName,\n    containerTestId,\n    circle = false,\n    style: styleProp,\n    ...originalPropsStyleOptions\n  } = _ref2;\n  var _a, _b, _c;\n  const contextStyleOptions = React.useContext(SkeletonThemeContext);\n  const propsStyleOptions = {\n    ...originalPropsStyleOptions\n  };\n  // DO NOT overwrite style options from the context if `propsStyleOptions`\n  // has properties explicity set to undefined\n  for (const [key, value] of Object.entries(originalPropsStyleOptions)) {\n    if (typeof value === 'undefined') {\n      delete propsStyleOptions[key];\n    }\n  }\n  // Props take priority over context\n  const styleOptions = {\n    ...contextStyleOptions,\n    ...propsStyleOptions,\n    circle\n  };\n  // `styleProp` has the least priority out of everything\n  const style = {\n    ...styleProp,\n    ...styleOptionsToCssProperties(styleOptions)\n  };\n  let className = 'react-loading-skeleton';\n  if (customClassName) className += \" \".concat(customClassName);\n  const inline = (_a = styleOptions.inline) !== null && _a !== void 0 ? _a : false;\n  const elements = [];\n  const countCeil = Math.ceil(count);\n  for (let i = 0; i < countCeil; i++) {\n    let thisStyle = style;\n    if (countCeil > count && i === countCeil - 1) {\n      // count is not an integer and we've reached the last iteration of\n      // the loop, so add a \"fractional\" skeleton.\n      //\n      // For example, if count is 3.5, we've already added 3 full\n      // skeletons, so now we add one more skeleton that is 0.5 times the\n      // original width.\n      const width = (_b = thisStyle.width) !== null && _b !== void 0 ? _b : '100%'; // 100% is the default since that's what's in the CSS\n      const fractionalPart = count % 1;\n      const fractionalWidth = typeof width === 'number' ? width * fractionalPart : \"calc(\".concat(width, \" * \").concat(fractionalPart, \")\");\n      thisStyle = {\n        ...thisStyle,\n        width: fractionalWidth\n      };\n    }\n    const skeletonSpan = React.createElement(\"span\", {\n      className: className,\n      style: thisStyle,\n      key: i\n    }, \"\\u200C\");\n    if (inline) {\n      elements.push(skeletonSpan);\n    } else {\n      // Without the <br />, the skeleton lines will all run together if\n      // `width` is specified\n      elements.push(React.createElement(React.Fragment, {\n        key: i\n      }, skeletonSpan, React.createElement(\"br\", null)));\n    }\n  }\n  return React.createElement(\"span\", {\n    className: containerClassName,\n    \"data-testid\": containerTestId,\n    \"aria-live\": \"polite\",\n    \"aria-busy\": (_c = styleOptions.enableAnimation) !== null && _c !== void 0 ? _c : defaultEnableAnimation\n  }, Wrapper ? elements.map((el, i) => React.createElement(Wrapper, {\n    key: i\n  }, el)) : elements);\n}\nfunction SkeletonTheme(_ref3) {\n  let {\n    children,\n    ...styleOptions\n  } = _ref3;\n  return React.createElement(SkeletonThemeContext.Provider, {\n    value: styleOptions\n  }, children);\n}\nexport { SkeletonTheme, Skeleton as default };", "map": {"version": 3, "names": ["React", "SkeletonThemeContext", "createContext", "defaultEnableAnimation", "styleOptionsToCssProperties", "_ref", "baseColor", "highlightColor", "width", "height", "borderRadius", "circle", "direction", "duration", "enableAnimation", "style", "concat", "Skeleton", "_ref2", "count", "wrapper", "Wrapper", "className", "customClassName", "containerClassName", "containerTestId", "styleProp", "originalPropsStyleOptions", "_a", "_b", "_c", "contextStyleOptions", "useContext", "propsStyleOptions", "key", "value", "Object", "entries", "styleOptions", "inline", "elements", "count<PERSON><PERSON>", "Math", "ceil", "i", "thisStyle", "fractionalPart", "fractionalWidth", "skeletonSpan", "createElement", "push", "Fragment", "map", "el", "SkeletonTheme", "_ref3", "children", "Provider", "default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/react-loading-skeleton/dist/index.js"], "sourcesContent": ["'use client';\nimport React from 'react';\n\n/**\n * @internal\n */\nconst SkeletonThemeContext = React.createContext({});\n\n/* eslint-disable react/no-array-index-key */\nconst defaultEnableAnimation = true;\n// For performance & cleanliness, don't add any inline styles unless we have to\nfunction styleOptionsToCssProperties({ baseColor, highlightColor, width, height, borderRadius, circle, direction, duration, enableAnimation = defaultEnableAnimation, }) {\n    const style = {};\n    if (direction === 'rtl')\n        style['--animation-direction'] = 'reverse';\n    if (typeof duration === 'number')\n        style['--animation-duration'] = `${duration}s`;\n    if (!enableAnimation)\n        style['--pseudo-element-display'] = 'none';\n    if (typeof width === 'string' || typeof width === 'number')\n        style.width = width;\n    if (typeof height === 'string' || typeof height === 'number')\n        style.height = height;\n    if (typeof borderRadius === 'string' || typeof borderRadius === 'number')\n        style.borderRadius = borderRadius;\n    if (circle)\n        style.borderRadius = '50%';\n    if (typeof baseColor !== 'undefined')\n        style['--base-color'] = baseColor;\n    if (typeof highlightColor !== 'undefined')\n        style['--highlight-color'] = highlightColor;\n    return style;\n}\nfunction Skeleton({ count = 1, wrapper: Wrapper, className: customClassName, containerClassName, containerTestId, circle = false, style: styleProp, ...originalPropsStyleOptions }) {\n    var _a, _b, _c;\n    const contextStyleOptions = React.useContext(SkeletonThemeContext);\n    const propsStyleOptions = { ...originalPropsStyleOptions };\n    // DO NOT overwrite style options from the context if `propsStyleOptions`\n    // has properties explicity set to undefined\n    for (const [key, value] of Object.entries(originalPropsStyleOptions)) {\n        if (typeof value === 'undefined') {\n            delete propsStyleOptions[key];\n        }\n    }\n    // Props take priority over context\n    const styleOptions = {\n        ...contextStyleOptions,\n        ...propsStyleOptions,\n        circle,\n    };\n    // `styleProp` has the least priority out of everything\n    const style = {\n        ...styleProp,\n        ...styleOptionsToCssProperties(styleOptions),\n    };\n    let className = 'react-loading-skeleton';\n    if (customClassName)\n        className += ` ${customClassName}`;\n    const inline = (_a = styleOptions.inline) !== null && _a !== void 0 ? _a : false;\n    const elements = [];\n    const countCeil = Math.ceil(count);\n    for (let i = 0; i < countCeil; i++) {\n        let thisStyle = style;\n        if (countCeil > count && i === countCeil - 1) {\n            // count is not an integer and we've reached the last iteration of\n            // the loop, so add a \"fractional\" skeleton.\n            //\n            // For example, if count is 3.5, we've already added 3 full\n            // skeletons, so now we add one more skeleton that is 0.5 times the\n            // original width.\n            const width = (_b = thisStyle.width) !== null && _b !== void 0 ? _b : '100%'; // 100% is the default since that's what's in the CSS\n            const fractionalPart = count % 1;\n            const fractionalWidth = typeof width === 'number'\n                ? width * fractionalPart\n                : `calc(${width} * ${fractionalPart})`;\n            thisStyle = { ...thisStyle, width: fractionalWidth };\n        }\n        const skeletonSpan = (React.createElement(\"span\", { className: className, style: thisStyle, key: i }, \"\\u200C\"));\n        if (inline) {\n            elements.push(skeletonSpan);\n        }\n        else {\n            // Without the <br />, the skeleton lines will all run together if\n            // `width` is specified\n            elements.push(React.createElement(React.Fragment, { key: i },\n                skeletonSpan,\n                React.createElement(\"br\", null)));\n        }\n    }\n    return (React.createElement(\"span\", { className: containerClassName, \"data-testid\": containerTestId, \"aria-live\": \"polite\", \"aria-busy\": (_c = styleOptions.enableAnimation) !== null && _c !== void 0 ? _c : defaultEnableAnimation }, Wrapper\n        ? elements.map((el, i) => React.createElement(Wrapper, { key: i }, el))\n        : elements));\n}\n\nfunction SkeletonTheme({ children, ...styleOptions }) {\n    return (React.createElement(SkeletonThemeContext.Provider, { value: styleOptions }, children));\n}\n\nexport { SkeletonTheme, Skeleton as default };\n"], "mappings": "AAAA,YAAY;;AACZ,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA;AACA;AACA,MAAMC,oBAAoB,GAAGD,KAAK,CAACE,aAAa,CAAC,CAAC,CAAC,CAAC;;AAEpD;AACA,MAAMC,sBAAsB,GAAG,IAAI;AACnC;AACA,SAASC,2BAA2BA,CAAAC,IAAA,EAAqI;EAAA,IAApI;IAAEC,SAAS;IAAEC,cAAc;IAAEC,KAAK;IAAEC,MAAM;IAAEC,YAAY;IAAEC,MAAM;IAAEC,SAAS;IAAEC,QAAQ;IAAEC,eAAe,GAAGX;EAAwB,CAAC,GAAAE,IAAA;EACnK,MAAMU,KAAK,GAAG,CAAC,CAAC;EAChB,IAAIH,SAAS,KAAK,KAAK,EACnBG,KAAK,CAAC,uBAAuB,CAAC,GAAG,SAAS;EAC9C,IAAI,OAAOF,QAAQ,KAAK,QAAQ,EAC5BE,KAAK,CAAC,sBAAsB,CAAC,MAAAC,MAAA,CAAMH,QAAQ,MAAG;EAClD,IAAI,CAACC,eAAe,EAChBC,KAAK,CAAC,0BAA0B,CAAC,GAAG,MAAM;EAC9C,IAAI,OAAOP,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,QAAQ,EACtDO,KAAK,CAACP,KAAK,GAAGA,KAAK;EACvB,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAI,OAAOA,MAAM,KAAK,QAAQ,EACxDM,KAAK,CAACN,MAAM,GAAGA,MAAM;EACzB,IAAI,OAAOC,YAAY,KAAK,QAAQ,IAAI,OAAOA,YAAY,KAAK,QAAQ,EACpEK,KAAK,CAACL,YAAY,GAAGA,YAAY;EACrC,IAAIC,MAAM,EACNI,KAAK,CAACL,YAAY,GAAG,KAAK;EAC9B,IAAI,OAAOJ,SAAS,KAAK,WAAW,EAChCS,KAAK,CAAC,cAAc,CAAC,GAAGT,SAAS;EACrC,IAAI,OAAOC,cAAc,KAAK,WAAW,EACrCQ,KAAK,CAAC,mBAAmB,CAAC,GAAGR,cAAc;EAC/C,OAAOQ,KAAK;AAChB;AACA,SAASE,QAAQA,CAAAC,KAAA,EAAmK;EAAA,IAAlK;IAAEC,KAAK,GAAG,CAAC;IAAEC,OAAO,EAAEC,OAAO;IAAEC,SAAS,EAAEC,eAAe;IAAEC,kBAAkB;IAAEC,eAAe;IAAEd,MAAM,GAAG,KAAK;IAAEI,KAAK,EAAEW,SAAS;IAAE,GAAGC;EAA0B,CAAC,GAAAT,KAAA;EAC9K,IAAIU,EAAE,EAAEC,EAAE,EAAEC,EAAE;EACd,MAAMC,mBAAmB,GAAG/B,KAAK,CAACgC,UAAU,CAAC/B,oBAAoB,CAAC;EAClE,MAAMgC,iBAAiB,GAAG;IAAE,GAAGN;EAA0B,CAAC;EAC1D;EACA;EACA,KAAK,MAAM,CAACO,GAAG,EAAEC,KAAK,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACV,yBAAyB,CAAC,EAAE;IAClE,IAAI,OAAOQ,KAAK,KAAK,WAAW,EAAE;MAC9B,OAAOF,iBAAiB,CAACC,GAAG,CAAC;IACjC;EACJ;EACA;EACA,MAAMI,YAAY,GAAG;IACjB,GAAGP,mBAAmB;IACtB,GAAGE,iBAAiB;IACpBtB;EACJ,CAAC;EACD;EACA,MAAMI,KAAK,GAAG;IACV,GAAGW,SAAS;IACZ,GAAGtB,2BAA2B,CAACkC,YAAY;EAC/C,CAAC;EACD,IAAIhB,SAAS,GAAG,wBAAwB;EACxC,IAAIC,eAAe,EACfD,SAAS,QAAAN,MAAA,CAAQO,eAAe,CAAE;EACtC,MAAMgB,MAAM,GAAG,CAACX,EAAE,GAAGU,YAAY,CAACC,MAAM,MAAM,IAAI,IAAIX,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,KAAK;EAChF,MAAMY,QAAQ,GAAG,EAAE;EACnB,MAAMC,SAAS,GAAGC,IAAI,CAACC,IAAI,CAACxB,KAAK,CAAC;EAClC,KAAK,IAAIyB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,EAAEG,CAAC,EAAE,EAAE;IAChC,IAAIC,SAAS,GAAG9B,KAAK;IACrB,IAAI0B,SAAS,GAAGtB,KAAK,IAAIyB,CAAC,KAAKH,SAAS,GAAG,CAAC,EAAE;MAC1C;MACA;MACA;MACA;MACA;MACA;MACA,MAAMjC,KAAK,GAAG,CAACqB,EAAE,GAAGgB,SAAS,CAACrC,KAAK,MAAM,IAAI,IAAIqB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,MAAM,CAAC,CAAC;MAC9E,MAAMiB,cAAc,GAAG3B,KAAK,GAAG,CAAC;MAChC,MAAM4B,eAAe,GAAG,OAAOvC,KAAK,KAAK,QAAQ,GAC3CA,KAAK,GAAGsC,cAAc,WAAA9B,MAAA,CACdR,KAAK,SAAAQ,MAAA,CAAM8B,cAAc,MAAG;MAC1CD,SAAS,GAAG;QAAE,GAAGA,SAAS;QAAErC,KAAK,EAAEuC;MAAgB,CAAC;IACxD;IACA,MAAMC,YAAY,GAAIhD,KAAK,CAACiD,aAAa,CAAC,MAAM,EAAE;MAAE3B,SAAS,EAAEA,SAAS;MAAEP,KAAK,EAAE8B,SAAS;MAAEX,GAAG,EAAEU;IAAE,CAAC,EAAE,QAAQ,CAAE;IAChH,IAAIL,MAAM,EAAE;MACRC,QAAQ,CAACU,IAAI,CAACF,YAAY,CAAC;IAC/B,CAAC,MACI;MACD;MACA;MACAR,QAAQ,CAACU,IAAI,CAAClD,KAAK,CAACiD,aAAa,CAACjD,KAAK,CAACmD,QAAQ,EAAE;QAAEjB,GAAG,EAAEU;MAAE,CAAC,EACxDI,YAAY,EACZhD,KAAK,CAACiD,aAAa,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC;IACzC;EACJ;EACA,OAAQjD,KAAK,CAACiD,aAAa,CAAC,MAAM,EAAE;IAAE3B,SAAS,EAAEE,kBAAkB;IAAE,aAAa,EAAEC,eAAe;IAAE,WAAW,EAAE,QAAQ;IAAE,WAAW,EAAE,CAACK,EAAE,GAAGQ,YAAY,CAACxB,eAAe,MAAM,IAAI,IAAIgB,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG3B;EAAuB,CAAC,EAAEkB,OAAO,GACzOmB,QAAQ,CAACY,GAAG,CAAC,CAACC,EAAE,EAAET,CAAC,KAAK5C,KAAK,CAACiD,aAAa,CAAC5B,OAAO,EAAE;IAAEa,GAAG,EAAEU;EAAE,CAAC,EAAES,EAAE,CAAC,CAAC,GACrEb,QAAQ,CAAC;AACnB;AAEA,SAASc,aAAaA,CAAAC,KAAA,EAAgC;EAAA,IAA/B;IAAEC,QAAQ;IAAE,GAAGlB;EAAa,CAAC,GAAAiB,KAAA;EAChD,OAAQvD,KAAK,CAACiD,aAAa,CAAChD,oBAAoB,CAACwD,QAAQ,EAAE;IAAEtB,KAAK,EAAEG;EAAa,CAAC,EAAEkB,QAAQ,CAAC;AACjG;AAEA,SAASF,aAAa,EAAErC,QAAQ,IAAIyC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}