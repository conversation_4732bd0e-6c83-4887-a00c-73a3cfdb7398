{"ast": null, "code": "import React from\"react\";import{Link}from\"react-router-dom\";import{NoProfile}from\"../assets\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const FriendsCard=_ref=>{let{friends}=_ref;return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsxs(\"div\",{className:\"w-full bg-primary shadow-sm rounded-lg px-6 py-5\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between text-ascent-1 pb-2 border-b border-[#66666645]\",children:[/*#__PURE__*/_jsx(\"span\",{children:\" Connections\"}),/*#__PURE__*/_jsx(\"span\",{children:friends===null||friends===void 0?void 0:friends.length})]}),/*#__PURE__*/_jsx(\"div\",{className:\"w-full flex flex-col gap-4 pt-4\",children:friends===null||friends===void 0?void 0:friends.map(friend=>{var _friend$profileUrl,_friend$profession;return/*#__PURE__*/_jsxs(Link,{to:\"/profile/\"+(friend===null||friend===void 0?void 0:friend._id),className:\"w-full flex gap-4 items-center cursor-pointer\",children:[/*#__PURE__*/_jsx(\"img\",{src:(_friend$profileUrl=friend===null||friend===void 0?void 0:friend.profileUrl)!==null&&_friend$profileUrl!==void 0?_friend$profileUrl:NoProfile,alt:friend===null||friend===void 0?void 0:friend.firstName,className:\"w-10 h-10 object-cover rounded-full\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsxs(\"p\",{className:\"text-base font-medium text-ascent-1\",children:[friend===null||friend===void 0?void 0:friend.firstName,\" \",friend===null||friend===void 0?void 0:friend.lastName]}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-ascent-2\",children:(_friend$profession=friend===null||friend===void 0?void 0:friend.profession)!==null&&_friend$profession!==void 0?_friend$profession:\"No Profession\"})]})]},friend===null||friend===void 0?void 0:friend._id);})})]})});};export default FriendsCard;", "map": {"version": 3, "names": ["React", "Link", "NoProfile", "jsx", "_jsx", "jsxs", "_jsxs", "FriendsCard", "_ref", "friends", "children", "className", "length", "map", "friend", "_friend$profileUrl", "_friend$profession", "to", "_id", "src", "profileUrl", "alt", "firstName", "lastName", "profession"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/components/FriendsCard.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { Link } from \"react-router-dom\";\r\nimport { NoProfile } from \"../assets\";\r\n\r\nconst FriendsCard = ({ friends }) => {\r\n  return (\r\n    <div>\r\n      <div className='w-full bg-primary shadow-sm rounded-lg px-6 py-5'>\r\n        <div className='flex items-center justify-between text-ascent-1 pb-2 border-b border-[#66666645]'>\r\n          <span> Connections</span>\r\n          <span>{friends?.length}</span>\r\n        </div>\r\n\r\n        <div className='w-full flex flex-col gap-4 pt-4'>\r\n          {friends?.map((friend) => (\r\n            <Link\r\n              to={\"/profile/\" + friend?._id}\r\n              key={friend?._id}\r\n              className='w-full flex gap-4 items-center cursor-pointer'\r\n            >\r\n              <img\r\n                src={friend?.profileUrl ?? NoProfile}\r\n                alt={friend?.firstName}\r\n                className='w-10 h-10 object-cover rounded-full'\r\n              />\r\n              <div className='flex-1'>\r\n                <p className='text-base font-medium text-ascent-1'>\r\n                  {friend?.firstName} {friend?.lastName}\r\n                </p>\r\n                <span className='text-sm text-ascent-2'>\r\n                  {friend?.profession ?? \"No Profession\"}\r\n                </span>\r\n              </div>\r\n            </Link>\r\n          ))}\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FriendsCard;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,SAAS,KAAQ,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAEtC,KAAM,CAAAC,WAAW,CAAGC,IAAA,EAAiB,IAAhB,CAAEC,OAAQ,CAAC,CAAAD,IAAA,CAC9B,mBACEJ,IAAA,QAAAM,QAAA,cACEJ,KAAA,QAAKK,SAAS,CAAC,kDAAkD,CAAAD,QAAA,eAC/DJ,KAAA,QAAKK,SAAS,CAAC,kFAAkF,CAAAD,QAAA,eAC/FN,IAAA,SAAAM,QAAA,CAAM,cAAY,CAAM,CAAC,cACzBN,IAAA,SAAAM,QAAA,CAAOD,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEG,MAAM,CAAO,CAAC,EAC3B,CAAC,cAENR,IAAA,QAAKO,SAAS,CAAC,iCAAiC,CAAAD,QAAA,CAC7CD,OAAO,SAAPA,OAAO,iBAAPA,OAAO,CAAEI,GAAG,CAAEC,MAAM,OAAAC,kBAAA,CAAAC,kBAAA,oBACnBV,KAAA,CAACL,IAAI,EACHgB,EAAE,CAAE,WAAW,EAAGH,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEI,GAAG,CAAC,CAE9BP,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAEzDN,IAAA,QACEe,GAAG,EAAAJ,kBAAA,CAAED,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEM,UAAU,UAAAL,kBAAA,UAAAA,kBAAA,CAAIb,SAAU,CACrCmB,GAAG,CAAEP,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEQ,SAAU,CACvBX,SAAS,CAAC,qCAAqC,CAChD,CAAC,cACFL,KAAA,QAAKK,SAAS,CAAC,QAAQ,CAAAD,QAAA,eACrBJ,KAAA,MAAGK,SAAS,CAAC,qCAAqC,CAAAD,QAAA,EAC/CI,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEQ,SAAS,CAAC,GAAC,CAACR,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAES,QAAQ,EACpC,CAAC,cACJnB,IAAA,SAAMO,SAAS,CAAC,uBAAuB,CAAAD,QAAA,EAAAM,kBAAA,CACpCF,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEU,UAAU,UAAAR,kBAAA,UAAAA,kBAAA,CAAI,eAAe,CAClC,CAAC,EACJ,CAAC,GAfDF,MAAM,SAANA,MAAM,iBAANA,MAAM,CAAEI,GAgBT,CAAC,EACR,CAAC,CACC,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAX,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}