{"ast": null, "code": "export const BACKEND_URL=process.env.REACT_APP_BACKEND_URL;", "map": {"version": 3, "names": ["BACKEND_URL", "process", "env", "REACT_APP_BACKEND_URL"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/utils/api.js"], "sourcesContent": ["export const BACKEND_URL = process.env.REACT_APP_BACKEND_URL;\r\n"], "mappings": "AAAA,MAAO,MAAM,CAAAA,WAAW,CAAGC,OAAO,CAACC,GAAG,CAACC,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}