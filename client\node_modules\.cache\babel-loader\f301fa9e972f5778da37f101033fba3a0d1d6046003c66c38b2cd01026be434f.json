{"ast": null, "code": "import deepmerge from '@mui/utils/deepmerge';\nfunction merge(acc, item) {\n  if (!item) {\n    return acc;\n  }\n  return deepmerge(acc, item, {\n    clone: false // No need to clone deep, it's way faster.\n  });\n}\nexport default merge;", "map": {"version": 3, "names": ["deepmerge", "merge", "acc", "item", "clone"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/system/esm/merge.js"], "sourcesContent": ["import deepmerge from '@mui/utils/deepmerge';\nfunction merge(acc, item) {\n  if (!item) {\n    return acc;\n  }\n  return deepmerge(acc, item, {\n    clone: false // No need to clone deep, it's way faster.\n  });\n}\nexport default merge;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,sBAAsB;AAC5C,SAASC,KAAKA,CAACC,GAAG,EAAEC,IAAI,EAAE;EACxB,IAAI,CAACA,IAAI,EAAE;IACT,OAAOD,GAAG;EACZ;EACA,OAAOF,SAAS,CAACE,GAAG,EAAEC,IAAI,EAAE;IAC1BC,KAAK,EAAE,KAAK,CAAC;EACf,CAAC,CAAC;AACJ;AACA,eAAeH,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}