{"ast": null, "code": "export { NoSsr as default } from '@mui/base/NoSsr';", "map": {"version": 3, "names": ["NoSsr", "default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/NoSsr/index.js"], "sourcesContent": ["export { NoSsr as default } from '@mui/base/NoSsr';"], "mappings": "AAAA,SAASA,KAAK,IAAIC,OAAO,QAAQ,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}