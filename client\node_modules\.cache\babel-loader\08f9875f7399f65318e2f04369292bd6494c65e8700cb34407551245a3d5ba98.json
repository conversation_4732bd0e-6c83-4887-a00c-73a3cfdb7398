{"ast": null, "code": "'use client';\n\nexport { default } from './DialogContent';\nexport { default as dialogContentClasses } from './dialogContentClasses';\nexport * from './dialogContentClasses';", "map": {"version": 3, "names": ["default", "dialogContentClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/DialogContent/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './DialogContent';\nexport { default as dialogContentClasses } from './dialogContentClasses';\nexport * from './dialogContentClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,iBAAiB;AACzC,SAASA,OAAO,IAAIC,oBAAoB,QAAQ,wBAAwB;AACxE,cAAc,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}