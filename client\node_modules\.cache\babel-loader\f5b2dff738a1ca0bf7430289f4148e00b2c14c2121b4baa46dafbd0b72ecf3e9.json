{"ast": null, "code": "export { default } from './createChainedFunction';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/createChainedFunction/index.js"], "sourcesContent": ["export { default } from './createChainedFunction';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}