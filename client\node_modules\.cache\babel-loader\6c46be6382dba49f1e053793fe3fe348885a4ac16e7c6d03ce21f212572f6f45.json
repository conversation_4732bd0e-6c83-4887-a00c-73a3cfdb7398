{"ast": null, "code": "'use client';\n\nimport createStyled, { shouldForwardProp } from '@mui/system/createStyled';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport const rootShouldForwardProp = prop => shouldForwardProp(prop) && prop !== 'classes';\nexport const slotShouldForwardProp = shouldForwardProp;\nconst styled = createStyled({\n  themeId: THEME_ID,\n  defaultTheme,\n  rootShouldForwardProp\n});\nexport default styled;", "map": {"version": 3, "names": ["createStyled", "shouldForwardProp", "defaultTheme", "THEME_ID", "rootShouldForwardProp", "prop", "slotShouldForwardProp", "styled", "themeId"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/styles/styled.js"], "sourcesContent": ["'use client';\n\nimport createStyled, { shouldForwardProp } from '@mui/system/createStyled';\nimport defaultTheme from './defaultTheme';\nimport THEME_ID from './identifier';\nexport const rootShouldForwardProp = prop => shouldForwardProp(prop) && prop !== 'classes';\nexport const slotShouldForwardProp = shouldForwardProp;\nconst styled = createStyled({\n  themeId: THEME_ID,\n  defaultTheme,\n  rootShouldForwardProp\n});\nexport default styled;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,YAAY,IAAIC,iBAAiB,QAAQ,0BAA0B;AAC1E,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,QAAQ,MAAM,cAAc;AACnC,OAAO,MAAMC,qBAAqB,GAAGC,IAAI,IAAIJ,iBAAiB,CAACI,IAAI,CAAC,IAAIA,IAAI,KAAK,SAAS;AAC1F,OAAO,MAAMC,qBAAqB,GAAGL,iBAAiB;AACtD,MAAMM,MAAM,GAAGP,YAAY,CAAC;EAC1BQ,OAAO,EAAEL,QAAQ;EACjBD,YAAY;EACZE;AACF,CAAC,CAAC;AACF,eAAeG,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}