{"ast": null, "code": "import React from 'react';\nvar isCheckBoxInput = element => element.type === 'checkbox';\nvar isDateObject = value => value instanceof Date;\nvar isNullOrUndefined = value => value == null;\nconst isObjectType = value => typeof value === 'object';\nvar isObject = value => !isNullOrUndefined(value) && !Array.isArray(value) && isObjectType(value) && !isDateObject(value);\nvar getEventValue = event => isObject(event) && event.target ? isCheckBoxInput(event.target) ? event.target.checked : event.target.value : event;\nvar getNodeParentName = name => name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\nvar isNameInFieldArray = (names, name) => names.has(getNodeParentName(name));\nvar isPlainObject = tempObject => {\n  const prototypeCopy = tempObject.constructor && tempObject.constructor.prototype;\n  return isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf');\n};\nvar isWeb = typeof window !== 'undefined' && typeof window.HTMLElement !== 'undefined' && typeof document !== 'undefined';\nfunction cloneObject(data) {\n  let copy;\n  const isArray = Array.isArray(data);\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (!(isWeb && (data instanceof Blob || data instanceof FileList)) && (isArray || isObject(data))) {\n    copy = isArray ? [] : {};\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n  return copy;\n}\nvar compact = value => Array.isArray(value) ? value.filter(Boolean) : [];\nvar isUndefined = val => val === undefined;\nvar get = (object, path, defaultValue) => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n  const result = compact(path.split(/[,[\\].]+?/)).reduce((result, key) => isNullOrUndefined(result) ? result : result[key], object);\n  return isUndefined(result) || result === object ? isUndefined(object[path]) ? defaultValue : object[path] : result;\n};\nvar isBoolean = value => typeof value === 'boolean';\nconst EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change'\n};\nconst VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all'\n};\nconst INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate'\n};\nconst HookFormContext = React.createContext(null);\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst useFormContext = () => React.useContext(HookFormContext);\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nconst FormProvider = props => {\n  const {\n    children,\n    ...data\n  } = props;\n  return React.createElement(HookFormContext.Provider, {\n    value: data\n  }, children);\n};\nvar getProxyFormState = function (formState, control, localProxyFormState) {\n  let isRoot = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : true;\n  const result = {\n    defaultValues: control._defaultValues\n  };\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key;\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      }\n    });\n  }\n  return result;\n};\nvar isEmptyObject = value => isObject(value) && !Object.keys(value).length;\nvar shouldRenderFormState = (formStateData, _proxyFormState, updateFormState, isRoot) => {\n  updateFormState(formStateData);\n  const {\n    name,\n    ...formState\n  } = formStateData;\n  return isEmptyObject(formState) || Object.keys(formState).length >= Object.keys(_proxyFormState).length || Object.keys(formState).find(key => _proxyFormState[key] === (!isRoot || VALIDATION_MODE.all));\n};\nvar convertToArrayPayload = value => Array.isArray(value) ? value : [value];\nvar shouldSubscribeByName = (name, signalName, exact) => !name || !signalName || name === signalName || convertToArrayPayload(name).some(currentName => currentName && (exact ? currentName === signalName : currentName.startsWith(signalName) || signalName.startsWith(currentName)));\nfunction useSubscribe(props) {\n  const _props = React.useRef(props);\n  _props.current = props;\n  React.useEffect(() => {\n    const subscription = !props.disabled && _props.current.subject && _props.current.subject.subscribe({\n      next: _props.current.next\n    });\n    return () => {\n      subscription && subscription.unsubscribe();\n    };\n  }, [props.disabled]);\n}\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    disabled,\n    name,\n    exact\n  } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _mounted = React.useRef(true);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  });\n  const _name = React.useRef(name);\n  _name.current = name;\n  useSubscribe({\n    disabled,\n    next: value => _mounted.current && shouldSubscribeByName(_name.current, value.name, exact) && shouldRenderFormState(value, _localProxyFormState.current, control._updateFormState) && updateFormState({\n      ...control._formState,\n      ...value\n    }),\n    subject: control._subjects.state\n  });\n  React.useEffect(() => {\n    _mounted.current = true;\n    _localProxyFormState.current.isValid && control._updateValid(true);\n    return () => {\n      _mounted.current = false;\n    };\n  }, [control]);\n  return getProxyFormState(formState, control, _localProxyFormState.current, false);\n}\nvar isString = value => typeof value === 'string';\nvar generateWatchOutput = (names, _names, formValues, isGlobal, defaultValue) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n  if (Array.isArray(names)) {\n    return names.map(fieldName => (isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)));\n  }\n  isGlobal && (_names.watchAll = true);\n  return formValues;\n};\n\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nfunction useWatch(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact\n  } = props || {};\n  const _name = React.useRef(name);\n  _name.current = name;\n  useSubscribe({\n    disabled,\n    subject: control._subjects.values,\n    next: formState => {\n      if (shouldSubscribeByName(_name.current, formState.name, exact)) {\n        updateValue(cloneObject(generateWatchOutput(_name.current, control._names, formState.values || control._formValues, false, defaultValue)));\n      }\n    }\n  });\n  const [value, updateValue] = React.useState(control._getWatch(name, defaultValue));\n  React.useEffect(() => control._removeUnmounted());\n  return value;\n}\nvar isKey = value => /^\\w*$/.test(value);\nvar stringToPath = input => compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\nvar set = (object, path, value) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue = isObject(objValue) || Array.isArray(objValue) ? objValue : !isNaN(+tempPath[index + 1]) ? [] : {};\n    }\n    object[key] = newValue;\n    object = object[key];\n  }\n  return object;\n};\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nfunction useController(props) {\n  const methods = useFormContext();\n  const {\n    name,\n    disabled,\n    control = methods.control,\n    shouldUnregister\n  } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(control._formValues, name, get(control._defaultValues, name, props.defaultValue)),\n    exact: true\n  });\n  const formState = useFormState({\n    control,\n    name\n  });\n  const _registerProps = React.useRef(control.register(name, {\n    ...props.rules,\n    value,\n    ...(isBoolean(props.disabled) ? {\n      disabled: props.disabled\n    } : {})\n  }));\n  React.useEffect(() => {\n    const _shouldUnregisterField = control._options.shouldUnregister || shouldUnregister;\n    const updateMounted = (name, value) => {\n      const field = get(control._fields, name);\n      if (field) {\n        field._f.mount = value;\n      }\n    };\n    updateMounted(name, true);\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n    return () => {\n      (isArrayField ? _shouldUnregisterField && !control._state.action : _shouldUnregisterField) ? control.unregister(name) : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n  React.useEffect(() => {\n    if (get(control._fields, name)) {\n      control._updateDisabledField({\n        disabled,\n        fields: control._fields,\n        name,\n        value: get(control._fields, name)._f.value\n      });\n    }\n  }, [disabled, name, control]);\n  return {\n    field: {\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled ? {\n        disabled: formState.disabled || disabled\n      } : {}),\n      onChange: React.useCallback(event => _registerProps.current.onChange({\n        target: {\n          value: getEventValue(event),\n          name: name\n        },\n        type: EVENTS.CHANGE\n      }), [name]),\n      onBlur: React.useCallback(() => _registerProps.current.onBlur({\n        target: {\n          value: get(control._formValues, name),\n          name: name\n        },\n        type: EVENTS.BLUR\n      }), [name, control]),\n      ref: elm => {\n        const field = get(control._fields, name);\n        if (field && elm) {\n          field._f.ref = {\n            focus: () => elm.focus(),\n            select: () => elm.select(),\n            setCustomValidity: message => elm.setCustomValidity(message),\n            reportValidity: () => elm.reportValidity()\n          };\n        }\n      }\n    },\n    formState,\n    fieldState: Object.defineProperties({}, {\n      invalid: {\n        enumerable: true,\n        get: () => !!get(formState.errors, name)\n      },\n      isDirty: {\n        enumerable: true,\n        get: () => !!get(formState.dirtyFields, name)\n      },\n      isTouched: {\n        enumerable: true,\n        get: () => !!get(formState.touchedFields, name)\n      },\n      isValidating: {\n        enumerable: true,\n        get: () => !!get(formState.validatingFields, name)\n      },\n      error: {\n        enumerable: true,\n        get: () => get(formState.errors, name)\n      }\n    })\n  };\n}\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = props => props.render(useController(props));\nconst POST_REQUEST = 'post';\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form(props) {\n  const methods = useFormContext();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n  const submit = async event => {\n    let hasError = false;\n    let type = '';\n    await control.handleSubmit(async data => {\n      const formData = new FormData();\n      let formDataJson = '';\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch (_a) {}\n      for (const name of control._names.mount) {\n        formData.append(name, get(data, name));\n      }\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson\n        });\n      }\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [headers && headers['Content-Type'], encType].some(value => value && value.includes('json'));\n          const response = await fetch(action, {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? {\n                'Content-Type': encType\n              } : {})\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData\n          });\n          if (response && (validateStatus ? !validateStatus(response.status) : response.status < 200 || response.status >= 300)) {\n            hasError = true;\n            onError && onError({\n              response\n            });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({\n              response\n            });\n          }\n        } catch (error) {\n          hasError = true;\n          onError && onError({\n            error\n          });\n        }\n      }\n    })(event);\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false\n      });\n      props.control.setError('root.server', {\n        type\n      });\n    }\n  };\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n  return render ? React.createElement(React.Fragment, null, render({\n    submit\n  })) : React.createElement(\"form\", {\n    noValidate: mounted,\n    action: action,\n    method: method,\n    encType: encType,\n    onSubmit: submit,\n    ...rest\n  }, children);\n}\nvar appendErrors = (name, validateAllFieldCriteria, errors, type, message) => validateAllFieldCriteria ? {\n  ...errors[name],\n  types: {\n    ...(errors[name] && errors[name].types ? errors[name].types : {}),\n    [type]: message || true\n  }\n} : {};\nvar generateId = () => {\n  const d = typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, c => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n    return (c == 'x' ? r : r & 0x3 | 0x8).toString(16);\n  });\n};\nvar getFocusFieldName = function (name, index) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  return options.shouldFocus || isUndefined(options.shouldFocus) ? options.focusName || \"\".concat(name, \".\").concat(isUndefined(options.focusIndex) ? index : options.focusIndex, \".\") : '';\n};\nvar getValidationModes = mode => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched\n});\nvar isWatched = (name, _names, isBlurEvent) => !isBlurEvent && (_names.watchAll || _names.watch.has(name) || [..._names.watch].some(watchName => name.startsWith(watchName) && /^\\.\\w+/.test(name.slice(watchName.length))));\nconst iterateFieldsByAction = (fields, action, fieldsNames, abortEarly) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n    if (field) {\n      const {\n        _f,\n        ...currentField\n      } = field;\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          break;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          break;\n        } else {\n          iterateFieldsByAction(currentField, action);\n        }\n      } else if (isObject(currentField)) {\n        iterateFieldsByAction(currentField, action);\n      }\n    }\n  }\n};\nvar updateFieldArrayRootError = (errors, error, name) => {\n  const fieldArrayErrors = compact(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\nvar isFileInput = element => element.type === 'file';\nvar isFunction = value => typeof value === 'function';\nvar isHTMLElement = value => {\n  if (!isWeb) {\n    return false;\n  }\n  const owner = value ? value.ownerDocument : 0;\n  return value instanceof (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement);\n};\nvar isMessage = value => isString(value);\nvar isRadioInput = element => element.type === 'radio';\nvar isRegex = value => value instanceof RegExp;\nconst defaultResult = {\n  value: false,\n  isValid: false\n};\nconst validResult = {\n  value: true,\n  isValid: true\n};\nvar getCheckboxValue = options => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options.filter(option => option && option.checked && !option.disabled).map(option => option.value);\n      return {\n        value: values,\n        isValid: !!values.length\n      };\n    }\n    return options[0].checked && !options[0].disabled ?\n    // @ts-expect-error expected to work in the browser\n    options[0].attributes && !isUndefined(options[0].attributes.value) ? isUndefined(options[0].value) || options[0].value === '' ? validResult : {\n      value: options[0].value,\n      isValid: true\n    } : validResult : defaultResult;\n  }\n  return defaultResult;\n};\nconst defaultReturn = {\n  isValid: false,\n  value: null\n};\nvar getRadioValue = options => Array.isArray(options) ? options.reduce((previous, option) => option && option.checked && !option.disabled ? {\n  isValid: true,\n  value: option.value\n} : previous, defaultReturn) : defaultReturn;\nfunction getValidateError(result, ref) {\n  let type = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 'validate';\n  if (isMessage(result) || Array.isArray(result) && result.every(isMessage) || isBoolean(result) && !result) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref\n    };\n  }\n}\nvar getValueAndMessage = validationData => isObject(validationData) && !isRegex(validationData) ? validationData : {\n  value: validationData,\n  message: ''\n};\nvar validateField = async (field, formValues, validateAllFieldCriteria, shouldUseNativeValidation, isFieldArray) => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n    disabled\n  } = field._f;\n  const inputValue = get(formValues, name);\n  if (!mount || disabled) {\n    return {};\n  }\n  const inputRef = refs ? refs[0] : ref;\n  const setCustomValidity = message => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty = (valueAsNumber || isFileInput(ref)) && isUndefined(ref.value) && isUndefined(inputValue) || isHTMLElement(ref) && ref.value === '' || inputValue === '' || Array.isArray(inputValue) && !inputValue.length;\n  const appendErrorsCurry = appendErrors.bind(null, name, validateAllFieldCriteria, error);\n  const getMinMaxMessage = function (exceedMax, maxLengthMessage, minLengthMessage) {\n    let maxType = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : INPUT_VALIDATION_RULES.maxLength;\n    let minType = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : INPUT_VALIDATION_RULES.minLength;\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message)\n    };\n  };\n  if (isFieldArray ? !Array.isArray(inputValue) || !inputValue.length : required && (!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue)) || isBoolean(inputValue) && !inputValue || isCheckBox && !getCheckboxValue(refs).isValid || isRadio && !getRadioValue(refs).isValid)) {\n    const {\n      value,\n      message\n    } = isMessage(required) ? {\n      value: !!required,\n      message: required\n    } : getValueAndMessage(required);\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message)\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue)) {\n      const valueNumber = ref.valueAsNumber || (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate = ref.valueAsDate || new Date(inputValue);\n      const convertTimeToDate = time => new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value) : isWeek ? inputValue > maxOutput.value : valueDate > new Date(maxOutput.value);\n      }\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value) : isWeek ? inputValue < minOutput.value : valueDate < new Date(minOutput.value);\n      }\n    }\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(!!exceedMax, maxOutput.message, minOutput.message, INPUT_VALIDATION_RULES.max, INPUT_VALIDATION_RULES.min);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if ((maxLength || minLength) && !isEmpty && (isString(inputValue) || isFieldArray && Array.isArray(inputValue))) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax = !isNullOrUndefined(maxLengthOutput.value) && inputValue.length > +maxLengthOutput.value;\n    const exceedMin = !isNullOrUndefined(minLengthOutput.value) && inputValue.length < +minLengthOutput.value;\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(exceedMax, maxLengthOutput.message, minLengthOutput.message);\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name].message);\n        return error;\n      }\n    }\n  }\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const {\n      value: patternValue,\n      message\n    } = getValueAndMessage(pattern);\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message)\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(INPUT_VALIDATION_RULES.validate, validateError.message)\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {};\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n        const validateError = getValidateError(await validate[key](inputValue, formValues), inputRef, key);\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message)\n          };\n          setCustomValidity(validateError.message);\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n  setCustomValidity(true);\n  return error;\n};\nvar appendAt = (data, value) => [...data, ...convertToArrayPayload(value)];\nvar fillEmptyArray = value => Array.isArray(value) ? value.map(() => undefined) : undefined;\nfunction insert(data, index, value) {\n  return [...data.slice(0, index), ...convertToArrayPayload(value), ...data.slice(index)];\n}\nvar moveArrayAt = (data, from, to) => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n  return data;\n};\nvar prependAt = (data, value) => [...convertToArrayPayload(value), ...convertToArrayPayload(data)];\nfunction removeAtIndexes(data, indexes) {\n  let i = 0;\n  const temp = [...data];\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n  return compact(temp).length ? temp : [];\n}\nvar removeArrayAt = (data, index) => isUndefined(index) ? [] : removeAtIndexes(data, convertToArrayPayload(index).sort((a, b) => a - b));\nvar swapArrayAt = (data, indexA, indexB) => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\nfunction baseGet(object, updatePath) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n  return object;\n}\nfunction isEmptyArray(obj) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\nfunction unset(object, path) {\n  const paths = Array.isArray(path) ? path : isKey(path) ? [path] : stringToPath(path);\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n  const index = paths.length - 1;\n  const key = paths[index];\n  if (childObject) {\n    delete childObject[key];\n  }\n  if (index !== 0 && (isObject(childObject) && isEmptyObject(childObject) || Array.isArray(childObject) && isEmptyArray(childObject))) {\n    unset(object, paths.slice(0, -1));\n  }\n  return object;\n}\nvar updateAt = (fieldValues, index, value) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFieldArray(props) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef(control._getFieldArray(name).map(generateId));\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n  props.rules && control.register(name, props.rules);\n  useSubscribe({\n    next: _ref => {\n      let {\n        values,\n        name: fieldArrayName\n      } = _ref;\n      if (fieldArrayName === _name.current || !fieldArrayName) {\n        const fieldValues = get(values, _name.current);\n        if (Array.isArray(fieldValues)) {\n          setFields(fieldValues);\n          ids.current = fieldValues.map(generateId);\n        }\n      }\n    },\n    subject: control._subjects.array\n  });\n  const updateValues = React.useCallback(updatedFieldArrayValues => {\n    _actioned.current = true;\n    control._updateFieldArray(name, updatedFieldArrayValues);\n  }, [control, name]);\n  const append = (value, options) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(control._getFieldArray(name), appendValue);\n    control._names.focus = getFocusFieldName(name, updatedFieldArrayValues.length - 1, options);\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const prepend = (value, options) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(control._getFieldArray(name), prependValue);\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value)\n    });\n  };\n  const remove = index => {\n    const updatedFieldArrayValues = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index\n    });\n  };\n  const insert$1 = (index, value, options) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insert(control._getFieldArray(name), index, insertValue);\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insert(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, insert, {\n      argA: index,\n      argB: fillEmptyArray(value)\n    });\n  };\n  const swap = (indexA, indexB) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, swapArrayAt, {\n      argA: indexA,\n      argB: indexB\n    }, false);\n  };\n  const move = (from, to) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, moveArrayAt, {\n      argA: from,\n      argB: to\n    }, false);\n  };\n  const update = (index, value) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(control._getFieldArray(name), index, updateValue);\n    ids.current = [...updatedFieldArrayValues].map((item, i) => !item || i === index ? generateId() : ids.current[i]);\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(name, updatedFieldArrayValues, updateAt, {\n      argA: index,\n      argB: updateValue\n    }, true, false);\n  };\n  const replace = value => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(name, [...updatedFieldArrayValues], data => data, {}, true, false);\n  };\n  React.useEffect(() => {\n    control._state.action = false;\n    isWatched(name, control._names) && control._subjects.state.next({\n      ...control._formState\n    });\n    if (_actioned.current && (!getValidationModes(control._options.mode).isOnSubmit || control._formState.isSubmitted)) {\n      if (control._options.resolver) {\n        control._executeSchema([name]).then(result => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n          if (existingError ? !error && existingError.type || error && (existingError.type !== error.type || existingError.message !== error.message) : error && error.type) {\n            error ? set(control._formState.errors, name, error) : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors\n            });\n          }\n        });\n      } else {\n        const field = get(control._fields, name);\n        if (field && field._f && !(getValidationModes(control._options.reValidateMode).isOnSubmit && getValidationModes(control._options.mode).isOnSubmit)) {\n          validateField(field, control._formValues, control._options.criteriaMode === VALIDATION_MODE.all, control._options.shouldUseNativeValidation, true).then(error => !isEmptyObject(error) && control._subjects.state.next({\n            errors: updateFieldArrayRootError(control._formState.errors, error, name)\n          }));\n        }\n      }\n    }\n    control._subjects.values.next({\n      name,\n      values: {\n        ...control._formValues\n      }\n    });\n    control._names.focus && iterateFieldsByAction(control._fields, (ref, key) => {\n      if (control._names.focus && key.startsWith(control._names.focus) && ref.focus) {\n        ref.focus();\n        return 1;\n      }\n      return;\n    });\n    control._names.focus = '';\n    control._updateValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._updateFieldArray(name);\n    return () => {\n      (control._options.shouldUnregister || shouldUnregister) && control.unregister(name);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert$1, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(() => fields.map((field, index) => ({\n      ...field,\n      [keyName]: ids.current[index] || generateId()\n    })), [fields, keyName])\n  };\n}\nvar createSubject = () => {\n  let _observers = [];\n  const next = value => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n  const subscribe = observer => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter(o => o !== observer);\n      }\n    };\n  };\n  const unsubscribe = () => {\n    _observers = [];\n  };\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe\n  };\n};\nvar isPrimitive = value => isNullOrUndefined(value) || !isObjectType(value);\nfunction deepEqual(object1, object2) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n  for (const key of keys1) {\n    const val1 = object1[key];\n    if (!keys2.includes(key)) {\n      return false;\n    }\n    if (key !== 'ref') {\n      const val2 = object2[key];\n      if (isDateObject(val1) && isDateObject(val2) || isObject(val1) && isObject(val2) || Array.isArray(val1) && Array.isArray(val2) ? !deepEqual(val1, val2) : val1 !== val2) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\nvar isMultipleSelect = element => element.type === \"select-multiple\";\nvar isRadioOrCheckbox = ref => isRadioInput(ref) || isCheckBoxInput(ref);\nvar live = ref => isHTMLElement(ref) && ref.isConnected;\nvar objectHasTruthyValue = value => isObject(value) && Object.values(value).some(val => val);\nvar objectHasFunction = data => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\nfunction markFieldsDirty(data) {\n  let fields = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n  return fields;\n}\nfunction getDirtyFieldsFromDefaultValues(data, formValues, dirtyFieldsFromValues) {\n  const isParentNodeArray = Array.isArray(data);\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (Array.isArray(data[key]) || isObject(data[key]) && !objectHasFunction(data[key])) {\n        if (isUndefined(formValues) || isPrimitive(dirtyFieldsFromValues[key])) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key]) ? markFieldsDirty(data[key], []) : {\n            ...markFieldsDirty(data[key])\n          };\n        } else {\n          getDirtyFieldsFromDefaultValues(data[key], isNullOrUndefined(formValues) ? {} : formValues[key], dirtyFieldsFromValues[key]);\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n  return dirtyFieldsFromValues;\n}\nvar getDirtyFields = (defaultValues, formValues) => getDirtyFieldsFromDefaultValues(defaultValues, formValues, markFieldsDirty(formValues));\nvar getFieldValueAs = (value, _ref2) => {\n  let {\n    valueAsNumber,\n    valueAsDate,\n    setValueAs\n  } = _ref2;\n  return isUndefined(value) ? value : valueAsNumber ? value === '' ? NaN : value ? +value : value : valueAsDate && isString(value) ? new Date(value) : setValueAs ? setValueAs(value) : value;\n};\nfunction getFieldValue(_f) {\n  const ref = _f.ref;\n  if (_f.refs ? _f.refs.every(ref => ref.disabled) : ref.disabled) {\n    return;\n  }\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(_ref3 => {\n      let {\n        value\n      } = _ref3;\n      return value;\n    });\n  }\n  if (isCheckBoxInput(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\nvar getResolverOptions = (fieldsNames, _fields, criteriaMode, shouldUseNativeValidation) => {\n  const fields = {};\n  for (const name of fieldsNames) {\n    const field = get(_fields, name);\n    field && set(fields, name, field._f);\n  }\n  return {\n    criteriaMode,\n    names: [...fieldsNames],\n    fields,\n    shouldUseNativeValidation\n  };\n};\nvar getRuleValue = rule => isUndefined(rule) ? rule : isRegex(rule) ? rule.source : isObject(rule) ? isRegex(rule.value) ? rule.value.source : rule.value : rule;\nvar hasValidation = options => options.mount && (options.required || options.min || options.max || options.maxLength || options.minLength || options.pattern || options.validate);\nfunction schemaErrorLookup(errors, _fields, name) {\n  const error = get(errors, name);\n  if (error || isKey(name)) {\n    return {\n      error,\n      name\n    };\n  }\n  const names = name.split('.');\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return {\n        name\n      };\n    }\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError\n      };\n    }\n    names.pop();\n  }\n  return {\n    name\n  };\n}\nvar skipValidation = (isBlurEvent, isTouched, isSubmitted, reValidateMode, mode) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\nvar unsetEmptyArray = (ref, name) => !compact(get(ref, name)).length && unset(ref, name);\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true\n};\nfunction createFormControl() {\n  let props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  let _options = {\n    ...defaultOptions,\n    ...props\n  };\n  let _formState = {\n    submitCount: 0,\n    isDirty: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false\n  };\n  let _fields = {};\n  let _defaultValues = isObject(_options.defaultValues) || isObject(_options.values) ? cloneObject(_options.defaultValues || _options.values) || {} : {};\n  let _formValues = _options.shouldUnregister ? {} : cloneObject(_defaultValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false\n  };\n  let _names = {\n    mount: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set()\n  };\n  let delayErrorCallback;\n  let timer = 0;\n  const _proxyFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false\n  };\n  const _subjects = {\n    values: createSubject(),\n    array: createSubject(),\n    state: createSubject()\n  };\n  const validationModeBeforeSubmit = getValidationModes(_options.mode);\n  const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n  const shouldDisplayAllAssociatedErrors = _options.criteriaMode === VALIDATION_MODE.all;\n  const debounce = callback => wait => {\n    clearTimeout(timer);\n    timer = setTimeout(callback, wait);\n  };\n  const _updateValid = async shouldUpdateValid => {\n    if (_proxyFormState.isValid || shouldUpdateValid) {\n      const isValid = _options.resolver ? isEmptyObject((await _executeSchema()).errors) : await executeBuiltInValidation(_fields, true);\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid\n        });\n      }\n    }\n  };\n  const _updateIsValidating = (isValidating, names) => {\n    if (!(_proxyFormState.isValidating || _proxyFormState.validatingFields)) {\n      return;\n    }\n    names.forEach(name => {\n      set(_formState.validatingFields, name, isValidating);\n    });\n    _formState.isValidating = objectHasTruthyValue(_formState.validatingFields);\n    _subjects.state.next({\n      validatingFields: _formState.validatingFields,\n      isValidating: _formState.isValidating\n    });\n  };\n  const _updateFieldArray = function (name) {\n    let values = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    let method = arguments.length > 2 ? arguments[2] : undefined;\n    let args = arguments.length > 3 ? arguments[3] : undefined;\n    let shouldSetValues = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : true;\n    let shouldUpdateFieldsAndState = arguments.length > 5 && arguments[5] !== undefined ? arguments[5] : true;\n    if (args && method) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_formState.errors, name))) {\n        const errors = method(get(_formState.errors, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n      if (_proxyFormState.touchedFields && shouldUpdateFieldsAndState && Array.isArray(get(_formState.touchedFields, name))) {\n        const touchedFields = method(get(_formState.touchedFields, name), args.argA, args.argB);\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n      if (_proxyFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n  const updateErrors = (name, error) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors\n    });\n  };\n  const _setErrors = errors => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false\n    });\n  };\n  const updateValidAndValue = (name, shouldSkipSetValueAs, value, ref) => {\n    const field = get(_fields, name);\n    if (field) {\n      const defaultValue = get(_formValues, name, isUndefined(value) ? get(_defaultValues, name) : value);\n      isUndefined(defaultValue) || ref && ref.defaultChecked || shouldSkipSetValueAs ? set(_formValues, name, shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f)) : setFieldValue(name, defaultValue);\n      _state.mount && _updateValid();\n    }\n  };\n  const updateTouchAndDirty = (name, fieldValue, isBlurEvent, shouldDirty, shouldRender) => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output = {\n      name\n    };\n    const disabledField = !!(get(_fields, name) && get(_fields, name)._f.disabled);\n    if (!isBlurEvent || shouldDirty) {\n      if (_proxyFormState.isDirty) {\n        isPreviousDirty = _formState.isDirty;\n        _formState.isDirty = output.isDirty = _getDirty();\n        shouldUpdateField = isPreviousDirty !== output.isDirty;\n      }\n      const isCurrentFieldPristine = disabledField || deepEqual(get(_defaultValues, name), fieldValue);\n      isPreviousDirty = !!(!disabledField && get(_formState.dirtyFields, name));\n      isCurrentFieldPristine || disabledField ? unset(_formState.dirtyFields, name) : set(_formState.dirtyFields, name, true);\n      output.dirtyFields = _formState.dirtyFields;\n      shouldUpdateField = shouldUpdateField || _proxyFormState.dirtyFields && isPreviousDirty !== !isCurrentFieldPristine;\n    }\n    if (isBlurEvent) {\n      const isPreviousFieldTouched = get(_formState.touchedFields, name);\n      if (!isPreviousFieldTouched) {\n        set(_formState.touchedFields, name, isBlurEvent);\n        output.touchedFields = _formState.touchedFields;\n        shouldUpdateField = shouldUpdateField || _proxyFormState.touchedFields && isPreviousFieldTouched !== isBlurEvent;\n      }\n    }\n    shouldUpdateField && shouldRender && _subjects.state.next(output);\n    return shouldUpdateField ? output : {};\n  };\n  const shouldRenderByError = (name, isValid, error, fieldState) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid = _proxyFormState.isValid && isBoolean(isValid) && _formState.isValid !== isValid;\n    if (props.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(props.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n    }\n    if ((error ? !deepEqual(previousFieldError, error) : previousFieldError) || !isEmptyObject(fieldState) || shouldUpdateValid) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? {\n          isValid\n        } : {}),\n        errors: _formState.errors,\n        name\n      };\n      _formState = {\n        ..._formState,\n        ...updatedFormState\n      };\n      _subjects.state.next(updatedFormState);\n    }\n    _updateIsValidating(false, Object.keys(_formState.validatingFields).filter(key => key === name));\n  };\n  const _executeSchema = async name => _options.resolver(_formValues, _options.context, getResolverOptions(name || _names.mount, _fields, _options.criteriaMode, _options.shouldUseNativeValidation));\n  const executeSchemaAndUpdateState = async names => {\n    const {\n      errors\n    } = await _executeSchema(names);\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error ? set(_formState.errors, name, error) : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n    return errors;\n  };\n  const executeBuiltInValidation = async function (fields, shouldOnlyCheckValid) {\n    let context = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {\n      valid: true\n    };\n    for (const name in fields) {\n      const field = fields[name];\n      if (field) {\n        const {\n          _f,\n          ...fieldValue\n        } = field;\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const fieldError = await validateField(field, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation && !shouldOnlyCheckValid, isFieldArrayRoot);\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n          !shouldOnlyCheckValid && (get(fieldError, _f.name) ? isFieldArrayRoot ? updateFieldArrayRootError(_formState.errors, fieldError, _f.name) : set(_formState.errors, _f.name, fieldError[_f.name]) : unset(_formState.errors, _f.name));\n        }\n        fieldValue && (await executeBuiltInValidation(fieldValue, shouldOnlyCheckValid, context));\n      }\n    }\n    return context.valid;\n  };\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field = get(_fields, name);\n      field && (field._f.refs ? field._f.refs.every(ref => !live(ref)) : !live(field._f.ref)) && unregister(name);\n    }\n    _names.unMount = new Set();\n  };\n  const _getDirty = (name, data) => (name && data && set(_formValues, name, data), !deepEqual(getValues(), _defaultValues));\n  const _getWatch = (names, defaultValue, isGlobal) => generateWatchOutput(names, _names, {\n    ...(_state.mount ? _formValues : isUndefined(defaultValue) ? _defaultValues : isString(names) ? {\n      [names]: defaultValue\n    } : defaultValue)\n  }, isGlobal, defaultValue);\n  const _getFieldArray = name => compact(get(_state.mount ? _formValues : _defaultValues, name, props.shouldUnregister ? get(_defaultValues, name, []) : []));\n  const setFieldValue = function (name, value) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const field = get(_fields, name);\n    let fieldValue = value;\n    if (field) {\n      const fieldReference = field._f;\n      if (fieldReference) {\n        !fieldReference.disabled && set(_formValues, name, getFieldValueAs(value, fieldReference));\n        fieldValue = isHTMLElement(fieldReference.ref) && isNullOrUndefined(value) ? '' : value;\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(optionRef => optionRef.selected = fieldValue.includes(optionRef.value));\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.length > 1 ? fieldReference.refs.forEach(checkboxRef => (!checkboxRef.defaultChecked || !checkboxRef.disabled) && (checkboxRef.checked = Array.isArray(fieldValue) ? !!fieldValue.find(data => data === checkboxRef.value) : fieldValue === checkboxRef.value)) : fieldReference.refs[0] && (fieldReference.refs[0].checked = !!fieldValue);\n          } else {\n            fieldReference.refs.forEach(radioRef => radioRef.checked = radioRef.value === fieldValue);\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n          if (!fieldReference.ref.type) {\n            _subjects.values.next({\n              name,\n              values: {\n                ..._formValues\n              }\n            });\n          }\n        }\n      }\n    }\n    (options.shouldDirty || options.shouldTouch) && updateTouchAndDirty(name, fieldValue, options.shouldTouch, options.shouldDirty, true);\n    options.shouldValidate && trigger(name);\n  };\n  const setValues = (name, value, options) => {\n    for (const fieldKey in value) {\n      const fieldValue = value[fieldKey];\n      const fieldName = \"\".concat(name, \".\").concat(fieldKey);\n      const field = get(_fields, fieldName);\n      (_names.array.has(name) || !isPrimitive(fieldValue) || field && !field._f) && !isDateObject(fieldValue) ? setValues(fieldName, fieldValue, options) : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n  const setValue = function (name, value) {\n    let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n    set(_formValues, name, cloneValue);\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: {\n          ..._formValues\n        }\n      });\n      if ((_proxyFormState.isDirty || _proxyFormState.dirtyFields) && options.shouldDirty) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue)\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue) ? setValues(name, cloneValue, options) : setFieldValue(name, cloneValue, options);\n    }\n    isWatched(name, _names) && _subjects.state.next({\n      ..._formState\n    });\n    _subjects.values.next({\n      name: _state.mount ? name : undefined,\n      values: {\n        ..._formValues\n      }\n    });\n  };\n  const onChange = async event => {\n    const target = event.target;\n    let name = target.name;\n    let isFieldValueUpdated = true;\n    const field = get(_fields, name);\n    const getCurrentFieldValue = () => target.type ? getFieldValue(field._f) : getEventValue(event);\n    const _updateIsFieldValueUpdated = fieldValue => {\n      isFieldValueUpdated = Number.isNaN(fieldValue) || fieldValue === get(_formValues, name, fieldValue);\n    };\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = getCurrentFieldValue();\n      const isBlurEvent = event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation = !hasValidation(field._f) && !_options.resolver && !get(_formState.errors, name) && !field._f.deps || skipValidation(isBlurEvent, get(_formState.touchedFields, name), _formState.isSubmitted, validationModeAfterSubmit, validationModeBeforeSubmit);\n      const watched = isWatched(name, _names, isBlurEvent);\n      set(_formValues, name, fieldValue);\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n      const fieldState = updateTouchAndDirty(name, fieldValue, isBlurEvent, false);\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n      !isBlurEvent && _subjects.values.next({\n        name,\n        type: event.type,\n        values: {\n          ..._formValues\n        }\n      });\n      if (shouldSkipValidation) {\n        _proxyFormState.isValid && _updateValid();\n        return shouldRender && _subjects.state.next({\n          name,\n          ...(watched ? {} : fieldState)\n        });\n      }\n      !isBlurEvent && watched && _subjects.state.next({\n        ..._formState\n      });\n      _updateIsValidating(true, [name]);\n      if (_options.resolver) {\n        const {\n          errors\n        } = await _executeSchema([name]);\n        _updateIsFieldValueUpdated(fieldValue);\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(_formState.errors, _fields, name);\n          const errorLookupResult = schemaErrorLookup(errors, _fields, previousErrorLookupResult.name || name);\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        error = (await validateField(field, _formValues, shouldDisplayAllAssociatedErrors, _options.shouldUseNativeValidation))[name];\n        _updateIsFieldValueUpdated(fieldValue);\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (_proxyFormState.isValid) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n      if (isFieldValueUpdated) {\n        field._f.deps && trigger(field._f.deps);\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n  const _focusInput = (ref, key) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n  const trigger = async function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name);\n    _updateIsValidating(true, fieldNames);\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(isUndefined(name) ? name : fieldNames);\n      isValid = isEmptyObject(errors);\n      validationResult = name ? !fieldNames.some(name => get(errors, name)) : isValid;\n    } else if (name) {\n      validationResult = (await Promise.all(fieldNames.map(async fieldName => {\n        const field = get(_fields, fieldName);\n        return await executeBuiltInValidation(field && field._f ? {\n          [fieldName]: field\n        } : field);\n      }))).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _updateValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n    _subjects.state.next({\n      ...(!isString(name) || _proxyFormState.isValid && isValid !== _formState.isValid ? {} : {\n        name\n      }),\n      ...(_options.resolver || !name ? {\n        isValid\n      } : {}),\n      errors: _formState.errors,\n      isValidating: false\n    });\n    options.shouldFocus && !validationResult && iterateFieldsByAction(_fields, _focusInput, name ? fieldNames : _names.mount);\n    return validationResult;\n  };\n  const getValues = fieldNames => {\n    const values = {\n      ..._defaultValues,\n      ...(_state.mount ? _formValues : {})\n    };\n    return isUndefined(fieldNames) ? values : isString(fieldNames) ? get(values, fieldNames) : fieldNames.map(name => get(values, name));\n  };\n  const getFieldState = (name, formState) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n    isValidating: !!get((formState || _formState).validatingFields, name),\n    error: get((formState || _formState).errors, name)\n  });\n  const clearErrors = name => {\n    name && convertToArrayPayload(name).forEach(inputName => unset(_formState.errors, inputName));\n    _subjects.state.next({\n      errors: name ? _formState.errors : {}\n    });\n  };\n  const setError = (name, error, options) => {\n    const ref = (get(_fields, name, {\n      _f: {}\n    })._f || {}).ref;\n    set(_formState.errors, name, {\n      ...error,\n      ref\n    });\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false\n    });\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n  const watch = (name, defaultValue) => isFunction(name) ? _subjects.values.subscribe({\n    next: payload => name(_getWatch(undefined, defaultValue), payload)\n  }) : _getWatch(name, defaultValue, true);\n  const unregister = function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating && unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister && !options.keepDefaultValue && unset(_defaultValues, fieldName);\n    }\n    _subjects.values.next({\n      values: {\n        ..._formValues\n      }\n    });\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : {\n        isDirty: _getDirty()\n      })\n    });\n    !options.keepIsValid && _updateValid();\n  };\n  const _updateDisabledField = _ref4 => {\n    let {\n      disabled,\n      name,\n      field,\n      fields,\n      value\n    } = _ref4;\n    if (isBoolean(disabled)) {\n      const inputValue = disabled ? undefined : isUndefined(value) ? getFieldValue(field ? field._f : get(fields, name)._f) : value;\n      set(_formValues, name, inputValue);\n      updateTouchAndDirty(name, inputValue, false, false, true);\n    }\n  };\n  const register = function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    let field = get(_fields, name);\n    const disabledIsDefined = isBoolean(options.disabled);\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : {\n          ref: {\n            name\n          }\n        }),\n        name,\n        mount: true,\n        ...options\n      }\n    });\n    _names.mount.add(name);\n    if (field) {\n      _updateDisabledField({\n        field,\n        disabled: options.disabled,\n        name,\n        value: options.value\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n    return {\n      ...(disabledIsDefined ? {\n        disabled: options.disabled\n      } : {}),\n      ...(_options.progressive ? {\n        required: !!options.required,\n        min: getRuleValue(options.min),\n        max: getRuleValue(options.max),\n        minLength: getRuleValue(options.minLength),\n        maxLength: getRuleValue(options.maxLength),\n        pattern: getRuleValue(options.pattern)\n      } : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: ref => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n          const fieldRef = isUndefined(ref.value) ? ref.querySelectorAll ? ref.querySelectorAll('input,select,textarea')[0] || ref : ref : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n          if (radioOrCheckbox ? refs.find(option => option === fieldRef) : fieldRef === field._f.ref) {\n            return;\n          }\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox ? {\n                refs: [...refs.filter(live), fieldRef, ...(Array.isArray(get(_defaultValues, name)) ? [{}] : [])],\n                ref: {\n                  type: fieldRef.type,\n                  name\n                }\n              } : {\n                ref: fieldRef\n              })\n            }\n          });\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n          if (field._f) {\n            field._f.mount = false;\n          }\n          (_options.shouldUnregister || options.shouldUnregister) && !(isNameInFieldArray(_names.array, name) && _state.action) && _names.unMount.add(name);\n        }\n      }\n    };\n  };\n  const _focusError = () => _options.shouldFocusError && iterateFieldsByAction(_fields, _focusInput, _names.mount);\n  const _disableForm = disabled => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({\n        disabled\n      });\n      iterateFieldsByAction(_fields, (ref, name) => {\n        let requiredDisabledState = disabled;\n        const currentField = get(_fields, name);\n        if (currentField && isBoolean(currentField._f.disabled)) {\n          requiredDisabledState || (requiredDisabledState = currentField._f.disabled);\n        }\n        ref.disabled = requiredDisabledState;\n      }, 0, false);\n    }\n  };\n  const handleSubmit = (onValid, onInvalid) => async e => {\n    let onValidError = undefined;\n    if (e) {\n      e.preventDefault && e.preventDefault();\n      e.persist && e.persist();\n    }\n    let fieldValues = cloneObject(_formValues);\n    _subjects.state.next({\n      isSubmitting: true\n    });\n    if (_options.resolver) {\n      const {\n        errors,\n        values\n      } = await _executeSchema();\n      _formState.errors = errors;\n      fieldValues = values;\n    } else {\n      await executeBuiltInValidation(_fields);\n    }\n    unset(_formState.errors, 'root');\n    if (isEmptyObject(_formState.errors)) {\n      _subjects.state.next({\n        errors: {}\n      });\n      try {\n        await onValid(fieldValues, e);\n      } catch (error) {\n        onValidError = error;\n      }\n    } else {\n      if (onInvalid) {\n        await onInvalid({\n          ..._formState.errors\n        }, e);\n      }\n      _focusError();\n      setTimeout(_focusError);\n    }\n    _subjects.state.next({\n      isSubmitted: true,\n      isSubmitting: false,\n      isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n      submitCount: _formState.submitCount + 1,\n      errors: _formState.errors\n    });\n    if (onValidError) {\n      throw onValidError;\n    }\n  };\n  const resetField = function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(name, options.defaultValue);\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue ? _getDirty(name, cloneObject(get(_defaultValues, name))) : _getDirty();\n      }\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _updateValid();\n      }\n      _subjects.state.next({\n        ..._formState\n      });\n    }\n  };\n  const _reset = function (formValues) {\n    let keepStateOptions = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        for (const fieldName of _names.mount) {\n          get(_formState.dirtyFields, fieldName) ? set(values, fieldName, get(_formValues, fieldName)) : setValue(fieldName, get(values, fieldName));\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs) ? field._f.refs[0] : field._f.ref;\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n        _fields = {};\n      }\n      _formValues = props.shouldUnregister ? keepStateOptions.keepDefaultValues ? cloneObject(_defaultValues) : {} : cloneObject(values);\n      _subjects.array.next({\n        values: {\n          ...values\n        }\n      });\n      _subjects.values.next({\n        values: {\n          ...values\n        }\n      });\n    }\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: ''\n    };\n    _state.mount = !_proxyFormState.isValid || !!keepStateOptions.keepIsValid || !!keepStateOptions.keepDirtyValues;\n    _state.watch = !!props.shouldUnregister;\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount ? _formState.submitCount : 0,\n      isDirty: isEmptyResetValues ? false : keepStateOptions.keepDirty ? _formState.isDirty : !!(keepStateOptions.keepDefaultValues && !deepEqual(formValues, _defaultValues)),\n      isSubmitted: keepStateOptions.keepIsSubmitted ? _formState.isSubmitted : false,\n      dirtyFields: isEmptyResetValues ? [] : keepStateOptions.keepDirtyValues ? keepStateOptions.keepDefaultValues && _formValues ? getDirtyFields(_defaultValues, _formValues) : _formState.dirtyFields : keepStateOptions.keepDefaultValues && formValues ? getDirtyFields(_defaultValues, formValues) : {},\n      touchedFields: keepStateOptions.keepTouched ? _formState.touchedFields : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful ? _formState.isSubmitSuccessful : false,\n      isSubmitting: false\n    });\n  };\n  const reset = (formValues, keepStateOptions) => _reset(isFunction(formValues) ? formValues(_formValues) : formValues, keepStateOptions);\n  const setFocus = function (name) {\n    let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs ? fieldReference.refs[0] : fieldReference.ref;\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect && fieldRef.select();\n      }\n    }\n  };\n  const _updateFormState = updatedFormState => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState\n    };\n  };\n  const _resetDefaultValues = () => isFunction(_options.defaultValues) && _options.defaultValues().then(values => {\n    reset(values, _options.resetOptions);\n    _subjects.state.next({\n      isLoading: false\n    });\n  });\n  return {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _executeSchema,\n      _getWatch,\n      _getDirty,\n      _updateValid,\n      _removeUnmounted,\n      _updateFieldArray,\n      _updateDisabledField,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _updateFormState,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      _setErrors,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      set _formState(value) {\n        _formState = value;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value\n        };\n      }\n    },\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState\n  };\n}\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useForm() {\n  let props = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const _formControl = React.useRef();\n  const _values = React.useRef();\n  const [formState, updateFormState] = React.useState({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    defaultValues: isFunction(props.defaultValues) ? undefined : props.defaultValues\n  });\n  if (!_formControl.current) {\n    _formControl.current = {\n      ...createFormControl(props),\n      formState\n    };\n  }\n  const control = _formControl.current.control;\n  control._options = props;\n  useSubscribe({\n    subject: control._subjects.state,\n    next: value => {\n      if (shouldRenderFormState(value, control._proxyFormState, control._updateFormState, true)) {\n        updateFormState({\n          ...control._formState\n        });\n      }\n    }\n  });\n  React.useEffect(() => control._disableForm(props.disabled), [control, props.disabled]);\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState(state => ({\n        ...state\n      }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [props.values, control]);\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n    }\n  }, [props.errors, control]);\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._updateValid();\n      control._state.mount = true;\n    }\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({\n        ...control._formState\n      });\n    }\n    control._removeUnmounted();\n  });\n  React.useEffect(() => {\n    props.shouldUnregister && control._subjects.values.next({\n      values: control._getWatch()\n    });\n  }, [props.shouldUnregister, control]);\n  _formControl.current.formState = getProxyFormState(formState, control);\n  return _formControl.current;\n}\nexport { Controller, Form, FormProvider, appendErrors, get, set, useController, useFieldArray, useForm, useFormContext, useFormState, useWatch };", "map": {"version": 3, "names": ["isCheckBoxInput", "element", "type", "isDateObject", "value", "Date", "isNullOrUndefined", "isObjectType", "isObject", "Array", "isArray", "getEventValue", "event", "target", "checked", "getNodeParentName", "name", "substring", "search", "isNameInFieldArray", "names", "has", "isPlainObject", "tempObject", "prototypeCopy", "constructor", "prototype", "hasOwnProperty", "isWeb", "window", "HTMLElement", "document", "cloneObject", "data", "copy", "Set", "Blob", "FileList", "key", "compact", "filter", "Boolean", "isUndefined", "val", "undefined", "get", "object", "path", "defaultValue", "result", "split", "reduce", "isBoolean", "EVENTS", "BLUR", "FOCUS_OUT", "CHANGE", "VALIDATION_MODE", "onBlur", "onChange", "onSubmit", "onTouched", "all", "INPUT_VALIDATION_RULES", "max", "min", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "pattern", "required", "validate", "HookFormContext", "React", "createContext", "useFormContext", "useContext", "FormProvider", "props", "children", "createElement", "Provider", "getProxyFormState", "formState", "control", "localProxyFormState", "isRoot", "arguments", "length", "defaultValues", "_defaultValues", "Object", "defineProperty", "_key", "_proxyFormState", "isEmptyObject", "keys", "shouldRenderFormState", "formStateData", "updateFormState", "find", "convertToArrayPayload", "shouldSubscribeByName", "signalName", "exact", "some", "currentName", "startsWith", "useSubscribe", "_props", "useRef", "current", "useEffect", "subscription", "disabled", "subject", "subscribe", "next", "unsubscribe", "useFormState", "methods", "useState", "_formState", "_mounted", "_localProxyFormState", "isDirty", "isLoading", "dirtyFields", "touchedFields", "validatingFields", "isValidating", "<PERSON><PERSON><PERSON><PERSON>", "errors", "_name", "_updateFormState", "_subjects", "state", "_updateValid", "isString", "generateWatchOutput", "_names", "formValues", "isGlobal", "watch", "add", "map", "fieldName", "watchAll", "useWatch", "values", "updateValue", "_formValues", "_getWatch", "_removeUnmounted", "is<PERSON>ey", "test", "stringToPath", "input", "replace", "set", "index", "temp<PERSON>ath", "lastIndex", "newValue", "objValue", "isNaN", "useController", "shouldUnregister", "isArrayField", "array", "_registerProps", "register", "rules", "_shouldUnregisterField", "_options", "updateMounted", "field", "_fields", "_f", "mount", "_state", "action", "unregister", "_updateDisabledField", "fields", "useCallback", "ref", "elm", "focus", "select", "setCustomValidity", "message", "reportValidity", "fieldState", "defineProperties", "invalid", "enumerable", "isTouched", "error", "Controller", "render", "POST_REQUEST", "Form", "mounted", "setMounted", "method", "headers", "encType", "onError", "onSuccess", "validateStatus", "rest", "submit", "<PERSON><PERSON><PERSON><PERSON>", "handleSubmit", "formData", "FormData", "formDataJson", "JSON", "stringify", "_a", "append", "shouldStringifySubmissionData", "includes", "response", "fetch", "body", "status", "String", "isSubmitSuccessful", "setError", "Fragment", "noValidate", "appendErrors", "validateAllFieldCriteria", "types", "generateId", "d", "performance", "now", "c", "r", "Math", "random", "toString", "getFocusFieldName", "options", "shouldFocus", "focusName", "concat", "focusIndex", "getValidationModes", "mode", "isOnSubmit", "isOnBlur", "isOnChange", "isOnAll", "isOnTouch", "isWatched", "isBlurEvent", "watchName", "slice", "iterateFieldsByAction", "fieldsNames", "abort<PERSON><PERSON><PERSON>", "current<PERSON><PERSON>", "refs", "updateFieldArrayRootError", "fieldArrayErrors", "isFileInput", "isFunction", "isHTMLElement", "owner", "ownerDocument", "defaultView", "isMessage", "isRadioInput", "isRegex", "RegExp", "defaultResult", "validResult", "getCheckboxValue", "option", "attributes", "defaultReturn", "getRadioValue", "previous", "getValidateError", "every", "getValueAndMessage", "validationData", "validateField", "shouldUseNativeValidation", "isFieldArray", "valueAsNumber", "inputValue", "inputRef", "isRadio", "isCheckBox", "isRadioOrCheckbox", "isEmpty", "appendErrors<PERSON><PERSON><PERSON>", "bind", "getMinMaxMessage", "exceedMax", "maxLengthMessage", "minLengthMessage", "maxType", "minType", "exceedMin", "maxOutput", "minOutput", "valueNumber", "valueDate", "valueAsDate", "convertTimeToDate", "time", "toDateString", "isTime", "isWeek", "maxLengthOutput", "minLengthOutput", "patternValue", "match", "validateError", "validationResult", "appendAt", "fillEmptyArray", "insert", "moveArrayAt", "from", "to", "splice", "prependAt", "removeAtIndexes", "indexes", "i", "temp", "removeArrayAt", "sort", "a", "b", "swapArrayAt", "indexA", "indexB", "baseGet", "updatePath", "isEmptyArray", "obj", "unset", "paths", "childObject", "updateAt", "field<PERSON><PERSON><PERSON>", "useFieldArray", "keyName", "setFields", "_getFieldArray", "ids", "_fieldIds", "_actioned", "_ref", "fieldArrayName", "updateValues", "updatedFieldArrayValues", "_updateFieldArray", "appendValue", "argA", "prepend", "prependValue", "remove", "insert$1", "insertValue", "argB", "swap", "move", "update", "item", "isSubmitted", "resolver", "_executeSchema", "then", "existingError", "reValidateMode", "criteriaMode", "useMemo", "createSubject", "_observers", "observer", "push", "o", "observers", "isPrimitive", "deepEqual", "object1", "object2", "getTime", "keys1", "keys2", "val1", "val2", "isMultipleSelect", "live", "isConnected", "objectHasTruthyValue", "objectHasFunction", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "isParentNodeArray", "getDirtyFieldsFromDefaultValues", "dirtyField<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getDirty<PERSON>ields", "getFieldValueAs", "_ref2", "setValueAs", "NaN", "getFieldValue", "files", "selectedOptions", "_ref3", "getResolverOptions", "getRuleValue", "rule", "source", "hasValidation", "schemaErrorLookup", "join", "found<PERSON><PERSON>r", "pop", "skipValidation", "unsetEmptyArray", "defaultOptions", "shouldFocusError", "createFormControl", "submitCount", "isSubmitting", "unMount", "delayError<PERSON><PERSON><PERSON>", "timer", "validationModeBeforeSubmit", "validationModeAfterSubmit", "shouldDisplayAllAssociatedErrors", "debounce", "callback", "wait", "clearTimeout", "setTimeout", "shouldUpdateValid", "executeBuiltInValidation", "_updateIsValidating", "for<PERSON>ach", "args", "shouldSetValues", "shouldUpdateFieldsAndState", "_getDirty", "updateErrors", "_setErrors", "updateValidAndValue", "shouldSkipSetValueAs", "defaultChecked", "setFieldValue", "updateTouchAndDirty", "fieldValue", "should<PERSON>irty", "shouldRender", "shouldUpdateField", "is<PERSON>revious<PERSON><PERSON>y", "output", "<PERSON><PERSON><PERSON>", "isCurrentFieldPristine", "isPreviousFieldTouched", "shouldRenderByError", "previousFieldError", "delayError", "updatedFormState", "context", "executeSchemaAndUpdateState", "should<PERSON>nly<PERSON><PERSON><PERSON><PERSON>d", "valid", "isFieldArrayRoot", "fieldError", "getV<PERSON>ues", "fieldReference", "optionRef", "selected", "checkboxRef", "radioRef", "shouldTouch", "shouldValidate", "trigger", "set<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "setValue", "cloneValue", "isFieldValueUpdated", "getCurrentFieldValue", "_updateIsFieldValueUpdated", "Number", "shouldSkipValidation", "deps", "watched", "previousErrorLookupResult", "errorLookupResult", "_focusInput", "fieldNames", "Promise", "getFieldState", "clearErrors", "inputName", "payload", "delete", "keepValue", "keepError", "keep<PERSON>irty", "keepTouched", "keepIsValidating", "keepDefaultValue", "keepIsValid", "_ref4", "disabledIsDefined", "progressive", "fieldRef", "querySelectorAll", "radioOrCheckbox", "_focusError", "_disableForm", "requiredDisabledState", "onValid", "onInvalid", "e", "onValidError", "preventDefault", "persist", "reset<PERSON>ield", "_reset", "keepStateOptions", "updatedValues", "cloneUpdatedValues", "isEmptyResetValues", "keepDefaultValues", "keepV<PERSON>ues", "keepDirtyV<PERSON>ues", "form", "closest", "reset", "keepSubmitCount", "keepIsSubmitted", "keepErrors", "keepIsSubmitSuccessful", "setFocus", "shouldSelect", "_resetDefaultValues", "resetOptions", "useForm", "_formControl", "_values"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isCheckBoxInput.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isDateObject.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isNullOrUndefined.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isObject.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getEventValue.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getNodeParentName.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\isNameInFieldArray.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isPlainObject.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isWeb.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\cloneObject.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\compact.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isUndefined.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\get.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isBoolean.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\constants.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\useFormContext.tsx", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getProxyFormState.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isEmptyObject.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\shouldRenderFormState.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\convertToArrayPayload.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\shouldSubscribeByName.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\useSubscribe.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\useFormState.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isString.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\generateWatchOutput.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\useWatch.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isKey.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\stringToPath.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\set.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\useController.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\controller.tsx", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\form.tsx", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\appendErrors.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\generateId.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getFocusFieldName.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getValidationModes.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\isWatched.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\iterateFieldsByAction.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\updateFieldArrayRootError.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isFileInput.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isFunction.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isHTMLElement.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isMessage.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isRadioInput.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isRegex.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getCheckboxValue.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getRadioValue.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getValidateError.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getValueAndMessage.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\validateField.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\append.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\fillEmptyArray.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\insert.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\move.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\prepend.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\remove.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\swap.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\unset.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\update.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\useFieldArray.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\createSubject.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isPrimitive.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\deepEqual.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isMultipleSelect.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\isRadioOrCheckbox.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\live.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\objectHasTruthyValue.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\utils\\objectHasFunction.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getDirtyFields.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getFieldValueAs.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getFieldValue.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getResolverOptions.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\getRuleValue.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\hasValidation.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\schemaErrorLookup.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\skipValidation.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\unsetEmptyArray.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\logic\\createFormControl.ts", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\node_modules\\react-hook-form\\src\\useForm.ts"], "sourcesContent": ["import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'checkbox';\n", "export default (value: unknown): value is Date => value instanceof Date;\n", "export default (value: unknown): value is null | undefined => value == null;\n", "import isDateObject from './isDateObject';\nimport isNullOrUndefined from './isNullOrUndefined';\n\nexport const isObjectType = (value: unknown): value is object =>\n  typeof value === 'object';\n\nexport default <T extends object>(value: unknown): value is T =>\n  !isNullOrUndefined(value) &&\n  !Array.isArray(value) &&\n  isObjectType(value) &&\n  !isDateObject(value);\n", "import isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isObject from '../utils/isObject';\n\ntype Event = { target: any };\n\nexport default (event: unknown) =>\n  isObject(event) && (event as Event).target\n    ? isCheckBoxInput((event as Event).target)\n      ? (event as Event).target.checked\n      : (event as Event).target.value\n    : event;\n", "export default (name: string) =>\n  name.substring(0, name.search(/\\.\\d+(\\.|$)/)) || name;\n", "import { InternalFieldName } from '../types';\n\nimport getNodeParentName from './getNodeParentName';\n\nexport default (names: Set<InternalFieldName>, name: InternalFieldName) =>\n  names.has(getNodeParentName(name));\n", "import isObject from './isObject';\n\nexport default (tempObject: object) => {\n  const prototypeCopy =\n    tempObject.constructor && tempObject.constructor.prototype;\n\n  return (\n    isObject(prototypeCopy) && prototypeCopy.hasOwnProperty('isPrototypeOf')\n  );\n};\n", "export default typeof window !== 'undefined' &&\n  typeof window.HTMLElement !== 'undefined' &&\n  typeof document !== 'undefined';\n", "import isObject from './isObject';\nimport isPlainObject from './isPlainObject';\nimport isWeb from './isWeb';\n\nexport default function cloneObject<T>(data: T): T {\n  let copy: any;\n  const isArray = Array.isArray(data);\n\n  if (data instanceof Date) {\n    copy = new Date(data);\n  } else if (data instanceof Set) {\n    copy = new Set(data);\n  } else if (\n    !(isWeb && (data instanceof Blob || data instanceof FileList)) &&\n    (isArray || isObject(data))\n  ) {\n    copy = isArray ? [] : {};\n\n    if (!isArray && !isPlainObject(data)) {\n      copy = data;\n    } else {\n      for (const key in data) {\n        if (data.hasOwnProperty(key)) {\n          copy[key] = cloneObject(data[key]);\n        }\n      }\n    }\n  } else {\n    return data;\n  }\n\n  return copy;\n}\n", "export default <TValue>(value: TValue[]) =>\n  Array.isArray(value) ? value.filter(Boolean) : [];\n", "export default (val: unknown): val is undefined => val === undefined;\n", "import compact from './compact';\nimport isNullOrUndefined from './isNullOrUndefined';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\n\nexport default <T>(object: T, path?: string, defaultValue?: unknown): any => {\n  if (!path || !isObject(object)) {\n    return defaultValue;\n  }\n\n  const result = compact(path.split(/[,[\\].]+?/)).reduce(\n    (result, key) =>\n      isNullOrUndefined(result) ? result : result[key as keyof {}],\n    object,\n  );\n\n  return isUndefined(result) || result === object\n    ? isUndefined(object[path as keyof T])\n      ? defaultValue\n      : object[path as keyof T]\n    : result;\n};\n", "export default (value: unknown): value is boolean => typeof value === 'boolean';\n", "export const EVENTS = {\n  BLUR: 'blur',\n  FOCUS_OUT: 'focusout',\n  CHANGE: 'change',\n} as const;\n\nexport const VALIDATION_MODE = {\n  onBlur: 'onBlur',\n  onChange: 'onChange',\n  onSubmit: 'onSubmit',\n  onTouched: 'onTouched',\n  all: 'all',\n} as const;\n\nexport const INPUT_VALIDATION_RULES = {\n  max: 'max',\n  min: 'min',\n  maxLength: 'maxLength',\n  minLength: 'minLength',\n  pattern: 'pattern',\n  required: 'required',\n  validate: 'validate',\n} as const;\n", "import React from 'react';\n\nimport { FieldValues, FormProviderProps, UseFormReturn } from './types';\n\nconst HookFormContext = React.createContext<UseFormReturn | null>(null);\n\n/**\n * This custom hook allows you to access the form context. useFormContext is intended to be used in deeply nested structures, where it would become inconvenient to pass the context as a prop. To be used with {@link FormProvider}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @returns return all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const useFormContext = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TransformedValues extends FieldValues | undefined = undefined,\n>(): UseFormReturn<TFieldValues, TContext, TransformedValues> =>\n  React.useContext(HookFormContext) as UseFormReturn<\n    TFieldValues,\n    TContext,\n    TransformedValues\n  >;\n\n/**\n * A provider component that propagates the `useForm` methods to all children components via [React Context](https://reactjs.org/docs/context.html) API. To be used with {@link useFormContext}.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformcontext) • [Demo](https://codesandbox.io/s/react-hook-form-v7-form-context-ytudi)\n *\n * @param props - all useForm methods\n *\n * @example\n * ```tsx\n * function App() {\n *   const methods = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   return (\n *     <FormProvider {...methods} >\n *       <form onSubmit={methods.handleSubmit(onSubmit)}>\n *         <NestedInput />\n *         <input type=\"submit\" />\n *       </form>\n *     </FormProvider>\n *   );\n * }\n *\n *  function NestedInput() {\n *   const { register } = useFormContext(); // retrieve all hook methods\n *   return <input {...register(\"test\")} />;\n * }\n * ```\n */\nexport const FormProvider = <\n  TFieldValues extends FieldValues,\n  TContext = any,\n  TTransformedValues extends FieldValues | undefined = undefined,\n>(\n  props: FormProviderProps<TFieldValues, TContext, TTransformedValues>,\n) => {\n  const { children, ...data } = props;\n  return (\n    <HookFormContext.Provider value={data as unknown as UseFormReturn}>\n      {children}\n    </HookFormContext.Provider>\n  );\n};\n", "import { VALIDATION_MODE } from '../constants';\nimport { Control, FieldValues, FormState, ReadFormState } from '../types';\n\nexport default <TFieldValues extends FieldValues, TContext = any>(\n  formState: FormState<TFieldValues>,\n  control: Control<TFieldValues, TContext>,\n  localProxyFormState?: ReadFormState,\n  isRoot = true,\n) => {\n  const result = {\n    defaultValues: control._defaultValues,\n  } as typeof formState;\n\n  for (const key in formState) {\n    Object.defineProperty(result, key, {\n      get: () => {\n        const _key = key as keyof FormState<TFieldValues> & keyof ReadFormState;\n\n        if (control._proxyFormState[_key] !== VALIDATION_MODE.all) {\n          control._proxyFormState[_key] = !isRoot || VALIDATION_MODE.all;\n        }\n\n        localProxyFormState && (localProxyFormState[_key] = true);\n        return formState[_key];\n      },\n    });\n  }\n\n  return result;\n};\n", "import { EmptyObject } from '../types';\n\nimport isObject from './isObject';\n\nexport default (value: unknown): value is EmptyObject =>\n  isObject(value) && !Object.keys(value).length;\n", "import { VALIDATION_MODE } from '../constants';\nimport {\n  Control,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  ReadFormState,\n} from '../types';\nimport isEmptyObject from '../utils/isEmptyObject';\n\nexport default <T extends FieldValues, K extends ReadFormState>(\n  formStateData: Partial<FormState<T>> & { name?: InternalFieldName },\n  _proxyFormState: K,\n  updateFormState: Control<T>['_updateFormState'],\n  isRoot?: boolean,\n) => {\n  updateFormState(formStateData);\n  const { name, ...formState } = formStateData;\n\n  return (\n    isEmptyObject(formState) ||\n    Object.keys(formState).length >= Object.keys(_proxyFormState).length ||\n    Object.keys(formState).find(\n      (key) =>\n        _proxyFormState[key as keyof ReadFormState] ===\n        (!isRoot || VALIDATION_MODE.all),\n    )\n  );\n};\n", "export default <T>(value: T) => (Array.isArray(value) ? value : [value]);\n", "import convertToArrayPayload from '../utils/convertToArrayPayload';\n\nexport default <T extends string | string[] | undefined>(\n  name?: T,\n  signalName?: string,\n  exact?: boolean,\n) =>\n  !name ||\n  !signalName ||\n  name === signalName ||\n  convertToArrayPayload(name).some(\n    (currentName) =>\n      currentName &&\n      (exact\n        ? currentName === signalName\n        : currentName.startsWith(signalName) ||\n          signalName.startsWith(currentName)),\n  );\n", "import React from 'react';\n\nimport { Subject } from './utils/createSubject';\n\ntype Props<T> = {\n  disabled?: boolean;\n  subject: Subject<T>;\n  next: (value: T) => void;\n};\n\nexport function useSubscribe<T>(props: Props<T>) {\n  const _props = React.useRef(props);\n  _props.current = props;\n\n  React.useEffect(() => {\n    const subscription =\n      !props.disabled &&\n      _props.current.subject &&\n      _props.current.subject.subscribe({\n        next: _props.current.next,\n      });\n\n    return () => {\n      subscription && subscription.unsubscribe();\n    };\n  }, [props.disabled]);\n}\n", "import React from 'react';\n\nimport getProxyFormState from './logic/getProxyFormState';\nimport shouldRenderFormState from './logic/shouldRenderFormState';\nimport shouldSubscribeByName from './logic/shouldSubscribeByName';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  UseFormStateProps,\n  UseFormStateReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * This custom hook allows you to subscribe to each form state, and isolate the re-render at the custom hook level. It has its scope in terms of form state subscription, so it would not affect other useFormState and useForm. Using this hook can reduce the re-render impact on large and complex form application.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useformstate) • [Demo](https://codesandbox.io/s/useformstate-75xly)\n *\n * @param props - include options on specify fields to subscribe. {@link UseFormStateReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, control } = useForm({\n *     defaultValues: {\n *     firstName: \"firstName\"\n *   }});\n *   const { dirtyFields } = useFormState({\n *     control\n *   });\n *   const onSubmit = (data) => console.log(data);\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input {...register(\"firstName\")} placeholder=\"First Name\" />\n *       {dirtyFields.firstName && <p>Field is dirty.</p>}\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nfunction useFormState<TFieldValues extends FieldValues = FieldValues>(\n  props?: UseFormStateProps<TFieldValues>,\n): UseFormStateReturn<TFieldValues> {\n  const methods = useFormContext<TFieldValues>();\n  const { control = methods.control, disabled, name, exact } = props || {};\n  const [formState, updateFormState] = React.useState(control._formState);\n  const _mounted = React.useRef(true);\n  const _localProxyFormState = React.useRef({\n    isDirty: false,\n    isLoading: false,\n    dirtyFields: false,\n    touchedFields: false,\n    validatingFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  });\n  const _name = React.useRef(name);\n\n  _name.current = name;\n\n  useSubscribe({\n    disabled,\n    next: (\n      value: Partial<FormState<TFieldValues>> & { name?: InternalFieldName },\n    ) =>\n      _mounted.current &&\n      shouldSubscribeByName(\n        _name.current as InternalFieldName,\n        value.name,\n        exact,\n      ) &&\n      shouldRenderFormState(\n        value,\n        _localProxyFormState.current,\n        control._updateFormState,\n      ) &&\n      updateFormState({\n        ...control._formState,\n        ...value,\n      }),\n    subject: control._subjects.state,\n  });\n\n  React.useEffect(() => {\n    _mounted.current = true;\n    _localProxyFormState.current.isValid && control._updateValid(true);\n\n    return () => {\n      _mounted.current = false;\n    };\n  }, [control]);\n\n  return getProxyFormState(\n    formState,\n    control,\n    _localProxyFormState.current,\n    false,\n  );\n}\n\nexport { useFormState };\n", "export default (value: unknown): value is string => typeof value === 'string';\n", "import { DeepPartial, FieldValues, Names } from '../types';\nimport get from '../utils/get';\nimport isString from '../utils/isString';\n\nexport default <T>(\n  names: string | string[] | undefined,\n  _names: Names,\n  formValues?: FieldValues,\n  isGlobal?: boolean,\n  defaultValue?: DeepPartial<T> | unknown,\n) => {\n  if (isString(names)) {\n    isGlobal && _names.watch.add(names);\n    return get(formValues, names, defaultValue);\n  }\n\n  if (Array.isArray(names)) {\n    return names.map(\n      (fieldName) => (\n        isGlobal && _names.watch.add(fieldName), get(formValues, fieldName)\n      ),\n    );\n  }\n\n  isGlobal && (_names.watchAll = true);\n\n  return formValues;\n};\n", "import React from 'react';\n\nimport generateWatchOutput from './logic/generateWatchOutput';\nimport shouldSubscribeByName from './logic/shouldSubscribeByName';\nimport cloneObject from './utils/cloneObject';\nimport {\n  Control,\n  DeepPartialSkipArrayKey,\n  FieldPath,\n  FieldPathValue,\n  FieldPathValues,\n  FieldValues,\n  InternalFieldName,\n  UseWatchProps,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * Subscribe to the entire form values change and re-render at the hook level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   defaultValue: {\n *     name: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(props: {\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: \"fieldA\",\n *   defaultValue: \"default value\",\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(props: {\n  name: TFieldName;\n  defaultValue?: FieldPathValue<TFieldValues, TFieldName>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValue<TFieldValues, TFieldName>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @param props - defaultValue, disable subscription and match exact name.\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   control,\n *   name: [\"fieldA\", \"fieldB\"],\n *   defaultValue: {\n *     fieldA: \"data\",\n *     fieldB: \"data\"\n *   },\n *   exact: false,\n * })\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldNames extends readonly FieldPath<TFieldValues>[] = readonly FieldPath<TFieldValues>[],\n>(props: {\n  name: readonly [...TFieldNames];\n  defaultValue?: DeepPartialSkipArrayKey<TFieldValues>;\n  control?: Control<TFieldValues>;\n  disabled?: boolean;\n  exact?: boolean;\n}): FieldPathValues<TFieldValues, TFieldNames>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * // can skip passing down the control into useWatch if the form is wrapped with the FormProvider\n * const values = useWatch()\n * ```\n */\nexport function useWatch<\n  TFieldValues extends FieldValues = FieldValues,\n>(): DeepPartialSkipArrayKey<TFieldValues>;\n/**\n * Custom hook to subscribe to field change and isolate re-rendering at the component level.\n *\n * @remarks\n *\n * [API](https://react-hook-form.com/docs/usewatch) • [Demo](https://codesandbox.io/s/react-hook-form-v7-ts-usewatch-h9i5e)\n *\n * @example\n * ```tsx\n * const { control } = useForm();\n * const values = useWatch({\n *   name: \"fieldName\"\n *   control,\n * })\n * ```\n */\nexport function useWatch<TFieldValues extends FieldValues>(\n  props?: UseWatchProps<TFieldValues>,\n) {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    defaultValue,\n    disabled,\n    exact,\n  } = props || {};\n  const _name = React.useRef(name);\n\n  _name.current = name;\n\n  useSubscribe({\n    disabled,\n    subject: control._subjects.values,\n    next: (formState: { name?: InternalFieldName; values?: FieldValues }) => {\n      if (\n        shouldSubscribeByName(\n          _name.current as InternalFieldName,\n          formState.name,\n          exact,\n        )\n      ) {\n        updateValue(\n          cloneObject(\n            generateWatchOutput(\n              _name.current as InternalFieldName | InternalFieldName[],\n              control._names,\n              formState.values || control._formValues,\n              false,\n              defaultValue,\n            ),\n          ),\n        );\n      }\n    },\n  });\n\n  const [value, updateValue] = React.useState(\n    control._getWatch(\n      name as InternalFieldName,\n      defaultValue as DeepPartialSkipArrayKey<TFieldValues>,\n    ),\n  );\n\n  React.useEffect(() => control._removeUnmounted());\n\n  return value;\n}\n", "export default (value: string) => /^\\w*$/.test(value);\n", "import compact from './compact';\n\nexport default (input: string): string[] =>\n  compact(input.replace(/[\"|']|\\]/g, '').split(/\\.|\\[/));\n", "import { FieldValues } from '../types';\n\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport stringToPath from './stringToPath';\n\nexport default (object: FieldValues, path: string, value?: unknown) => {\n  let index = -1;\n  const tempPath = isKey(path) ? [path] : stringToPath(path);\n  const length = tempPath.length;\n  const lastIndex = length - 1;\n\n  while (++index < length) {\n    const key = tempPath[index];\n    let newValue = value;\n\n    if (index !== lastIndex) {\n      const objValue = object[key];\n      newValue =\n        isObject(objValue) || Array.isArray(objValue)\n          ? objValue\n          : !isNaN(+tempPath[index + 1])\n          ? []\n          : {};\n    }\n    object[key] = newValue;\n    object = object[key];\n  }\n  return object;\n};\n", "import React from 'react';\n\nimport getEventValue from './logic/getEventValue';\nimport isNameInFieldArray from './logic/isNameInFieldArray';\nimport cloneObject from './utils/cloneObject';\nimport get from './utils/get';\nimport isBoolean from './utils/isBoolean';\nimport isUndefined from './utils/isUndefined';\nimport { EVENTS } from './constants';\nimport {\n  ControllerFieldState,\n  Field,\n  FieldPath,\n  FieldPathValue,\n  FieldValues,\n  InternalFieldName,\n  UseControllerProps,\n  UseControllerReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useFormState } from './useFormState';\nimport { useWatch } from './useWatch';\nimport { set } from './utils';\n\n/**\n * Custom hook to work with controlled component, this function provide you with both form and field level state. Re-render is isolated at the hook level.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller) • [Demo](https://codesandbox.io/s/usecontroller-0o8px)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns field properties, field and form state. {@link UseControllerReturn}\n *\n * @example\n * ```tsx\n * function Input(props) {\n *   const { field, fieldState, formState } = useController(props);\n *   return (\n *     <div>\n *       <input {...field} placeholder={props.name} />\n *       <p>{fieldState.isTouched && \"Touched\"}</p>\n *       <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *     </div>\n *   );\n * }\n * ```\n */\nexport function useController<\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(\n  props: UseControllerProps<TFieldValues, TName>,\n): UseControllerReturn<TFieldValues, TName> {\n  const methods = useFormContext<TFieldValues>();\n  const { name, disabled, control = methods.control, shouldUnregister } = props;\n  const isArrayField = isNameInFieldArray(control._names.array, name);\n  const value = useWatch({\n    control,\n    name,\n    defaultValue: get(\n      control._formValues,\n      name,\n      get(control._defaultValues, name, props.defaultValue),\n    ),\n    exact: true,\n  }) as FieldPathValue<TFieldValues, TName>;\n  const formState = useFormState({\n    control,\n    name,\n  });\n\n  const _registerProps = React.useRef(\n    control.register(name, {\n      ...props.rules,\n      value,\n      ...(isBoolean(props.disabled) ? { disabled: props.disabled } : {}),\n    }),\n  );\n\n  React.useEffect(() => {\n    const _shouldUnregisterField =\n      control._options.shouldUnregister || shouldUnregister;\n\n    const updateMounted = (name: InternalFieldName, value: boolean) => {\n      const field: Field = get(control._fields, name);\n\n      if (field) {\n        field._f.mount = value;\n      }\n    };\n\n    updateMounted(name, true);\n\n    if (_shouldUnregisterField) {\n      const value = cloneObject(get(control._options.defaultValues, name));\n      set(control._defaultValues, name, value);\n      if (isUndefined(get(control._formValues, name))) {\n        set(control._formValues, name, value);\n      }\n    }\n\n    return () => {\n      (\n        isArrayField\n          ? _shouldUnregisterField && !control._state.action\n          : _shouldUnregisterField\n      )\n        ? control.unregister(name)\n        : updateMounted(name, false);\n    };\n  }, [name, control, isArrayField, shouldUnregister]);\n\n  React.useEffect(() => {\n    if (get(control._fields, name)) {\n      control._updateDisabledField({\n        disabled,\n        fields: control._fields,\n        name,\n        value: get(control._fields, name)._f.value,\n      });\n    }\n  }, [disabled, name, control]);\n\n  return {\n    field: {\n      name,\n      value,\n      ...(isBoolean(disabled) || formState.disabled\n        ? { disabled: formState.disabled || disabled }\n        : {}),\n      onChange: React.useCallback(\n        (event) =>\n          _registerProps.current.onChange({\n            target: {\n              value: getEventValue(event),\n              name: name as InternalFieldName,\n            },\n            type: EVENTS.CHANGE,\n          }),\n        [name],\n      ),\n      onBlur: React.useCallback(\n        () =>\n          _registerProps.current.onBlur({\n            target: {\n              value: get(control._formValues, name),\n              name: name as InternalFieldName,\n            },\n            type: EVENTS.BLUR,\n          }),\n        [name, control],\n      ),\n      ref: (elm) => {\n        const field = get(control._fields, name);\n\n        if (field && elm) {\n          field._f.ref = {\n            focus: () => elm.focus(),\n            select: () => elm.select(),\n            setCustomValidity: (message: string) =>\n              elm.setCustomValidity(message),\n            reportValidity: () => elm.reportValidity(),\n          };\n        }\n      },\n    },\n    formState,\n    fieldState: Object.defineProperties(\n      {},\n      {\n        invalid: {\n          enumerable: true,\n          get: () => !!get(formState.errors, name),\n        },\n        isDirty: {\n          enumerable: true,\n          get: () => !!get(formState.dirtyFields, name),\n        },\n        isTouched: {\n          enumerable: true,\n          get: () => !!get(formState.touchedFields, name),\n        },\n        isValidating: {\n          enumerable: true,\n          get: () => !!get(formState.validatingFields, name),\n        },\n        error: {\n          enumerable: true,\n          get: () => get(formState.errors, name),\n        },\n      },\n    ) as ControllerFieldState,\n  };\n}\n", "import { ControllerProps, FieldPath, FieldValues } from './types';\nimport { useController } from './useController';\n\n/**\n * Component based on `useController` hook to work with controlled component.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usecontroller/controller) • [Demo](https://codesandbox.io/s/react-hook-form-v6-controller-ts-jwyzw) • [Video](https://www.youtube.com/watch?v=N2UNk_UCVyA)\n *\n * @param props - the path name to the form field value, and validation rules.\n *\n * @returns provide field handler functions, field and form state.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control } = useForm<FormValues>({\n *     defaultValues: {\n *       test: \"\"\n *     }\n *   });\n *\n *   return (\n *     <form>\n *       <Controller\n *         control={control}\n *         name=\"test\"\n *         render={({ field: { onChange, onBlur, value, ref }, formState, fieldState }) => (\n *           <>\n *             <input\n *               onChange={onChange} // send value to hook form\n *               onBlur={onBlur} // notify when input is touched\n *               value={value} // return updated value\n *               ref={ref} // set ref for focus management\n *             />\n *             <p>{formState.isSubmitted ? \"submitted\" : \"\"}</p>\n *             <p>{fieldState.isTouched ? \"touched\" : \"\"}</p>\n *           </>\n *         )}\n *       />\n *     </form>\n *   );\n * }\n * ```\n */\nconst Controller = <\n  TFieldValues extends FieldValues = FieldValues,\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>,\n>(\n  props: ControllerProps<TFieldValues, TName>,\n) => props.render(useController<TFieldValues, TName>(props));\n\nexport { Controller };\n", "import React from 'react';\n\nimport get from './utils/get';\nimport { FieldValues, FormProps } from './types';\nimport { useFormContext } from './useFormContext';\n\nconst POST_REQUEST = 'post';\n\n/**\n * Form component to manage submission.\n *\n * @param props - to setup submission detail. {@link FormProps}\n *\n * @returns form component or headless render prop.\n *\n * @example\n * ```tsx\n * function App() {\n *   const { control, formState: { errors } } = useForm();\n *\n *   return (\n *     <Form action=\"/api\" control={control}>\n *       <input {...register(\"name\")} />\n *       <p>{errors?.root?.server && 'Server error'}</p>\n *       <button>Submit</button>\n *     </Form>\n *   );\n * }\n * ```\n */\nfunction Form<\n  T extends FieldValues,\n  U extends FieldValues | undefined = undefined,\n>(props: FormProps<T, U>) {\n  const methods = useFormContext<T>();\n  const [mounted, setMounted] = React.useState(false);\n  const {\n    control = methods.control,\n    onSubmit,\n    children,\n    action,\n    method = POST_REQUEST,\n    headers,\n    encType,\n    onError,\n    render,\n    onSuccess,\n    validateStatus,\n    ...rest\n  } = props;\n\n  const submit = async (event?: React.BaseSyntheticEvent) => {\n    let hasError = false;\n    let type = '';\n\n    await control.handleSubmit(async (data) => {\n      const formData = new FormData();\n      let formDataJson = '';\n\n      try {\n        formDataJson = JSON.stringify(data);\n      } catch {}\n\n      for (const name of control._names.mount) {\n        formData.append(name, get(data, name));\n      }\n\n      if (onSubmit) {\n        await onSubmit({\n          data,\n          event,\n          method,\n          formData,\n          formDataJson,\n        });\n      }\n\n      if (action) {\n        try {\n          const shouldStringifySubmissionData = [\n            headers && headers['Content-Type'],\n            encType,\n          ].some((value) => value && value.includes('json'));\n\n          const response = await fetch(action, {\n            method,\n            headers: {\n              ...headers,\n              ...(encType ? { 'Content-Type': encType } : {}),\n            },\n            body: shouldStringifySubmissionData ? formDataJson : formData,\n          });\n\n          if (\n            response &&\n            (validateStatus\n              ? !validateStatus(response.status)\n              : response.status < 200 || response.status >= 300)\n          ) {\n            hasError = true;\n            onError && onError({ response });\n            type = String(response.status);\n          } else {\n            onSuccess && onSuccess({ response });\n          }\n        } catch (error: unknown) {\n          hasError = true;\n          onError && onError({ error });\n        }\n      }\n    })(event);\n\n    if (hasError && props.control) {\n      props.control._subjects.state.next({\n        isSubmitSuccessful: false,\n      });\n      props.control.setError('root.server', {\n        type,\n      });\n    }\n  };\n\n  React.useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  return render ? (\n    <>\n      {render({\n        submit,\n      })}\n    </>\n  ) : (\n    <form\n      noValidate={mounted}\n      action={action}\n      method={method}\n      encType={encType}\n      onSubmit={submit}\n      {...rest}\n    >\n      {children}\n    </form>\n  );\n}\n\nexport { Form };\n", "import {\n  InternalFieldErrors,\n  InternalFieldName,\n  ValidateResult,\n} from '../types';\n\nexport default (\n  name: InternalFieldName,\n  validateAllFieldCriteria: boolean,\n  errors: InternalFieldErrors,\n  type: string,\n  message: ValidateResult,\n) =>\n  validateAllFieldCriteria\n    ? {\n        ...errors[name],\n        types: {\n          ...(errors[name] && errors[name]!.types ? errors[name]!.types : {}),\n          [type]: message || true,\n        },\n      }\n    : {};\n", "export default () => {\n  const d =\n    typeof performance === 'undefined' ? Date.now() : performance.now() * 1000;\n\n  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n    const r = (Math.random() * 16 + d) % 16 | 0;\n\n    return (c == 'x' ? r : (r & 0x3) | 0x8).toString(16);\n  });\n};\n", "import { FieldArrayMethodProps, InternalFieldName } from '../types';\nimport isUndefined from '../utils/isUndefined';\n\nexport default (\n  name: InternalFieldName,\n  index: number,\n  options: FieldArrayMethodProps = {},\n): string =>\n  options.shouldFocus || isUndefined(options.shouldFocus)\n    ? options.focusName ||\n      `${name}.${isUndefined(options.focusIndex) ? index : options.focusIndex}.`\n    : '';\n", "import { VALIDATION_MODE } from '../constants';\nimport { Mode, ValidationModeFlags } from '../types';\n\nexport default (mode?: Mode): ValidationModeFlags => ({\n  isOnSubmit: !mode || mode === VALIDATION_MODE.onSubmit,\n  isOnBlur: mode === VALIDATION_MODE.onBlur,\n  isOnChange: mode === VALIDATION_MODE.onChange,\n  isOnAll: mode === VALIDATION_MODE.all,\n  isOnTouch: mode === VALIDATION_MODE.onTouched,\n});\n", "import { InternalFieldName, Names } from '../types';\n\nexport default (\n  name: InternalFieldName,\n  _names: Names,\n  isBlurEvent?: boolean,\n) =>\n  !isBlurEvent &&\n  (_names.watchAll ||\n    _names.watch.has(name) ||\n    [..._names.watch].some(\n      (watchName) =>\n        name.startsWith(watchName) &&\n        /^\\.\\w+/.test(name.slice(watchName.length)),\n    ));\n", "import { FieldRefs, InternalFieldName, Ref } from '../types';\nimport { get } from '../utils';\nimport isObject from '../utils/isObject';\n\nconst iterateFieldsByAction = (\n  fields: FieldRefs,\n  action: (ref: Ref, name: string) => 1 | undefined | void,\n  fieldsNames?: Set<InternalFieldName> | InternalFieldName[] | 0,\n  abortEarly?: boolean,\n) => {\n  for (const key of fieldsNames || Object.keys(fields)) {\n    const field = get(fields, key);\n\n    if (field) {\n      const { _f, ...currentField } = field;\n\n      if (_f) {\n        if (_f.refs && _f.refs[0] && action(_f.refs[0], key) && !abortEarly) {\n          break;\n        } else if (_f.ref && action(_f.ref, _f.name) && !abortEarly) {\n          break;\n        } else {\n          iterateFieldsByAction(currentField, action);\n        }\n      } else if (isObject(currentField)) {\n        iterateFieldsByAction(currentField, action);\n      }\n    }\n  }\n};\n\nexport default iterateFieldsByAction;\n", "import {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport compact from '../utils/compact';\nimport get from '../utils/get';\nimport set from '../utils/set';\n\nexport default <T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  error: Partial<Record<string, FieldError>>,\n  name: InternalFieldName,\n): FieldErrors<T> => {\n  const fieldArrayErrors = compact(get(errors, name));\n  set(fieldArrayErrors, 'root', error[name]);\n  set(errors, name, fieldArrayErrors);\n  return errors;\n};\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'file';\n", "export default (value: unknown): value is Function =>\n  typeof value === 'function';\n", "import isWeb from './isWeb';\n\nexport default (value: unknown): value is HTMLElement => {\n  if (!isWeb) {\n    return false;\n  }\n\n  const owner = value ? ((value as HTMLElement).ownerDocument as Document) : 0;\n  return (\n    value instanceof\n    (owner && owner.defaultView ? owner.defaultView.HTMLElement : HTMLElement)\n  );\n};\n", "import { Message } from '../types';\nimport isString from '../utils/isString';\n\nexport default (value: unknown): value is Message => isString(value);\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLInputElement =>\n  element.type === 'radio';\n", "export default (value: unknown): value is RegExp => value instanceof RegExp;\n", "import isUndefined from '../utils/isUndefined';\n\ntype CheckboxFieldResult = {\n  isValid: boolean;\n  value: string | string[] | boolean | undefined;\n};\n\nconst defaultResult: CheckboxFieldResult = {\n  value: false,\n  isValid: false,\n};\n\nconst validResult = { value: true, isValid: true };\n\nexport default (options?: HTMLInputElement[]): CheckboxFieldResult => {\n  if (Array.isArray(options)) {\n    if (options.length > 1) {\n      const values = options\n        .filter((option) => option && option.checked && !option.disabled)\n        .map((option) => option.value);\n      return { value: values, isValid: !!values.length };\n    }\n\n    return options[0].checked && !options[0].disabled\n      ? // @ts-expect-error expected to work in the browser\n        options[0].attributes && !isUndefined(options[0].attributes.value)\n        ? isUndefined(options[0].value) || options[0].value === ''\n          ? validResult\n          : { value: options[0].value, isValid: true }\n        : validResult\n      : defaultResult;\n  }\n\n  return defaultResult;\n};\n", "type RadioFieldResult = {\n  isValid: boolean;\n  value: number | string | null;\n};\n\nconst defaultReturn: RadioFieldResult = {\n  isValid: false,\n  value: null,\n};\n\nexport default (options?: HTMLInputElement[]): RadioFieldResult =>\n  Array.isArray(options)\n    ? options.reduce(\n        (previous, option): RadioFieldResult =>\n          option && option.checked && !option.disabled\n            ? {\n                isValid: true,\n                value: option.value,\n              }\n            : previous,\n        defaultReturn,\n      )\n    : defaultReturn;\n", "import { FieldError, Ref, ValidateResult } from '../types';\nimport isBoolean from '../utils/isBoolean';\nimport isMessage from '../utils/isMessage';\n\nexport default function getValidateError(\n  result: ValidateResult,\n  ref: Ref,\n  type = 'validate',\n): FieldError | void {\n  if (\n    isMessage(result) ||\n    (Array.isArray(result) && result.every(isMessage)) ||\n    (isBoolean(result) && !result)\n  ) {\n    return {\n      type,\n      message: isMessage(result) ? result : '',\n      ref,\n    };\n  }\n}\n", "import { ValidationRule } from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\n\nexport default (validationData?: ValidationRule) =>\n  isObject(validationData) && !isRegex(validationData)\n    ? validationData\n    : {\n        value: validationData,\n        message: '',\n      };\n", "import { INPUT_VALIDATION_RULES } from '../constants';\nimport {\n  Field,\n  FieldError,\n  FieldValues,\n  InternalFieldErrors,\n  MaxType,\n  Message,\n  MinType,\n  NativeFieldValue,\n} from '../types';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMessage from '../utils/isMessage';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isRadioInput from '../utils/isRadioInput';\nimport isRegex from '../utils/isRegex';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nimport appendErrors from './appendErrors';\nimport getCheckboxValue from './getCheckboxValue';\nimport getRadioValue from './getRadioValue';\nimport getValidateError from './getValidateError';\nimport getValueAndMessage from './getValueAndMessage';\n\nexport default async <T extends FieldValues>(\n  field: Field,\n  formValues: T,\n  validateAllFieldCriteria: boolean,\n  shouldUseNativeValidation?: boolean,\n  isFieldArray?: boolean,\n): Promise<InternalFieldErrors> => {\n  const {\n    ref,\n    refs,\n    required,\n    maxLength,\n    minLength,\n    min,\n    max,\n    pattern,\n    validate,\n    name,\n    valueAsNumber,\n    mount,\n    disabled,\n  } = field._f;\n  const inputValue: NativeFieldValue = get(formValues, name);\n  if (!mount || disabled) {\n    return {};\n  }\n  const inputRef: HTMLInputElement = refs ? refs[0] : (ref as HTMLInputElement);\n  const setCustomValidity = (message?: string | boolean) => {\n    if (shouldUseNativeValidation && inputRef.reportValidity) {\n      inputRef.setCustomValidity(isBoolean(message) ? '' : message || '');\n      inputRef.reportValidity();\n    }\n  };\n  const error: InternalFieldErrors = {};\n  const isRadio = isRadioInput(ref);\n  const isCheckBox = isCheckBoxInput(ref);\n  const isRadioOrCheckbox = isRadio || isCheckBox;\n  const isEmpty =\n    ((valueAsNumber || isFileInput(ref)) &&\n      isUndefined(ref.value) &&\n      isUndefined(inputValue)) ||\n    (isHTMLElement(ref) && ref.value === '') ||\n    inputValue === '' ||\n    (Array.isArray(inputValue) && !inputValue.length);\n  const appendErrorsCurry = appendErrors.bind(\n    null,\n    name,\n    validateAllFieldCriteria,\n    error,\n  );\n  const getMinMaxMessage = (\n    exceedMax: boolean,\n    maxLengthMessage: Message,\n    minLengthMessage: Message,\n    maxType: MaxType = INPUT_VALIDATION_RULES.maxLength,\n    minType: MinType = INPUT_VALIDATION_RULES.minLength,\n  ) => {\n    const message = exceedMax ? maxLengthMessage : minLengthMessage;\n    error[name] = {\n      type: exceedMax ? maxType : minType,\n      message,\n      ref,\n      ...appendErrorsCurry(exceedMax ? maxType : minType, message),\n    };\n  };\n\n  if (\n    isFieldArray\n      ? !Array.isArray(inputValue) || !inputValue.length\n      : required &&\n        ((!isRadioOrCheckbox && (isEmpty || isNullOrUndefined(inputValue))) ||\n          (isBoolean(inputValue) && !inputValue) ||\n          (isCheckBox && !getCheckboxValue(refs).isValid) ||\n          (isRadio && !getRadioValue(refs).isValid))\n  ) {\n    const { value, message } = isMessage(required)\n      ? { value: !!required, message: required }\n      : getValueAndMessage(required);\n\n    if (value) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.required,\n        message,\n        ref: inputRef,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.required, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (!isEmpty && (!isNullOrUndefined(min) || !isNullOrUndefined(max))) {\n    let exceedMax;\n    let exceedMin;\n    const maxOutput = getValueAndMessage(max);\n    const minOutput = getValueAndMessage(min);\n\n    if (!isNullOrUndefined(inputValue) && !isNaN(inputValue as number)) {\n      const valueNumber =\n        (ref as HTMLInputElement).valueAsNumber ||\n        (inputValue ? +inputValue : inputValue);\n      if (!isNullOrUndefined(maxOutput.value)) {\n        exceedMax = valueNumber > maxOutput.value;\n      }\n      if (!isNullOrUndefined(minOutput.value)) {\n        exceedMin = valueNumber < minOutput.value;\n      }\n    } else {\n      const valueDate =\n        (ref as HTMLInputElement).valueAsDate || new Date(inputValue as string);\n      const convertTimeToDate = (time: unknown) =>\n        new Date(new Date().toDateString() + ' ' + time);\n      const isTime = ref.type == 'time';\n      const isWeek = ref.type == 'week';\n\n      if (isString(maxOutput.value) && inputValue) {\n        exceedMax = isTime\n          ? convertTimeToDate(inputValue) > convertTimeToDate(maxOutput.value)\n          : isWeek\n          ? inputValue > maxOutput.value\n          : valueDate > new Date(maxOutput.value);\n      }\n\n      if (isString(minOutput.value) && inputValue) {\n        exceedMin = isTime\n          ? convertTimeToDate(inputValue) < convertTimeToDate(minOutput.value)\n          : isWeek\n          ? inputValue < minOutput.value\n          : valueDate < new Date(minOutput.value);\n      }\n    }\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        !!exceedMax,\n        maxOutput.message,\n        minOutput.message,\n        INPUT_VALIDATION_RULES.max,\n        INPUT_VALIDATION_RULES.min,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (\n    (maxLength || minLength) &&\n    !isEmpty &&\n    (isString(inputValue) || (isFieldArray && Array.isArray(inputValue)))\n  ) {\n    const maxLengthOutput = getValueAndMessage(maxLength);\n    const minLengthOutput = getValueAndMessage(minLength);\n    const exceedMax =\n      !isNullOrUndefined(maxLengthOutput.value) &&\n      inputValue.length > +maxLengthOutput.value;\n    const exceedMin =\n      !isNullOrUndefined(minLengthOutput.value) &&\n      inputValue.length < +minLengthOutput.value;\n\n    if (exceedMax || exceedMin) {\n      getMinMaxMessage(\n        exceedMax,\n        maxLengthOutput.message,\n        minLengthOutput.message,\n      );\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(error[name]!.message);\n        return error;\n      }\n    }\n  }\n\n  if (pattern && !isEmpty && isString(inputValue)) {\n    const { value: patternValue, message } = getValueAndMessage(pattern);\n\n    if (isRegex(patternValue) && !inputValue.match(patternValue)) {\n      error[name] = {\n        type: INPUT_VALIDATION_RULES.pattern,\n        message,\n        ref,\n        ...appendErrorsCurry(INPUT_VALIDATION_RULES.pattern, message),\n      };\n      if (!validateAllFieldCriteria) {\n        setCustomValidity(message);\n        return error;\n      }\n    }\n  }\n\n  if (validate) {\n    if (isFunction(validate)) {\n      const result = await validate(inputValue, formValues);\n      const validateError = getValidateError(result, inputRef);\n\n      if (validateError) {\n        error[name] = {\n          ...validateError,\n          ...appendErrorsCurry(\n            INPUT_VALIDATION_RULES.validate,\n            validateError.message,\n          ),\n        };\n        if (!validateAllFieldCriteria) {\n          setCustomValidity(validateError.message);\n          return error;\n        }\n      }\n    } else if (isObject(validate)) {\n      let validationResult = {} as FieldError;\n\n      for (const key in validate) {\n        if (!isEmptyObject(validationResult) && !validateAllFieldCriteria) {\n          break;\n        }\n\n        const validateError = getValidateError(\n          await validate[key](inputValue, formValues),\n          inputRef,\n          key,\n        );\n\n        if (validateError) {\n          validationResult = {\n            ...validateError,\n            ...appendErrorsCurry(key, validateError.message),\n          };\n\n          setCustomValidity(validateError.message);\n\n          if (validateAllFieldCriteria) {\n            error[name] = validationResult;\n          }\n        }\n      }\n\n      if (!isEmptyObject(validationResult)) {\n        error[name] = {\n          ref: inputRef,\n          ...validationResult,\n        };\n        if (!validateAllFieldCriteria) {\n          return error;\n        }\n      }\n    }\n  }\n\n  setCustomValidity(true);\n  return error;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...data,\n  ...convertToArrayPayload(value),\n];\n", "export default <T>(value: T | T[]): undefined[] | undefined =>\n  Array.isArray(value) ? value.map(() => undefined) : undefined;\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default function insert<T>(data: T[], index: number): (T | undefined)[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value: T | T[],\n): T[];\nexport default function insert<T>(\n  data: T[],\n  index: number,\n  value?: T | T[],\n): (T | undefined)[] {\n  return [\n    ...data.slice(0, index),\n    ...convertToArrayPayload(value),\n    ...data.slice(index),\n  ];\n}\n", "import isUndefined from './isUndefined';\n\nexport default <T>(\n  data: (T | undefined)[],\n  from: number,\n  to: number,\n): (T | undefined)[] => {\n  if (!Array.isArray(data)) {\n    return [];\n  }\n\n  if (isUndefined(data[to])) {\n    data[to] = undefined;\n  }\n  data.splice(to, 0, data.splice(from, 1)[0]);\n\n  return data;\n};\n", "import convertToArrayPayload from './convertToArrayPayload';\n\nexport default <T>(data: T[], value: T | T[]): T[] => [\n  ...convertToArrayPayload(value),\n  ...convertToArrayPayload(data),\n];\n", "import compact from './compact';\nimport convertToArrayPayload from './convertToArrayPayload';\nimport isUndefined from './isUndefined';\n\nfunction removeAtIndexes<T>(data: T[], indexes: number[]): T[] {\n  let i = 0;\n  const temp = [...data];\n\n  for (const index of indexes) {\n    temp.splice(index - i, 1);\n    i++;\n  }\n\n  return compact(temp).length ? temp : [];\n}\n\nexport default <T>(data: T[], index?: number | number[]): T[] =>\n  isUndefined(index)\n    ? []\n    : removeAtIndexes(\n        data,\n        (convertToArrayPayload(index) as number[]).sort((a, b) => a - b),\n      );\n", "export default <T>(data: T[], indexA: number, indexB: number): void => {\n  [data[indexA], data[indexB]] = [data[indexB], data[indexA]];\n};\n", "import isEmptyObject from './isEmptyObject';\nimport isKey from './isKey';\nimport isObject from './isObject';\nimport isUndefined from './isUndefined';\nimport stringToPath from './stringToPath';\n\nfunction baseGet(object: any, updatePath: (string | number)[]) {\n  const length = updatePath.slice(0, -1).length;\n  let index = 0;\n\n  while (index < length) {\n    object = isUndefined(object) ? index++ : object[updatePath[index++]];\n  }\n\n  return object;\n}\n\nfunction isEmptyArray(obj: unknown[]) {\n  for (const key in obj) {\n    if (obj.hasOwnProperty(key) && !isUndefined(obj[key])) {\n      return false;\n    }\n  }\n  return true;\n}\n\nexport default function unset(object: any, path: string | (string | number)[]) {\n  const paths = Array.isArray(path)\n    ? path\n    : isKey(path)\n    ? [path]\n    : stringToPath(path);\n\n  const childObject = paths.length === 1 ? object : baseGet(object, paths);\n\n  const index = paths.length - 1;\n  const key = paths[index];\n\n  if (childObject) {\n    delete childObject[key];\n  }\n\n  if (\n    index !== 0 &&\n    ((isObject(childObject) && isEmptyObject(childObject)) ||\n      (Array.isArray(childObject) && isEmptyArray(childObject)))\n  ) {\n    unset(object, paths.slice(0, -1));\n  }\n\n  return object;\n}\n", "export default <T>(fieldValues: T[], index: number, value: T) => {\n  fieldValues[index] = value;\n  return fieldValues;\n};\n", "import React from 'react';\n\nimport generateId from './logic/generateId';\nimport getFocusFieldName from './logic/getFocusFieldName';\nimport getValidationModes from './logic/getValidationModes';\nimport isWatched from './logic/isWatched';\nimport iterateFieldsByAction from './logic/iterateFieldsByAction';\nimport updateFieldArrayRootError from './logic/updateFieldArrayRootError';\nimport validateField from './logic/validateField';\nimport appendAt from './utils/append';\nimport cloneObject from './utils/cloneObject';\nimport convertToArrayPayload from './utils/convertToArrayPayload';\nimport fillEmptyArray from './utils/fillEmptyArray';\nimport get from './utils/get';\nimport insertAt from './utils/insert';\nimport isEmptyObject from './utils/isEmptyObject';\nimport moveArrayAt from './utils/move';\nimport prependAt from './utils/prepend';\nimport removeArrayAt from './utils/remove';\nimport set from './utils/set';\nimport swapArrayAt from './utils/swap';\nimport unset from './utils/unset';\nimport updateAt from './utils/update';\nimport { VALIDATION_MODE } from './constants';\nimport {\n  Control,\n  Field,\n  FieldArray,\n  FieldArrayMethodProps,\n  FieldArrayPath,\n  FieldArrayWithId,\n  FieldErrors,\n  FieldPath,\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  RegisterOptions,\n  UseFieldArrayProps,\n  UseFieldArrayReturn,\n} from './types';\nimport { useFormContext } from './useFormContext';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * A custom hook that exposes convenient methods to perform operations with a list of dynamic inputs that need to be appended, updated, removed etc. • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn) • [Video](https://youtu.be/4MrbfGSFY2A)\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/usefieldarray) • [Demo](https://codesandbox.io/s/react-hook-form-usefieldarray-ssugn)\n *\n * @param props - useFieldArray props\n *\n * @returns methods - functions to manipulate with the Field Arrays (dynamic inputs) {@link UseFieldArrayReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, control, handleSubmit, reset, trigger, setError } = useForm({\n *     defaultValues: {\n *       test: []\n *     }\n *   });\n *   const { fields, append } = useFieldArray({\n *     control,\n *     name: \"test\"\n *   });\n *\n *   return (\n *     <form onSubmit={handleSubmit(data => console.log(data))}>\n *       {fields.map((item, index) => (\n *          <input key={item.id} {...register(`test.${index}.firstName`)}  />\n *       ))}\n *       <button type=\"button\" onClick={() => append({ firstName: \"bill\" })}>\n *         append\n *       </button>\n *       <input type=\"submit\" />\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useFieldArray<\n  TFieldValues extends FieldValues = FieldValues,\n  TFieldArrayName extends FieldArrayPath<TFieldValues> = FieldArrayPath<TFieldValues>,\n  TKeyName extends string = 'id',\n>(\n  props: UseFieldArrayProps<TFieldValues, TFieldArrayName, TKeyName>,\n): UseFieldArrayReturn<TFieldValues, TFieldArrayName, TKeyName> {\n  const methods = useFormContext();\n  const {\n    control = methods.control,\n    name,\n    keyName = 'id',\n    shouldUnregister,\n  } = props;\n  const [fields, setFields] = React.useState(control._getFieldArray(name));\n  const ids = React.useRef<string[]>(\n    control._getFieldArray(name).map(generateId),\n  );\n  const _fieldIds = React.useRef(fields);\n  const _name = React.useRef(name);\n  const _actioned = React.useRef(false);\n\n  _name.current = name;\n  _fieldIds.current = fields;\n  control._names.array.add(name);\n\n  props.rules &&\n    (control as Control<TFieldValues>).register(\n      name as FieldPath<TFieldValues>,\n      props.rules as RegisterOptions<TFieldValues>,\n    );\n\n  useSubscribe({\n    next: ({\n      values,\n      name: fieldArrayName,\n    }: {\n      values?: FieldValues;\n      name?: InternalFieldName;\n    }) => {\n      if (fieldArrayName === _name.current || !fieldArrayName) {\n        const fieldValues = get(values, _name.current);\n        if (Array.isArray(fieldValues)) {\n          setFields(fieldValues);\n          ids.current = fieldValues.map(generateId);\n        }\n      }\n    },\n    subject: control._subjects.array,\n  });\n\n  const updateValues = React.useCallback(\n    <\n      T extends Partial<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >[],\n    >(\n      updatedFieldArrayValues: T,\n    ) => {\n      _actioned.current = true;\n      control._updateFieldArray(name, updatedFieldArrayValues);\n    },\n    [control, name],\n  );\n\n  const append = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const appendValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = appendAt(\n      control._getFieldArray(name),\n      appendValue,\n    );\n    control._names.focus = getFocusFieldName(\n      name,\n      updatedFieldArrayValues.length - 1,\n      options,\n    );\n    ids.current = appendAt(ids.current, appendValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, appendAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const prepend = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const prependValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = prependAt(\n      control._getFieldArray(name),\n      prependValue,\n    );\n    control._names.focus = getFocusFieldName(name, 0, options);\n    ids.current = prependAt(ids.current, prependValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, prependAt, {\n      argA: fillEmptyArray(value),\n    });\n  };\n\n  const remove = (index?: number | number[]) => {\n    const updatedFieldArrayValues: Partial<\n      FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n    >[] = removeArrayAt(control._getFieldArray(name), index);\n    ids.current = removeArrayAt(ids.current, index);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, removeArrayAt, {\n      argA: index,\n    });\n  };\n\n  const insert = (\n    index: number,\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n    options?: FieldArrayMethodProps,\n  ) => {\n    const insertValue = convertToArrayPayload(cloneObject(value));\n    const updatedFieldArrayValues = insertAt(\n      control._getFieldArray(name),\n      index,\n      insertValue,\n    );\n    control._names.focus = getFocusFieldName(name, index, options);\n    ids.current = insertAt(ids.current, index, insertValue.map(generateId));\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(name, updatedFieldArrayValues, insertAt, {\n      argA: index,\n      argB: fillEmptyArray(value),\n    });\n  };\n\n  const swap = (indexA: number, indexB: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    swapArrayAt(updatedFieldArrayValues, indexA, indexB);\n    swapArrayAt(ids.current, indexA, indexB);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      swapArrayAt,\n      {\n        argA: indexA,\n        argB: indexB,\n      },\n      false,\n    );\n  };\n\n  const move = (from: number, to: number) => {\n    const updatedFieldArrayValues = control._getFieldArray(name);\n    moveArrayAt(updatedFieldArrayValues, from, to);\n    moveArrayAt(ids.current, from, to);\n    updateValues(updatedFieldArrayValues);\n    setFields(updatedFieldArrayValues);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      moveArrayAt,\n      {\n        argA: from,\n        argB: to,\n      },\n      false,\n    );\n  };\n\n  const update = (\n    index: number,\n    value: FieldArray<TFieldValues, TFieldArrayName>,\n  ) => {\n    const updateValue = cloneObject(value);\n    const updatedFieldArrayValues = updateAt(\n      control._getFieldArray<\n        FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>\n      >(name),\n      index,\n      updateValue as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>,\n    );\n    ids.current = [...updatedFieldArrayValues].map((item, i) =>\n      !item || i === index ? generateId() : ids.current[i],\n    );\n    updateValues(updatedFieldArrayValues);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(\n      name,\n      updatedFieldArrayValues,\n      updateAt,\n      {\n        argA: index,\n        argB: updateValue,\n      },\n      true,\n      false,\n    );\n  };\n\n  const replace = (\n    value:\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>\n      | Partial<FieldArray<TFieldValues, TFieldArrayName>>[],\n  ) => {\n    const updatedFieldArrayValues = convertToArrayPayload(cloneObject(value));\n    ids.current = updatedFieldArrayValues.map(generateId);\n    updateValues([...updatedFieldArrayValues]);\n    setFields([...updatedFieldArrayValues]);\n    control._updateFieldArray(\n      name,\n      [...updatedFieldArrayValues],\n      <T>(data: T): T => data,\n      {},\n      true,\n      false,\n    );\n  };\n\n  React.useEffect(() => {\n    control._state.action = false;\n\n    isWatched(name, control._names) &&\n      control._subjects.state.next({\n        ...control._formState,\n      } as FormState<TFieldValues>);\n\n    if (\n      _actioned.current &&\n      (!getValidationModes(control._options.mode).isOnSubmit ||\n        control._formState.isSubmitted)\n    ) {\n      if (control._options.resolver) {\n        control._executeSchema([name]).then((result) => {\n          const error = get(result.errors, name);\n          const existingError = get(control._formState.errors, name);\n\n          if (\n            existingError\n              ? (!error && existingError.type) ||\n                (error &&\n                  (existingError.type !== error.type ||\n                    existingError.message !== error.message))\n              : error && error.type\n          ) {\n            error\n              ? set(control._formState.errors, name, error)\n              : unset(control._formState.errors, name);\n            control._subjects.state.next({\n              errors: control._formState.errors as FieldErrors<TFieldValues>,\n            });\n          }\n        });\n      } else {\n        const field: Field = get(control._fields, name);\n        if (\n          field &&\n          field._f &&\n          !(\n            getValidationModes(control._options.reValidateMode).isOnSubmit &&\n            getValidationModes(control._options.mode).isOnSubmit\n          )\n        ) {\n          validateField(\n            field,\n            control._formValues,\n            control._options.criteriaMode === VALIDATION_MODE.all,\n            control._options.shouldUseNativeValidation,\n            true,\n          ).then(\n            (error) =>\n              !isEmptyObject(error) &&\n              control._subjects.state.next({\n                errors: updateFieldArrayRootError(\n                  control._formState.errors as FieldErrors<TFieldValues>,\n                  error,\n                  name,\n                ) as FieldErrors<TFieldValues>,\n              }),\n          );\n        }\n      }\n    }\n\n    control._subjects.values.next({\n      name,\n      values: { ...control._formValues },\n    });\n\n    control._names.focus &&\n      iterateFieldsByAction(control._fields, (ref, key: string) => {\n        if (\n          control._names.focus &&\n          key.startsWith(control._names.focus) &&\n          ref.focus\n        ) {\n          ref.focus();\n          return 1;\n        }\n        return;\n      });\n\n    control._names.focus = '';\n\n    control._updateValid();\n    _actioned.current = false;\n  }, [fields, name, control]);\n\n  React.useEffect(() => {\n    !get(control._formValues, name) && control._updateFieldArray(name);\n\n    return () => {\n      (control._options.shouldUnregister || shouldUnregister) &&\n        control.unregister(name as FieldPath<TFieldValues>);\n    };\n  }, [name, control, keyName, shouldUnregister]);\n\n  return {\n    swap: React.useCallback(swap, [updateValues, name, control]),\n    move: React.useCallback(move, [updateValues, name, control]),\n    prepend: React.useCallback(prepend, [updateValues, name, control]),\n    append: React.useCallback(append, [updateValues, name, control]),\n    remove: React.useCallback(remove, [updateValues, name, control]),\n    insert: React.useCallback(insert, [updateValues, name, control]),\n    update: React.useCallback(update, [updateValues, name, control]),\n    replace: React.useCallback(replace, [updateValues, name, control]),\n    fields: React.useMemo(\n      () =>\n        fields.map((field, index) => ({\n          ...field,\n          [keyName]: ids.current[index] || generateId(),\n        })) as FieldArrayWithId<TFieldValues, TFieldArrayName, TKeyName>[],\n      [fields, keyName],\n    ),\n  };\n}\n", "import { Noop } from '../types';\n\nexport type Observer<T> = {\n  next: (value: T) => void;\n};\n\nexport type Subscription = {\n  unsubscribe: Noop;\n};\n\nexport type Subject<T> = {\n  readonly observers: Observer<T>[];\n  subscribe: (value: Observer<T>) => Subscription;\n  unsubscribe: Noop;\n} & Observer<T>;\n\nexport default <T>(): Subject<T> => {\n  let _observers: Observer<T>[] = [];\n\n  const next = (value: T) => {\n    for (const observer of _observers) {\n      observer.next && observer.next(value);\n    }\n  };\n\n  const subscribe = (observer: Observer<T>): Subscription => {\n    _observers.push(observer);\n    return {\n      unsubscribe: () => {\n        _observers = _observers.filter((o) => o !== observer);\n      },\n    };\n  };\n\n  const unsubscribe = () => {\n    _observers = [];\n  };\n\n  return {\n    get observers() {\n      return _observers;\n    },\n    next,\n    subscribe,\n    unsubscribe,\n  };\n};\n", "import { Primitive } from '../types';\n\nimport isNullOrUndefined from './isNullOrUndefined';\nimport { isObjectType } from './isObject';\n\nexport default (value: unknown): value is Primitive =>\n  isNullOrUndefined(value) || !isObjectType(value);\n", "import isObject from '../utils/isObject';\n\nimport isDateObject from './isDateObject';\nimport isPrimitive from './isPrimitive';\n\nexport default function deepEqual(object1: any, object2: any) {\n  if (isPrimitive(object1) || isPrimitive(object2)) {\n    return object1 === object2;\n  }\n\n  if (isDateObject(object1) && isDateObject(object2)) {\n    return object1.getTime() === object2.getTime();\n  }\n\n  const keys1 = Object.keys(object1);\n  const keys2 = Object.keys(object2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (const key of keys1) {\n    const val1 = object1[key];\n\n    if (!keys2.includes(key)) {\n      return false;\n    }\n\n    if (key !== 'ref') {\n      const val2 = object2[key];\n\n      if (\n        (isDateObject(val1) && isDateObject(val2)) ||\n        (isObject(val1) && isObject(val2)) ||\n        (Array.isArray(val1) && Array.isArray(val2))\n          ? !deepEqual(val1, val2)\n          : val1 !== val2\n      ) {\n        return false;\n      }\n    }\n  }\n\n  return true;\n}\n", "import { FieldElement } from '../types';\n\nexport default (element: FieldElement): element is HTMLSelectElement =>\n  element.type === `select-multiple`;\n", "import { FieldElement } from '../types';\n\nimport isCheckBoxInput from './isCheckBoxInput';\nimport isRadioInput from './isRadioInput';\n\nexport default (ref: FieldElement): ref is HTMLInputElement =>\n  isRadioInput(ref) || isCheckBoxInput(ref);\n", "import { Ref } from '../types';\n\nimport isHTMLElement from './isHTMLElement';\n\nexport default (ref: Ref) => isHTMLElement(ref) && ref.isConnected;\n", "import isObject from './isObject';\n\nexport default (value: unknown): value is object =>\n  isObject(value) && Object.values(value).some((val) => val);\n", "import isFunction from './isFunction';\n\nexport default <T>(data: T): boolean => {\n  for (const key in data) {\n    if (isFunction(data[key])) {\n      return true;\n    }\n  }\n  return false;\n};\n", "import deepEqual from '../utils/deepEqual';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isUndefined from '../utils/isUndefined';\nimport objectHasFunction from '../utils/objectHasFunction';\n\nfunction markFieldsDirty<T>(data: T, fields: Record<string, any> = {}) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        fields[key] = Array.isArray(data[key]) ? [] : {};\n        markFieldsDirty(data[key], fields[key]);\n      } else if (!isNullOrUndefined(data[key])) {\n        fields[key] = true;\n      }\n    }\n  }\n\n  return fields;\n}\n\nfunction getDirtyFieldsFromDefaultValues<T>(\n  data: T,\n  formValues: T,\n  dirtyFieldsFromValues: Record<\n    Extract<keyof T, string>,\n    ReturnType<typeof markFieldsDirty> | boolean\n  >,\n) {\n  const isParentNodeArray = Array.isArray(data);\n\n  if (isObject(data) || isParentNodeArray) {\n    for (const key in data) {\n      if (\n        Array.isArray(data[key]) ||\n        (isObject(data[key]) && !objectHasFunction(data[key]))\n      ) {\n        if (\n          isUndefined(formValues) ||\n          isPrimitive(dirtyFieldsFromValues[key])\n        ) {\n          dirtyFieldsFromValues[key] = Array.isArray(data[key])\n            ? markFieldsDirty(data[key], [])\n            : { ...markFieldsDirty(data[key]) };\n        } else {\n          getDirtyFieldsFromDefaultValues(\n            data[key],\n            isNullOrUndefined(formValues) ? {} : formValues[key],\n            dirtyFieldsFromValues[key],\n          );\n        }\n      } else {\n        dirtyFieldsFromValues[key] = !deepEqual(data[key], formValues[key]);\n      }\n    }\n  }\n\n  return dirtyFieldsFromValues;\n}\n\nexport default <T>(defaultValues: T, formValues: T) =>\n  getDirtyFieldsFromDefaultValues(\n    defaultValues,\n    formValues,\n    markFieldsDirty(formValues),\n  );\n", "import { Field, NativeFieldValue } from '../types';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends NativeFieldValue>(\n  value: T,\n  { valueAsNumber, valueAsDate, setValueAs }: Field['_f'],\n) =>\n  isUndefined(value)\n    ? value\n    : valueAsNumber\n    ? value === ''\n      ? NaN\n      : value\n      ? +value\n      : value\n    : valueAsDate && isString(value)\n    ? new Date(value)\n    : setValueAs\n    ? setValueAs(value)\n    : value;\n", "import { Field } from '../types';\nimport isCheckBox from '../utils/isCheckBoxInput';\nimport isFileInput from '../utils/isFileInput';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isRadioInput from '../utils/isRadioInput';\nimport isUndefined from '../utils/isUndefined';\n\nimport getCheckboxValue from './getCheckboxValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getRadioValue from './getRadioValue';\n\nexport default function getFieldValue(_f: Field['_f']) {\n  const ref = _f.ref;\n\n  if (_f.refs ? _f.refs.every((ref) => ref.disabled) : ref.disabled) {\n    return;\n  }\n\n  if (isFileInput(ref)) {\n    return ref.files;\n  }\n\n  if (isRadioInput(ref)) {\n    return getRadioValue(_f.refs).value;\n  }\n\n  if (isMultipleSelect(ref)) {\n    return [...ref.selectedOptions].map(({ value }) => value);\n  }\n\n  if (isCheckBox(ref)) {\n    return getCheckboxValue(_f.refs).value;\n  }\n\n  return getFieldValueAs(isUndefined(ref.value) ? _f.ref.value : ref.value, _f);\n}\n", "import {\n  CriteriaMode,\n  Field,\n  FieldName,\n  FieldRefs,\n  FieldValues,\n  InternalFieldName,\n} from '../types';\nimport { get } from '../utils';\nimport set from '../utils/set';\n\nexport default <TFieldValues extends FieldValues>(\n  fieldsNames: Set<InternalFieldName> | InternalFieldName[],\n  _fields: FieldRefs,\n  criteriaMode?: CriteriaMode,\n  shouldUseNativeValidation?: boolean | undefined,\n) => {\n  const fields: Record<InternalFieldName, Field['_f']> = {};\n\n  for (const name of fieldsNames) {\n    const field: Field = get(_fields, name);\n\n    field && set(fields, name, field._f);\n  }\n\n  return {\n    criteriaMode,\n    names: [...fieldsNames] as FieldName<TFieldValues>[],\n    fields,\n    shouldUseNativeValidation,\n  };\n};\n", "import {\n  ValidationRule,\n  ValidationValue,\n  ValidationValueMessage,\n} from '../types';\nimport isObject from '../utils/isObject';\nimport isRegex from '../utils/isRegex';\nimport isUndefined from '../utils/isUndefined';\n\nexport default <T extends ValidationValue>(\n  rule?: ValidationRule<T> | ValidationValueMessage<T>,\n) =>\n  isUndefined(rule)\n    ? rule\n    : isRegex(rule)\n    ? rule.source\n    : isObject(rule)\n    ? isRegex(rule.value)\n      ? rule.value.source\n      : rule.value\n    : rule;\n", "import { Field } from '../types';\n\nexport default (options: Field['_f']) =>\n  options.mount &&\n  (options.required ||\n    options.min ||\n    options.max ||\n    options.maxLength ||\n    options.minLength ||\n    options.pattern ||\n    options.validate);\n", "import { FieldError, FieldErrors, FieldValues } from '../types';\nimport get from '../utils/get';\nimport isKey from '../utils/isKey';\n\nexport default function schemaErrorLookup<T extends FieldValues = FieldValues>(\n  errors: FieldErrors<T>,\n  _fields: FieldValues,\n  name: string,\n): {\n  error?: FieldError;\n  name: string;\n} {\n  const error = get(errors, name);\n\n  if (error || isKey(name)) {\n    return {\n      error,\n      name,\n    };\n  }\n\n  const names = name.split('.');\n\n  while (names.length) {\n    const fieldName = names.join('.');\n    const field = get(_fields, fieldName);\n    const foundError = get(errors, fieldName);\n\n    if (field && !Array.isArray(field) && name !== fieldName) {\n      return { name };\n    }\n\n    if (foundError && foundError.type) {\n      return {\n        name: fieldName,\n        error: foundError,\n      };\n    }\n\n    names.pop();\n  }\n\n  return {\n    name,\n  };\n}\n", "import { ValidationModeFlags } from '../types';\n\nexport default (\n  isBlurEvent: boolean,\n  isTouched: boolean,\n  isSubmitted: boolean,\n  reValidateMode: {\n    isOnBlur: boolean;\n    isOnChange: boolean;\n  },\n  mode: Partial<ValidationModeFlags>,\n) => {\n  if (mode.isOnAll) {\n    return false;\n  } else if (!isSubmitted && mode.isOnTouch) {\n    return !(isTouched || isBlurEvent);\n  } else if (isSubmitted ? reValidateMode.isOnBlur : mode.isOnBlur) {\n    return !isBlurEvent;\n  } else if (isSubmitted ? reValidateMode.isOnChange : mode.isOnChange) {\n    return isBlurEvent;\n  }\n  return true;\n};\n", "import compact from '../utils/compact';\nimport get from '../utils/get';\nimport unset from '../utils/unset';\n\nexport default <T>(ref: T, name: string) =>\n  !compact(get(ref, name)).length && unset(ref, name);\n", "import { EVENTS, VALIDATION_MODE } from '../constants';\nimport {\n  BatchFieldArrayUpdate,\n  ChangeHandler,\n  Control,\n  DeepPartial,\n  DelayCallback,\n  EventType,\n  Field,\n  FieldError,\n  FieldErrors,\n  FieldNamesMarkedBoolean,\n  FieldPath,\n  FieldRefs,\n  FieldValues,\n  FormState,\n  GetIsDirty,\n  InternalFieldName,\n  Names,\n  Path,\n  PathValue,\n  ReadFormState,\n  Ref,\n  SetFieldValue,\n  SetValueConfig,\n  Subjects,\n  UseFormClearErrors,\n  UseFormGetFieldState,\n  UseFormGetValues,\n  UseFormHandleSubmit,\n  UseFormProps,\n  UseFormRegister,\n  UseFormReset,\n  UseFormResetField,\n  UseFormReturn,\n  UseFormSetError,\n  UseFormSetFocus,\n  UseFormSetValue,\n  UseFormTrigger,\n  UseFormUnregister,\n  UseFormWatch,\n  WatchInternal,\n  WatchObserver,\n} from '../types';\nimport cloneObject from '../utils/cloneObject';\nimport compact from '../utils/compact';\nimport convertToArrayPayload from '../utils/convertToArrayPayload';\nimport createSubject from '../utils/createSubject';\nimport deepEqual from '../utils/deepEqual';\nimport get from '../utils/get';\nimport isBoolean from '../utils/isBoolean';\nimport isCheckBoxInput from '../utils/isCheckBoxInput';\nimport isDateObject from '../utils/isDateObject';\nimport isEmptyObject from '../utils/isEmptyObject';\nimport isFileInput from '../utils/isFileInput';\nimport isFunction from '../utils/isFunction';\nimport isHTMLElement from '../utils/isHTMLElement';\nimport isMultipleSelect from '../utils/isMultipleSelect';\nimport isNullOrUndefined from '../utils/isNullOrUndefined';\nimport isObject from '../utils/isObject';\nimport isPrimitive from '../utils/isPrimitive';\nimport isRadioOrCheckbox from '../utils/isRadioOrCheckbox';\nimport isString from '../utils/isString';\nimport isUndefined from '../utils/isUndefined';\nimport isWeb from '../utils/isWeb';\nimport live from '../utils/live';\nimport objectHasTruthyValue from '../utils/objectHasTruthyValue';\nimport set from '../utils/set';\nimport unset from '../utils/unset';\n\nimport generateWatchOutput from './generateWatchOutput';\nimport getDirtyFields from './getDirtyFields';\nimport getEventValue from './getEventValue';\nimport getFieldValue from './getFieldValue';\nimport getFieldValueAs from './getFieldValueAs';\nimport getResolverOptions from './getResolverOptions';\nimport getRuleValue from './getRuleValue';\nimport getValidationModes from './getValidationModes';\nimport hasValidation from './hasValidation';\nimport isNameInFieldArray from './isNameInFieldArray';\nimport isWatched from './isWatched';\nimport iterateFieldsByAction from './iterateFieldsByAction';\nimport schemaErrorLookup from './schemaErrorLookup';\nimport skipValidation from './skipValidation';\nimport unsetEmptyArray from './unsetEmptyArray';\nimport updateFieldArrayRootError from './updateFieldArrayRootError';\nimport validateField from './validateField';\n\nconst defaultOptions = {\n  mode: VALIDATION_MODE.onSubmit,\n  reValidateMode: VALIDATION_MODE.onChange,\n  shouldFocusError: true,\n} as const;\n\nexport function createFormControl<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n>(\n  props: UseFormProps<TFieldValues, TContext> = {},\n): Omit<UseFormReturn<TFieldValues, TContext>, 'formState'> {\n  let _options = {\n    ...defaultOptions,\n    ...props,\n  };\n  let _formState: FormState<TFieldValues> = {\n    submitCount: 0,\n    isDirty: false,\n    isLoading: isFunction(_options.defaultValues),\n    isValidating: false,\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    touchedFields: {},\n    dirtyFields: {},\n    validatingFields: {},\n    errors: _options.errors || {},\n    disabled: _options.disabled || false,\n  };\n  let _fields: FieldRefs = {};\n  let _defaultValues =\n    isObject(_options.defaultValues) || isObject(_options.values)\n      ? cloneObject(_options.defaultValues || _options.values) || {}\n      : {};\n  let _formValues = _options.shouldUnregister\n    ? {}\n    : cloneObject(_defaultValues);\n  let _state = {\n    action: false,\n    mount: false,\n    watch: false,\n  };\n  let _names: Names = {\n    mount: new Set(),\n    unMount: new Set(),\n    array: new Set(),\n    watch: new Set(),\n  };\n  let delayErrorCallback: DelayCallback | null;\n  let timer = 0;\n  const _proxyFormState: ReadFormState = {\n    isDirty: false,\n    dirtyFields: false,\n    validatingFields: false,\n    touchedFields: false,\n    isValidating: false,\n    isValid: false,\n    errors: false,\n  };\n  const _subjects: Subjects<TFieldValues> = {\n    values: createSubject(),\n    array: createSubject(),\n    state: createSubject(),\n  };\n  const validationModeBeforeSubmit = getValidationModes(_options.mode);\n  const validationModeAfterSubmit = getValidationModes(_options.reValidateMode);\n  const shouldDisplayAllAssociatedErrors =\n    _options.criteriaMode === VALIDATION_MODE.all;\n\n  const debounce =\n    <T extends Function>(callback: T) =>\n    (wait: number) => {\n      clearTimeout(timer);\n      timer = setTimeout(callback, wait);\n    };\n\n  const _updateValid = async (shouldUpdateValid?: boolean) => {\n    if (_proxyFormState.isValid || shouldUpdateValid) {\n      const isValid = _options.resolver\n        ? isEmptyObject((await _executeSchema()).errors)\n        : await executeBuiltInValidation(_fields, true);\n\n      if (isValid !== _formState.isValid) {\n        _subjects.state.next({\n          isValid,\n        });\n      }\n    }\n  };\n\n  const _updateIsValidating = (isValidating: boolean, names: string[]) => {\n    if (!(_proxyFormState.isValidating || _proxyFormState.validatingFields)) {\n      return;\n    }\n    names.forEach((name) => {\n      set(_formState.validatingFields, name, isValidating);\n    });\n    _formState.isValidating = objectHasTruthyValue(_formState.validatingFields);\n    _subjects.state.next({\n      validatingFields: _formState.validatingFields,\n      isValidating: _formState.isValidating,\n    });\n  };\n\n  const _updateFieldArray: BatchFieldArrayUpdate = (\n    name,\n    values = [],\n    method,\n    args,\n    shouldSetValues = true,\n    shouldUpdateFieldsAndState = true,\n  ) => {\n    if (args && method) {\n      _state.action = true;\n      if (shouldUpdateFieldsAndState && Array.isArray(get(_fields, name))) {\n        const fieldValues = method(get(_fields, name), args.argA, args.argB);\n        shouldSetValues && set(_fields, name, fieldValues);\n      }\n\n      if (\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.errors, name))\n      ) {\n        const errors = method(\n          get(_formState.errors, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.errors, name, errors);\n        unsetEmptyArray(_formState.errors, name);\n      }\n\n      if (\n        _proxyFormState.touchedFields &&\n        shouldUpdateFieldsAndState &&\n        Array.isArray(get(_formState.touchedFields, name))\n      ) {\n        const touchedFields = method(\n          get(_formState.touchedFields, name),\n          args.argA,\n          args.argB,\n        );\n        shouldSetValues && set(_formState.touchedFields, name, touchedFields);\n      }\n\n      if (_proxyFormState.dirtyFields) {\n        _formState.dirtyFields = getDirtyFields(_defaultValues, _formValues);\n      }\n\n      _subjects.state.next({\n        name,\n        isDirty: _getDirty(name, values),\n        dirtyFields: _formState.dirtyFields,\n        errors: _formState.errors,\n        isValid: _formState.isValid,\n      });\n    } else {\n      set(_formValues, name, values);\n    }\n  };\n\n  const updateErrors = (name: InternalFieldName, error: FieldError) => {\n    set(_formState.errors, name, error);\n    _subjects.state.next({\n      errors: _formState.errors,\n    });\n  };\n\n  const _setErrors = (errors: FieldErrors<TFieldValues>) => {\n    _formState.errors = errors;\n    _subjects.state.next({\n      errors: _formState.errors,\n      isValid: false,\n    });\n  };\n\n  const updateValidAndValue = (\n    name: InternalFieldName,\n    shouldSkipSetValueAs: boolean,\n    value?: unknown,\n    ref?: Ref,\n  ) => {\n    const field: Field = get(_fields, name);\n\n    if (field) {\n      const defaultValue = get(\n        _formValues,\n        name,\n        isUndefined(value) ? get(_defaultValues, name) : value,\n      );\n\n      isUndefined(defaultValue) ||\n      (ref && (ref as HTMLInputElement).defaultChecked) ||\n      shouldSkipSetValueAs\n        ? set(\n            _formValues,\n            name,\n            shouldSkipSetValueAs ? defaultValue : getFieldValue(field._f),\n          )\n        : setFieldValue(name, defaultValue);\n\n      _state.mount && _updateValid();\n    }\n  };\n\n  const updateTouchAndDirty = (\n    name: InternalFieldName,\n    fieldValue: unknown,\n    isBlurEvent?: boolean,\n    shouldDirty?: boolean,\n    shouldRender?: boolean,\n  ): Partial<\n    Pick<FormState<TFieldValues>, 'dirtyFields' | 'isDirty' | 'touchedFields'>\n  > => {\n    let shouldUpdateField = false;\n    let isPreviousDirty = false;\n    const output: Partial<FormState<TFieldValues>> & { name: string } = {\n      name,\n    };\n    const disabledField = !!(\n      get(_fields, name) && get(_fields, name)._f.disabled\n    );\n\n    if (!isBlurEvent || shouldDirty) {\n      if (_proxyFormState.isDirty) {\n        isPreviousDirty = _formState.isDirty;\n        _formState.isDirty = output.isDirty = _getDirty();\n        shouldUpdateField = isPreviousDirty !== output.isDirty;\n      }\n\n      const isCurrentFieldPristine =\n        disabledField || deepEqual(get(_defaultValues, name), fieldValue);\n\n      isPreviousDirty = !!(!disabledField && get(_formState.dirtyFields, name));\n      isCurrentFieldPristine || disabledField\n        ? unset(_formState.dirtyFields, name)\n        : set(_formState.dirtyFields, name, true);\n      output.dirtyFields = _formState.dirtyFields;\n      shouldUpdateField =\n        shouldUpdateField ||\n        (_proxyFormState.dirtyFields &&\n          isPreviousDirty !== !isCurrentFieldPristine);\n    }\n\n    if (isBlurEvent) {\n      const isPreviousFieldTouched = get(_formState.touchedFields, name);\n\n      if (!isPreviousFieldTouched) {\n        set(_formState.touchedFields, name, isBlurEvent);\n        output.touchedFields = _formState.touchedFields;\n        shouldUpdateField =\n          shouldUpdateField ||\n          (_proxyFormState.touchedFields &&\n            isPreviousFieldTouched !== isBlurEvent);\n      }\n    }\n\n    shouldUpdateField && shouldRender && _subjects.state.next(output);\n\n    return shouldUpdateField ? output : {};\n  };\n\n  const shouldRenderByError = (\n    name: InternalFieldName,\n    isValid?: boolean,\n    error?: FieldError,\n    fieldState?: {\n      dirty?: FieldNamesMarkedBoolean<TFieldValues>;\n      isDirty?: boolean;\n      touched?: FieldNamesMarkedBoolean<TFieldValues>;\n    },\n  ) => {\n    const previousFieldError = get(_formState.errors, name);\n    const shouldUpdateValid =\n      _proxyFormState.isValid &&\n      isBoolean(isValid) &&\n      _formState.isValid !== isValid;\n\n    if (props.delayError && error) {\n      delayErrorCallback = debounce(() => updateErrors(name, error));\n      delayErrorCallback(props.delayError);\n    } else {\n      clearTimeout(timer);\n      delayErrorCallback = null;\n      error\n        ? set(_formState.errors, name, error)\n        : unset(_formState.errors, name);\n    }\n\n    if (\n      (error ? !deepEqual(previousFieldError, error) : previousFieldError) ||\n      !isEmptyObject(fieldState) ||\n      shouldUpdateValid\n    ) {\n      const updatedFormState = {\n        ...fieldState,\n        ...(shouldUpdateValid && isBoolean(isValid) ? { isValid } : {}),\n        errors: _formState.errors,\n        name,\n      };\n\n      _formState = {\n        ..._formState,\n        ...updatedFormState,\n      };\n\n      _subjects.state.next(updatedFormState);\n    }\n\n    _updateIsValidating(\n      false,\n      Object.keys(_formState.validatingFields).filter((key) => key === name),\n    );\n  };\n\n  const _executeSchema = async (name?: InternalFieldName[]) =>\n    _options.resolver!(\n      _formValues as TFieldValues,\n      _options.context,\n      getResolverOptions(\n        name || _names.mount,\n        _fields,\n        _options.criteriaMode,\n        _options.shouldUseNativeValidation,\n      ),\n    );\n\n  const executeSchemaAndUpdateState = async (names?: InternalFieldName[]) => {\n    const { errors } = await _executeSchema(names);\n\n    if (names) {\n      for (const name of names) {\n        const error = get(errors, name);\n        error\n          ? set(_formState.errors, name, error)\n          : unset(_formState.errors, name);\n      }\n    } else {\n      _formState.errors = errors;\n    }\n\n    return errors;\n  };\n\n  const executeBuiltInValidation = async (\n    fields: FieldRefs,\n    shouldOnlyCheckValid?: boolean,\n    context: {\n      valid: boolean;\n    } = {\n      valid: true,\n    },\n  ) => {\n    for (const name in fields) {\n      const field = fields[name];\n\n      if (field) {\n        const { _f, ...fieldValue } = field;\n\n        if (_f) {\n          const isFieldArrayRoot = _names.array.has(_f.name);\n          const fieldError = await validateField(\n            field,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation && !shouldOnlyCheckValid,\n            isFieldArrayRoot,\n          );\n\n          if (fieldError[_f.name]) {\n            context.valid = false;\n            if (shouldOnlyCheckValid) {\n              break;\n            }\n          }\n\n          !shouldOnlyCheckValid &&\n            (get(fieldError, _f.name)\n              ? isFieldArrayRoot\n                ? updateFieldArrayRootError(\n                    _formState.errors,\n                    fieldError,\n                    _f.name,\n                  )\n                : set(_formState.errors, _f.name, fieldError[_f.name])\n              : unset(_formState.errors, _f.name));\n        }\n\n        fieldValue &&\n          (await executeBuiltInValidation(\n            fieldValue,\n            shouldOnlyCheckValid,\n            context,\n          ));\n      }\n    }\n\n    return context.valid;\n  };\n\n  const _removeUnmounted = () => {\n    for (const name of _names.unMount) {\n      const field: Field = get(_fields, name);\n\n      field &&\n        (field._f.refs\n          ? field._f.refs.every((ref) => !live(ref))\n          : !live(field._f.ref)) &&\n        unregister(name as FieldPath<TFieldValues>);\n    }\n\n    _names.unMount = new Set();\n  };\n\n  const _getDirty: GetIsDirty = (name, data) => (\n    name && data && set(_formValues, name, data),\n    !deepEqual(getValues(), _defaultValues)\n  );\n\n  const _getWatch: WatchInternal<TFieldValues> = (\n    names,\n    defaultValue,\n    isGlobal,\n  ) =>\n    generateWatchOutput(\n      names,\n      _names,\n      {\n        ...(_state.mount\n          ? _formValues\n          : isUndefined(defaultValue)\n          ? _defaultValues\n          : isString(names)\n          ? { [names]: defaultValue }\n          : defaultValue),\n      },\n      isGlobal,\n      defaultValue,\n    );\n\n  const _getFieldArray = <TFieldArrayValues>(\n    name: InternalFieldName,\n  ): Partial<TFieldArrayValues>[] =>\n    compact(\n      get(\n        _state.mount ? _formValues : _defaultValues,\n        name,\n        props.shouldUnregister ? get(_defaultValues, name, []) : [],\n      ),\n    );\n\n  const setFieldValue = (\n    name: InternalFieldName,\n    value: SetFieldValue<TFieldValues>,\n    options: SetValueConfig = {},\n  ) => {\n    const field: Field = get(_fields, name);\n    let fieldValue: unknown = value;\n\n    if (field) {\n      const fieldReference = field._f;\n\n      if (fieldReference) {\n        !fieldReference.disabled &&\n          set(_formValues, name, getFieldValueAs(value, fieldReference));\n\n        fieldValue =\n          isHTMLElement(fieldReference.ref) && isNullOrUndefined(value)\n            ? ''\n            : value;\n\n        if (isMultipleSelect(fieldReference.ref)) {\n          [...fieldReference.ref.options].forEach(\n            (optionRef) =>\n              (optionRef.selected = (\n                fieldValue as InternalFieldName[]\n              ).includes(optionRef.value)),\n          );\n        } else if (fieldReference.refs) {\n          if (isCheckBoxInput(fieldReference.ref)) {\n            fieldReference.refs.length > 1\n              ? fieldReference.refs.forEach(\n                  (checkboxRef) =>\n                    (!checkboxRef.defaultChecked || !checkboxRef.disabled) &&\n                    (checkboxRef.checked = Array.isArray(fieldValue)\n                      ? !!(fieldValue as []).find(\n                          (data: string) => data === checkboxRef.value,\n                        )\n                      : fieldValue === checkboxRef.value),\n                )\n              : fieldReference.refs[0] &&\n                (fieldReference.refs[0].checked = !!fieldValue);\n          } else {\n            fieldReference.refs.forEach(\n              (radioRef: HTMLInputElement) =>\n                (radioRef.checked = radioRef.value === fieldValue),\n            );\n          }\n        } else if (isFileInput(fieldReference.ref)) {\n          fieldReference.ref.value = '';\n        } else {\n          fieldReference.ref.value = fieldValue;\n\n          if (!fieldReference.ref.type) {\n            _subjects.values.next({\n              name,\n              values: { ..._formValues },\n            });\n          }\n        }\n      }\n    }\n\n    (options.shouldDirty || options.shouldTouch) &&\n      updateTouchAndDirty(\n        name,\n        fieldValue,\n        options.shouldTouch,\n        options.shouldDirty,\n        true,\n      );\n\n    options.shouldValidate && trigger(name as Path<TFieldValues>);\n  };\n\n  const setValues = <\n    T extends InternalFieldName,\n    K extends SetFieldValue<TFieldValues>,\n    U extends SetValueConfig,\n  >(\n    name: T,\n    value: K,\n    options: U,\n  ) => {\n    for (const fieldKey in value) {\n      const fieldValue = value[fieldKey];\n      const fieldName = `${name}.${fieldKey}`;\n      const field = get(_fields, fieldName);\n\n      (_names.array.has(name) ||\n        !isPrimitive(fieldValue) ||\n        (field && !field._f)) &&\n      !isDateObject(fieldValue)\n        ? setValues(fieldName, fieldValue, options)\n        : setFieldValue(fieldName, fieldValue, options);\n    }\n  };\n\n  const setValue: UseFormSetValue<TFieldValues> = (\n    name,\n    value,\n    options = {},\n  ) => {\n    const field = get(_fields, name);\n    const isFieldArray = _names.array.has(name);\n    const cloneValue = cloneObject(value);\n\n    set(_formValues, name, cloneValue);\n\n    if (isFieldArray) {\n      _subjects.array.next({\n        name,\n        values: { ..._formValues },\n      });\n\n      if (\n        (_proxyFormState.isDirty || _proxyFormState.dirtyFields) &&\n        options.shouldDirty\n      ) {\n        _subjects.state.next({\n          name,\n          dirtyFields: getDirtyFields(_defaultValues, _formValues),\n          isDirty: _getDirty(name, cloneValue),\n        });\n      }\n    } else {\n      field && !field._f && !isNullOrUndefined(cloneValue)\n        ? setValues(name, cloneValue, options)\n        : setFieldValue(name, cloneValue, options);\n    }\n\n    isWatched(name, _names) && _subjects.state.next({ ..._formState });\n    _subjects.values.next({\n      name: _state.mount ? name : undefined,\n      values: { ..._formValues },\n    });\n  };\n\n  const onChange: ChangeHandler = async (event) => {\n    const target = event.target;\n    let name = target.name as string;\n    let isFieldValueUpdated = true;\n    const field: Field = get(_fields, name);\n    const getCurrentFieldValue = () =>\n      target.type ? getFieldValue(field._f) : getEventValue(event);\n    const _updateIsFieldValueUpdated = (fieldValue: any): void => {\n      isFieldValueUpdated =\n        Number.isNaN(fieldValue) ||\n        fieldValue === get(_formValues, name, fieldValue);\n    };\n\n    if (field) {\n      let error;\n      let isValid;\n      const fieldValue = getCurrentFieldValue();\n      const isBlurEvent =\n        event.type === EVENTS.BLUR || event.type === EVENTS.FOCUS_OUT;\n      const shouldSkipValidation =\n        (!hasValidation(field._f) &&\n          !_options.resolver &&\n          !get(_formState.errors, name) &&\n          !field._f.deps) ||\n        skipValidation(\n          isBlurEvent,\n          get(_formState.touchedFields, name),\n          _formState.isSubmitted,\n          validationModeAfterSubmit,\n          validationModeBeforeSubmit,\n        );\n      const watched = isWatched(name, _names, isBlurEvent);\n\n      set(_formValues, name, fieldValue);\n\n      if (isBlurEvent) {\n        field._f.onBlur && field._f.onBlur(event);\n        delayErrorCallback && delayErrorCallback(0);\n      } else if (field._f.onChange) {\n        field._f.onChange(event);\n      }\n\n      const fieldState = updateTouchAndDirty(\n        name,\n        fieldValue,\n        isBlurEvent,\n        false,\n      );\n\n      const shouldRender = !isEmptyObject(fieldState) || watched;\n\n      !isBlurEvent &&\n        _subjects.values.next({\n          name,\n          type: event.type,\n          values: { ..._formValues },\n        });\n\n      if (shouldSkipValidation) {\n        _proxyFormState.isValid && _updateValid();\n\n        return (\n          shouldRender &&\n          _subjects.state.next({ name, ...(watched ? {} : fieldState) })\n        );\n      }\n\n      !isBlurEvent && watched && _subjects.state.next({ ..._formState });\n\n      _updateIsValidating(true, [name]);\n\n      if (_options.resolver) {\n        const { errors } = await _executeSchema([name]);\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          const previousErrorLookupResult = schemaErrorLookup(\n            _formState.errors,\n            _fields,\n            name,\n          );\n          const errorLookupResult = schemaErrorLookup(\n            errors,\n            _fields,\n            previousErrorLookupResult.name || name,\n          );\n\n          error = errorLookupResult.error;\n          name = errorLookupResult.name;\n\n          isValid = isEmptyObject(errors);\n        }\n      } else {\n        error = (\n          await validateField(\n            field,\n            _formValues,\n            shouldDisplayAllAssociatedErrors,\n            _options.shouldUseNativeValidation,\n          )\n        )[name];\n\n        _updateIsFieldValueUpdated(fieldValue);\n\n        if (isFieldValueUpdated) {\n          if (error) {\n            isValid = false;\n          } else if (_proxyFormState.isValid) {\n            isValid = await executeBuiltInValidation(_fields, true);\n          }\n        }\n      }\n\n      if (isFieldValueUpdated) {\n        field._f.deps &&\n          trigger(\n            field._f.deps as\n              | FieldPath<TFieldValues>\n              | FieldPath<TFieldValues>[],\n          );\n        shouldRenderByError(name, isValid, error, fieldState);\n      }\n    }\n  };\n\n  const _focusInput = (ref: Ref, key: string) => {\n    if (get(_formState.errors, key) && ref.focus) {\n      ref.focus();\n      return 1;\n    }\n    return;\n  };\n\n  const trigger: UseFormTrigger<TFieldValues> = async (name, options = {}) => {\n    let isValid;\n    let validationResult;\n    const fieldNames = convertToArrayPayload(name) as InternalFieldName[];\n\n    _updateIsValidating(true, fieldNames);\n\n    if (_options.resolver) {\n      const errors = await executeSchemaAndUpdateState(\n        isUndefined(name) ? name : fieldNames,\n      );\n\n      isValid = isEmptyObject(errors);\n      validationResult = name\n        ? !fieldNames.some((name) => get(errors, name))\n        : isValid;\n    } else if (name) {\n      validationResult = (\n        await Promise.all(\n          fieldNames.map(async (fieldName) => {\n            const field = get(_fields, fieldName);\n            return await executeBuiltInValidation(\n              field && field._f ? { [fieldName]: field } : field,\n            );\n          }),\n        )\n      ).every(Boolean);\n      !(!validationResult && !_formState.isValid) && _updateValid();\n    } else {\n      validationResult = isValid = await executeBuiltInValidation(_fields);\n    }\n\n    _subjects.state.next({\n      ...(!isString(name) ||\n      (_proxyFormState.isValid && isValid !== _formState.isValid)\n        ? {}\n        : { name }),\n      ...(_options.resolver || !name ? { isValid } : {}),\n      errors: _formState.errors,\n      isValidating: false,\n    });\n\n    options.shouldFocus &&\n      !validationResult &&\n      iterateFieldsByAction(\n        _fields,\n        _focusInput,\n        name ? fieldNames : _names.mount,\n      );\n\n    return validationResult;\n  };\n\n  const getValues: UseFormGetValues<TFieldValues> = (\n    fieldNames?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>,\n  ) => {\n    const values = {\n      ..._defaultValues,\n      ...(_state.mount ? _formValues : {}),\n    };\n\n    return isUndefined(fieldNames)\n      ? values\n      : isString(fieldNames)\n      ? get(values, fieldNames)\n      : fieldNames.map((name) => get(values, name));\n  };\n\n  const getFieldState: UseFormGetFieldState<TFieldValues> = (\n    name,\n    formState,\n  ) => ({\n    invalid: !!get((formState || _formState).errors, name),\n    isDirty: !!get((formState || _formState).dirtyFields, name),\n    isTouched: !!get((formState || _formState).touchedFields, name),\n    isValidating: !!get((formState || _formState).validatingFields, name),\n    error: get((formState || _formState).errors, name),\n  });\n\n  const clearErrors: UseFormClearErrors<TFieldValues> = (name) => {\n    name &&\n      convertToArrayPayload(name).forEach((inputName) =>\n        unset(_formState.errors, inputName),\n      );\n\n    _subjects.state.next({\n      errors: name ? _formState.errors : {},\n    });\n  };\n\n  const setError: UseFormSetError<TFieldValues> = (name, error, options) => {\n    const ref = (get(_fields, name, { _f: {} })._f || {}).ref;\n\n    set(_formState.errors, name, {\n      ...error,\n      ref,\n    });\n\n    _subjects.state.next({\n      name,\n      errors: _formState.errors,\n      isValid: false,\n    });\n\n    options && options.shouldFocus && ref && ref.focus && ref.focus();\n  };\n\n  const watch: UseFormWatch<TFieldValues> = (\n    name?:\n      | FieldPath<TFieldValues>\n      | ReadonlyArray<FieldPath<TFieldValues>>\n      | WatchObserver<TFieldValues>,\n    defaultValue?: DeepPartial<TFieldValues>,\n  ) =>\n    isFunction(name)\n      ? _subjects.values.subscribe({\n          next: (payload) =>\n            name(\n              _getWatch(undefined, defaultValue),\n              payload as {\n                name?: FieldPath<TFieldValues>;\n                type?: EventType;\n                value?: unknown;\n              },\n            ),\n        })\n      : _getWatch(\n          name as InternalFieldName | InternalFieldName[],\n          defaultValue,\n          true,\n        );\n\n  const unregister: UseFormUnregister<TFieldValues> = (name, options = {}) => {\n    for (const fieldName of name ? convertToArrayPayload(name) : _names.mount) {\n      _names.mount.delete(fieldName);\n      _names.array.delete(fieldName);\n\n      if (!options.keepValue) {\n        unset(_fields, fieldName);\n        unset(_formValues, fieldName);\n      }\n\n      !options.keepError && unset(_formState.errors, fieldName);\n      !options.keepDirty && unset(_formState.dirtyFields, fieldName);\n      !options.keepTouched && unset(_formState.touchedFields, fieldName);\n      !options.keepIsValidating &&\n        unset(_formState.validatingFields, fieldName);\n      !_options.shouldUnregister &&\n        !options.keepDefaultValue &&\n        unset(_defaultValues, fieldName);\n    }\n\n    _subjects.values.next({\n      values: { ..._formValues },\n    });\n\n    _subjects.state.next({\n      ..._formState,\n      ...(!options.keepDirty ? {} : { isDirty: _getDirty() }),\n    });\n\n    !options.keepIsValid && _updateValid();\n  };\n\n  const _updateDisabledField: Control<TFieldValues>['_updateDisabledField'] = ({\n    disabled,\n    name,\n    field,\n    fields,\n    value,\n  }) => {\n    if (isBoolean(disabled)) {\n      const inputValue = disabled\n        ? undefined\n        : isUndefined(value)\n        ? getFieldValue(field ? field._f : get(fields, name)._f)\n        : value;\n      set(_formValues, name, inputValue);\n      updateTouchAndDirty(name, inputValue, false, false, true);\n    }\n  };\n\n  const register: UseFormRegister<TFieldValues> = (name, options = {}) => {\n    let field = get(_fields, name);\n    const disabledIsDefined = isBoolean(options.disabled);\n\n    set(_fields, name, {\n      ...(field || {}),\n      _f: {\n        ...(field && field._f ? field._f : { ref: { name } }),\n        name,\n        mount: true,\n        ...options,\n      },\n    });\n    _names.mount.add(name);\n\n    if (field) {\n      _updateDisabledField({\n        field,\n        disabled: options.disabled,\n        name,\n        value: options.value,\n      });\n    } else {\n      updateValidAndValue(name, true, options.value);\n    }\n\n    return {\n      ...(disabledIsDefined ? { disabled: options.disabled } : {}),\n      ...(_options.progressive\n        ? {\n            required: !!options.required,\n            min: getRuleValue(options.min),\n            max: getRuleValue(options.max),\n            minLength: getRuleValue<number>(options.minLength) as number,\n            maxLength: getRuleValue(options.maxLength) as number,\n            pattern: getRuleValue(options.pattern) as string,\n          }\n        : {}),\n      name,\n      onChange,\n      onBlur: onChange,\n      ref: (ref: HTMLInputElement | null): void => {\n        if (ref) {\n          register(name, options);\n          field = get(_fields, name);\n\n          const fieldRef = isUndefined(ref.value)\n            ? ref.querySelectorAll\n              ? (ref.querySelectorAll('input,select,textarea')[0] as Ref) || ref\n              : ref\n            : ref;\n          const radioOrCheckbox = isRadioOrCheckbox(fieldRef);\n          const refs = field._f.refs || [];\n\n          if (\n            radioOrCheckbox\n              ? refs.find((option: Ref) => option === fieldRef)\n              : fieldRef === field._f.ref\n          ) {\n            return;\n          }\n\n          set(_fields, name, {\n            _f: {\n              ...field._f,\n              ...(radioOrCheckbox\n                ? {\n                    refs: [\n                      ...refs.filter(live),\n                      fieldRef,\n                      ...(Array.isArray(get(_defaultValues, name)) ? [{}] : []),\n                    ],\n                    ref: { type: fieldRef.type, name },\n                  }\n                : { ref: fieldRef }),\n            },\n          });\n\n          updateValidAndValue(name, false, undefined, fieldRef);\n        } else {\n          field = get(_fields, name, {});\n\n          if (field._f) {\n            field._f.mount = false;\n          }\n\n          (_options.shouldUnregister || options.shouldUnregister) &&\n            !(isNameInFieldArray(_names.array, name) && _state.action) &&\n            _names.unMount.add(name);\n        }\n      },\n    };\n  };\n\n  const _focusError = () =>\n    _options.shouldFocusError &&\n    iterateFieldsByAction(_fields, _focusInput, _names.mount);\n\n  const _disableForm = (disabled?: boolean) => {\n    if (isBoolean(disabled)) {\n      _subjects.state.next({ disabled });\n      iterateFieldsByAction(\n        _fields,\n        (ref, name) => {\n          let requiredDisabledState = disabled;\n          const currentField = get(_fields, name);\n          if (currentField && isBoolean(currentField._f.disabled)) {\n            requiredDisabledState ||= currentField._f.disabled;\n          }\n\n          ref.disabled = requiredDisabledState;\n        },\n        0,\n        false,\n      );\n    }\n  };\n\n  const handleSubmit: UseFormHandleSubmit<TFieldValues> =\n    (onValid, onInvalid) => async (e) => {\n      let onValidError = undefined;\n      if (e) {\n        e.preventDefault && e.preventDefault();\n        e.persist && e.persist();\n      }\n      let fieldValues = cloneObject(_formValues);\n\n      _subjects.state.next({\n        isSubmitting: true,\n      });\n\n      if (_options.resolver) {\n        const { errors, values } = await _executeSchema();\n        _formState.errors = errors;\n        fieldValues = values;\n      } else {\n        await executeBuiltInValidation(_fields);\n      }\n\n      unset(_formState.errors, 'root');\n\n      if (isEmptyObject(_formState.errors)) {\n        _subjects.state.next({\n          errors: {},\n        });\n        try {\n          await onValid(fieldValues as TFieldValues, e);\n        } catch (error) {\n          onValidError = error;\n        }\n      } else {\n        if (onInvalid) {\n          await onInvalid({ ..._formState.errors }, e);\n        }\n        _focusError();\n        setTimeout(_focusError);\n      }\n\n      _subjects.state.next({\n        isSubmitted: true,\n        isSubmitting: false,\n        isSubmitSuccessful: isEmptyObject(_formState.errors) && !onValidError,\n        submitCount: _formState.submitCount + 1,\n        errors: _formState.errors,\n      });\n      if (onValidError) {\n        throw onValidError;\n      }\n    };\n\n  const resetField: UseFormResetField<TFieldValues> = (name, options = {}) => {\n    if (get(_fields, name)) {\n      if (isUndefined(options.defaultValue)) {\n        setValue(name, cloneObject(get(_defaultValues, name)));\n      } else {\n        setValue(\n          name,\n          options.defaultValue as PathValue<\n            TFieldValues,\n            FieldPath<TFieldValues>\n          >,\n        );\n        set(_defaultValues, name, cloneObject(options.defaultValue));\n      }\n\n      if (!options.keepTouched) {\n        unset(_formState.touchedFields, name);\n      }\n\n      if (!options.keepDirty) {\n        unset(_formState.dirtyFields, name);\n        _formState.isDirty = options.defaultValue\n          ? _getDirty(name, cloneObject(get(_defaultValues, name)))\n          : _getDirty();\n      }\n\n      if (!options.keepError) {\n        unset(_formState.errors, name);\n        _proxyFormState.isValid && _updateValid();\n      }\n\n      _subjects.state.next({ ..._formState });\n    }\n  };\n\n  const _reset: UseFormReset<TFieldValues> = (\n    formValues,\n    keepStateOptions = {},\n  ) => {\n    const updatedValues = formValues ? cloneObject(formValues) : _defaultValues;\n    const cloneUpdatedValues = cloneObject(updatedValues);\n    const isEmptyResetValues = isEmptyObject(formValues);\n    const values = isEmptyResetValues ? _defaultValues : cloneUpdatedValues;\n\n    if (!keepStateOptions.keepDefaultValues) {\n      _defaultValues = updatedValues;\n    }\n\n    if (!keepStateOptions.keepValues) {\n      if (keepStateOptions.keepDirtyValues) {\n        for (const fieldName of _names.mount) {\n          get(_formState.dirtyFields, fieldName)\n            ? set(values, fieldName, get(_formValues, fieldName))\n            : setValue(\n                fieldName as FieldPath<TFieldValues>,\n                get(values, fieldName),\n              );\n        }\n      } else {\n        if (isWeb && isUndefined(formValues)) {\n          for (const name of _names.mount) {\n            const field = get(_fields, name);\n            if (field && field._f) {\n              const fieldReference = Array.isArray(field._f.refs)\n                ? field._f.refs[0]\n                : field._f.ref;\n\n              if (isHTMLElement(fieldReference)) {\n                const form = fieldReference.closest('form');\n                if (form) {\n                  form.reset();\n                  break;\n                }\n              }\n            }\n          }\n        }\n\n        _fields = {};\n      }\n\n      _formValues = props.shouldUnregister\n        ? keepStateOptions.keepDefaultValues\n          ? cloneObject(_defaultValues)\n          : {}\n        : cloneObject(values);\n\n      _subjects.array.next({\n        values: { ...values },\n      });\n\n      _subjects.values.next({\n        values: { ...values },\n      });\n    }\n\n    _names = {\n      mount: keepStateOptions.keepDirtyValues ? _names.mount : new Set(),\n      unMount: new Set(),\n      array: new Set(),\n      watch: new Set(),\n      watchAll: false,\n      focus: '',\n    };\n\n    _state.mount =\n      !_proxyFormState.isValid ||\n      !!keepStateOptions.keepIsValid ||\n      !!keepStateOptions.keepDirtyValues;\n\n    _state.watch = !!props.shouldUnregister;\n\n    _subjects.state.next({\n      submitCount: keepStateOptions.keepSubmitCount\n        ? _formState.submitCount\n        : 0,\n      isDirty: isEmptyResetValues\n        ? false\n        : keepStateOptions.keepDirty\n        ? _formState.isDirty\n        : !!(\n            keepStateOptions.keepDefaultValues &&\n            !deepEqual(formValues, _defaultValues)\n          ),\n      isSubmitted: keepStateOptions.keepIsSubmitted\n        ? _formState.isSubmitted\n        : false,\n      dirtyFields: isEmptyResetValues\n        ? []\n        : keepStateOptions.keepDirtyValues\n        ? keepStateOptions.keepDefaultValues && _formValues\n          ? getDirtyFields(_defaultValues, _formValues)\n          : _formState.dirtyFields\n        : keepStateOptions.keepDefaultValues && formValues\n        ? getDirtyFields(_defaultValues, formValues)\n        : {},\n      touchedFields: keepStateOptions.keepTouched\n        ? _formState.touchedFields\n        : {},\n      errors: keepStateOptions.keepErrors ? _formState.errors : {},\n      isSubmitSuccessful: keepStateOptions.keepIsSubmitSuccessful\n        ? _formState.isSubmitSuccessful\n        : false,\n      isSubmitting: false,\n    });\n  };\n\n  const reset: UseFormReset<TFieldValues> = (formValues, keepStateOptions) =>\n    _reset(\n      isFunction(formValues)\n        ? formValues(_formValues as TFieldValues)\n        : formValues,\n      keepStateOptions,\n    );\n\n  const setFocus: UseFormSetFocus<TFieldValues> = (name, options = {}) => {\n    const field = get(_fields, name);\n    const fieldReference = field && field._f;\n\n    if (fieldReference) {\n      const fieldRef = fieldReference.refs\n        ? fieldReference.refs[0]\n        : fieldReference.ref;\n\n      if (fieldRef.focus) {\n        fieldRef.focus();\n        options.shouldSelect && fieldRef.select();\n      }\n    }\n  };\n\n  const _updateFormState = (\n    updatedFormState: Partial<FormState<TFieldValues>>,\n  ) => {\n    _formState = {\n      ..._formState,\n      ...updatedFormState,\n    };\n  };\n\n  const _resetDefaultValues = () =>\n    isFunction(_options.defaultValues) &&\n    _options.defaultValues().then((values) => {\n      reset(values, _options.resetOptions);\n      _subjects.state.next({\n        isLoading: false,\n      });\n    });\n\n  return {\n    control: {\n      register,\n      unregister,\n      getFieldState,\n      handleSubmit,\n      setError,\n      _executeSchema,\n      _getWatch,\n      _getDirty,\n      _updateValid,\n      _removeUnmounted,\n      _updateFieldArray,\n      _updateDisabledField,\n      _getFieldArray,\n      _reset,\n      _resetDefaultValues,\n      _updateFormState,\n      _disableForm,\n      _subjects,\n      _proxyFormState,\n      _setErrors,\n      get _fields() {\n        return _fields;\n      },\n      get _formValues() {\n        return _formValues;\n      },\n      get _state() {\n        return _state;\n      },\n      set _state(value) {\n        _state = value;\n      },\n      get _defaultValues() {\n        return _defaultValues;\n      },\n      get _names() {\n        return _names;\n      },\n      set _names(value) {\n        _names = value;\n      },\n      get _formState() {\n        return _formState;\n      },\n      set _formState(value) {\n        _formState = value;\n      },\n      get _options() {\n        return _options;\n      },\n      set _options(value) {\n        _options = {\n          ..._options,\n          ...value,\n        };\n      },\n    },\n    trigger,\n    register,\n    handleSubmit,\n    watch,\n    setValue,\n    getValues,\n    reset,\n    resetField,\n    clearErrors,\n    unregister,\n    setError,\n    setFocus,\n    getFieldState,\n  };\n}\n", "import React from 'react';\n\nimport { createFormControl } from './logic/createFormControl';\nimport getProxyFormState from './logic/getProxyFormState';\nimport shouldRenderFormState from './logic/shouldRenderFormState';\nimport deepEqual from './utils/deepEqual';\nimport isFunction from './utils/isFunction';\nimport {\n  FieldValues,\n  FormState,\n  InternalFieldName,\n  UseFormProps,\n  UseFormReturn,\n} from './types';\nimport { useSubscribe } from './useSubscribe';\n\n/**\n * Custom hook to manage the entire form.\n *\n * @remarks\n * [API](https://react-hook-form.com/docs/useform) • [Demo](https://codesandbox.io/s/react-hook-form-get-started-ts-5ksmm) • [Video](https://www.youtube.com/watch?v=RkXv4AXXC_4)\n *\n * @param props - form configuration and validation parameters.\n *\n * @returns methods - individual functions to manage the form state. {@link UseFormReturn}\n *\n * @example\n * ```tsx\n * function App() {\n *   const { register, handleSubmit, watch, formState: { errors } } = useForm();\n *   const onSubmit = data => console.log(data);\n *\n *   console.log(watch(\"example\"));\n *\n *   return (\n *     <form onSubmit={handleSubmit(onSubmit)}>\n *       <input defaultValue=\"test\" {...register(\"example\")} />\n *       <input {...register(\"exampleRequired\", { required: true })} />\n *       {errors.exampleRequired && <span>This field is required</span>}\n *       <button>Submit</button>\n *     </form>\n *   );\n * }\n * ```\n */\nexport function useForm<\n  TFieldValues extends FieldValues = FieldValues,\n  TContext = any,\n  TTransformedValues extends FieldValues | undefined = undefined,\n>(\n  props: UseFormProps<TFieldValues, TContext> = {},\n): UseFormReturn<TFieldValues, TContext, TTransformedValues> {\n  const _formControl = React.useRef<\n    UseFormReturn<TFieldValues, TContext, TTransformedValues> | undefined\n  >();\n  const _values = React.useRef<typeof props.values>();\n  const [formState, updateFormState] = React.useState<FormState<TFieldValues>>({\n    isDirty: false,\n    isValidating: false,\n    isLoading: isFunction(props.defaultValues),\n    isSubmitted: false,\n    isSubmitting: false,\n    isSubmitSuccessful: false,\n    isValid: false,\n    submitCount: 0,\n    dirtyFields: {},\n    touchedFields: {},\n    validatingFields: {},\n    errors: props.errors || {},\n    disabled: props.disabled || false,\n    defaultValues: isFunction(props.defaultValues)\n      ? undefined\n      : props.defaultValues,\n  });\n\n  if (!_formControl.current) {\n    _formControl.current = {\n      ...createFormControl(props),\n      formState,\n    };\n  }\n\n  const control = _formControl.current.control;\n  control._options = props;\n\n  useSubscribe({\n    subject: control._subjects.state,\n    next: (\n      value: Partial<FormState<TFieldValues>> & { name?: InternalFieldName },\n    ) => {\n      if (\n        shouldRenderFormState(\n          value,\n          control._proxyFormState,\n          control._updateFormState,\n          true,\n        )\n      ) {\n        updateFormState({ ...control._formState });\n      }\n    },\n  });\n\n  React.useEffect(\n    () => control._disableForm(props.disabled),\n    [control, props.disabled],\n  );\n\n  React.useEffect(() => {\n    if (control._proxyFormState.isDirty) {\n      const isDirty = control._getDirty();\n      if (isDirty !== formState.isDirty) {\n        control._subjects.state.next({\n          isDirty,\n        });\n      }\n    }\n  }, [control, formState.isDirty]);\n\n  React.useEffect(() => {\n    if (props.values && !deepEqual(props.values, _values.current)) {\n      control._reset(props.values, control._options.resetOptions);\n      _values.current = props.values;\n      updateFormState((state) => ({ ...state }));\n    } else {\n      control._resetDefaultValues();\n    }\n  }, [props.values, control]);\n\n  React.useEffect(() => {\n    if (props.errors) {\n      control._setErrors(props.errors);\n    }\n  }, [props.errors, control]);\n\n  React.useEffect(() => {\n    if (!control._state.mount) {\n      control._updateValid();\n      control._state.mount = true;\n    }\n\n    if (control._state.watch) {\n      control._state.watch = false;\n      control._subjects.state.next({ ...control._formState });\n    }\n\n    control._removeUnmounted();\n  });\n\n  React.useEffect(() => {\n    props.shouldUnregister &&\n      control._subjects.values.next({\n        values: control._getWatch(),\n      });\n  }, [props.shouldUnregister, control]);\n\n  _formControl.current.formState = getProxyFormState(formState, control);\n\n  return _formControl.current;\n}\n"], "mappings": ";AAEA,IAAAA,eAAA,GAAgBC,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,UAAU;ACH7B,IAAAC,YAAA,GAAgBC,KAAc,IAAoBA,KAAK,YAAYC,IAAI;ACAvE,IAAAC,iBAAA,GAAgBF,KAAc,IAAgCA,KAAK,IAAI,IAAI;ACGpE,MAAMG,YAAY,GAAIH,KAAc,IACzC,OAAOA,KAAK,KAAK,QAAQ;AAE3B,IAAAI,QAAA,GAAkCJ,KAAc,IAC9C,CAACE,iBAAiB,CAACF,KAAK,CAAC,IACzB,CAACK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,IACrBG,YAAY,CAACH,KAAK,CAAC,IACnB,CAACD,YAAY,CAACC,KAAK,CAAC;ACLtB,IAAAO,aAAA,GAAgBC,KAAc,IAC5BJ,QAAQ,CAACI,KAAK,CAAC,IAAKA,KAAe,CAACC,MAAM,GACtCb,eAAe,CAAEY,KAAe,CAACC,MAAM,CAAC,GACrCD,KAAe,CAACC,MAAM,CAACC,OAAO,GAC9BF,KAAe,CAACC,MAAM,CAACT,KAAK,GAC/BQ,KAAK;ACVX,IAAAG,iBAAA,GAAgBC,IAAY,IAC1BA,IAAI,CAACC,SAAS,CAAC,CAAC,EAAED,IAAI,CAACE,MAAM,CAAC,aAAa,CAAC,CAAC,IAAIF,IAAI;ACGvD,IAAAG,kBAAA,GAAeA,CAACC,KAA6B,EAAEJ,IAAuB,KACpEI,KAAK,CAACC,GAAG,CAACN,iBAAiB,CAACC,IAAI,CAAC,CAAC;ACHpC,IAAAM,aAAA,GAAgBC,UAAkB,IAAI;EACpC,MAAMC,aAAa,GACjBD,UAAU,CAACE,WAAW,IAAIF,UAAU,CAACE,WAAW,CAACC,SAAS;EAE5D,OACElB,QAAQ,CAACgB,aAAa,CAAC,IAAIA,aAAa,CAACG,cAAc,CAAC,eAAe,CAAC;AAE5E,CAAC;ACTD,IAAAC,KAAA,GAAe,OAAOC,MAAM,KAAK,WAAW,IAC1C,OAAOA,MAAM,CAACC,WAAW,KAAK,WAAW,IACzC,OAAOC,QAAQ,KAAK,WAAW;ACET,SAAAC,WAAWA,CAAIC,IAAO;EAC5C,IAAIC,IAAS;EACb,MAAMxB,OAAO,GAAGD,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EAEnC,IAAIA,IAAI,YAAY5B,IAAI,EAAE;IACxB6B,IAAI,GAAG,IAAI7B,IAAI,CAAC4B,IAAI,CAAC;GACtB,MAAM,IAAIA,IAAI,YAAYE,GAAG,EAAE;IAC9BD,IAAI,GAAG,IAAIC,GAAG,CAACF,IAAI,CAAC;GACrB,MAAM,IACL,EAAEL,KAAK,KAAKK,IAAI,YAAYG,IAAI,IAAIH,IAAI,YAAYI,QAAQ,CAAC,CAAC,KAC7D3B,OAAO,IAAIF,QAAQ,CAACyB,IAAI,CAAC,CAAC,EAC3B;IACAC,IAAI,GAAGxB,OAAO,GAAG,EAAE,GAAG,EAAE;IAExB,IAAI,CAACA,OAAO,IAAI,CAACY,aAAa,CAACW,IAAI,CAAC,EAAE;MACpCC,IAAI,GAAGD,IAAI;KACZ,MAAM;MACL,KAAK,MAAMK,GAAG,IAAIL,IAAI,EAAE;QACtB,IAAIA,IAAI,CAACN,cAAc,CAACW,GAAG,CAAC,EAAE;UAC5BJ,IAAI,CAACI,GAAG,CAAC,GAAGN,WAAW,CAACC,IAAI,CAACK,GAAG,CAAC,CAAC;;;;GAIzC,MAAM;IACL,OAAOL,IAAI;;EAGb,OAAOC,IAAI;AACb;AChCA,IAAAK,OAAA,GAAwBnC,KAAe,IACrCK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,CAACoC,MAAM,CAACC,OAAO,CAAC,GAAG,EAAE;ACDnD,IAAAC,WAAA,GAAgBC,GAAY,IAAuBA,GAAG,KAAKC,SAAS;ACKpE,IAAAC,GAAA,GAAeA,CAAIC,MAAS,EAAEC,IAAa,EAAEC,YAAsB,KAAS;EAC1E,IAAI,CAACD,IAAI,IAAI,CAACvC,QAAQ,CAACsC,MAAM,CAAC,EAAE;IAC9B,OAAOE,YAAY;;EAGrB,MAAMC,MAAM,GAAGV,OAAO,CAACQ,IAAI,CAACG,KAAK,CAAC,WAAW,CAAC,CAAC,CAACC,MAAM,CACpD,CAACF,MAAM,EAAEX,GAAG,KACVhC,iBAAiB,CAAC2C,MAAM,CAAC,GAAGA,MAAM,GAAGA,MAAM,CAACX,GAAe,CAAC,EAC9DQ,MAAM,CACP;EAED,OAAOJ,WAAW,CAACO,MAAM,CAAC,IAAIA,MAAM,KAAKH,MAAM,GAC3CJ,WAAW,CAACI,MAAM,CAACC,IAAe,CAAC,CAAC,GAClCC,YAAY,GACZF,MAAM,CAACC,IAAe,CAAC,GACzBE,MAAM;AACZ,CAAC;ACrBD,IAAAG,SAAA,GAAgBhD,KAAc,IAAuB,OAAOA,KAAK,KAAK,SAAS;ACAxE,MAAMiD,MAAM,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,SAAS,EAAE,UAAU;EACrBC,MAAM,EAAE;CACA;AAEH,MAAMC,eAAe,GAAG;EAC7BC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE,UAAU;EACpBC,SAAS,EAAE,WAAW;EACtBC,GAAG,EAAE;CACG;AAEH,MAAMC,sBAAsB,GAAG;EACpCC,GAAG,EAAE,KAAK;EACVC,GAAG,EAAE,KAAK;EACVC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,QAAQ,EAAE,UAAU;EACpBC,QAAQ,EAAE;CACF;AClBV,MAAMC,eAAe,GAAGC,KAAK,CAACC,aAAa,CAAuB,IAAI,CAAC;AAEvE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACI,MAAMC,cAAc,GAAGA,CAAA,KAK5BF,KAAK,CAACG,UAAU,CAACJ,eAAe;AAMlC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACU,MAAAK,YAAY,GAKvBC,KAAoE,IAClE;EACF,MAAM;IAAEC,QAAQ;IAAE,GAAG7C;EAAI,CAAE,GAAG4C,KAAK;EACnC,OACEL,KAAA,CAAAO,aAAA,CAACR,eAAe,CAACS,QAAQ;IAAC5E,KAAK,EAAE6B;EAAgC,GAC9D6C,QAAQ,CACgB;AAE/B;ACvFA,IAAAG,iBAAA,GAAe,SAAAA,CACbC,SAAkC,EAClCC,OAAwC,EACxCC,mBAAmC,EAEjC;EAAA,IADFC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAG,IAAI;EAEb,MAAMrC,MAAM,GAAG;IACbuC,aAAa,EAAEL,OAAO,CAACM;GACJ;EAErB,KAAK,MAAMnD,GAAG,IAAI4C,SAAS,EAAE;IAC3BQ,MAAM,CAACC,cAAc,CAAC1C,MAAM,EAAEX,GAAG,EAAE;MACjCO,GAAG,EAAEA,CAAA,KAAK;QACR,MAAM+C,IAAI,GAAGtD,GAA0D;QAEvE,IAAI6C,OAAO,CAACU,eAAe,CAACD,IAAI,CAAC,KAAKnC,eAAe,CAACK,GAAG,EAAE;UACzDqB,OAAO,CAACU,eAAe,CAACD,IAAI,CAAC,GAAG,CAACP,MAAM,IAAI5B,eAAe,CAACK,GAAG;;QAGhEsB,mBAAmB,KAAKA,mBAAmB,CAACQ,IAAI,CAAC,GAAG,IAAI,CAAC;QACzD,OAAOV,SAAS,CAACU,IAAI,CAAC;;IAEzB,EAAC;;EAGJ,OAAO3C,MAAM;AACf,CAAC;ACzBD,IAAA6C,aAAA,GAAgB1F,KAAc,IAC5BI,QAAQ,CAACJ,KAAK,CAAC,IAAI,CAACsF,MAAM,CAACK,IAAI,CAAC3F,KAAK,CAAC,CAACmF,MAAM;ACK/C,IAAAS,qBAAA,GAAeA,CACbC,aAAmE,EACnEJ,eAAkB,EAClBK,eAA+C,EAC/Cb,MAAgB,KACd;EACFa,eAAe,CAACD,aAAa,CAAC;EAC9B,MAAM;IAAEjF,IAAI;IAAE,GAAGkE;EAAS,CAAE,GAAGe,aAAa;EAE5C,OACEH,aAAa,CAACZ,SAAS,CAAC,IACxBQ,MAAM,CAACK,IAAI,CAACb,SAAS,CAAC,CAACK,MAAM,IAAIG,MAAM,CAACK,IAAI,CAACF,eAAe,CAAC,CAACN,MAAM,IACpEG,MAAM,CAACK,IAAI,CAACb,SAAS,CAAC,CAACiB,IAAI,CACxB7D,GAAG,IACFuD,eAAe,CAACvD,GAA0B,CAAC,MAC1C,CAAC+C,MAAM,IAAI5B,eAAe,CAACK,GAAG,CAAC,CACnC;AAEL,CAAC;AC5BD,IAAAsC,qBAAA,GAAmBhG,KAAQ,IAAMK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAE;ACExE,IAAAiG,qBAAA,GAAeA,CACbrF,IAAQ,EACRsF,UAAmB,EACnBC,KAAe,KAEf,CAACvF,IAAI,IACL,CAACsF,UAAU,IACXtF,IAAI,KAAKsF,UAAU,IACnBF,qBAAqB,CAACpF,IAAI,CAAC,CAACwF,IAAI,CAC7BC,WAAW,IACVA,WAAW,KACVF,KAAK,GACFE,WAAW,KAAKH,UAAU,GAC1BG,WAAW,CAACC,UAAU,CAACJ,UAAU,CAAC,IAClCA,UAAU,CAACI,UAAU,CAACD,WAAW,CAAC,CAAC,CAC1C;ACPG,SAAUE,YAAYA,CAAI9B,KAAe;EAC7C,MAAM+B,MAAM,GAAGpC,KAAK,CAACqC,MAAM,CAAChC,KAAK,CAAC;EAClC+B,MAAM,CAACE,OAAO,GAAGjC,KAAK;EAEtBL,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,MAAMC,YAAY,GAChB,CAACnC,KAAK,CAACoC,QAAQ,IACfL,MAAM,CAACE,OAAO,CAACI,OAAO,IACtBN,MAAM,CAACE,OAAO,CAACI,OAAO,CAACC,SAAS,CAAC;MAC/BC,IAAI,EAAER,MAAM,CAACE,OAAO,CAACM;IACtB,EAAC;IAEJ,OAAO,MAAK;MACVJ,YAAY,IAAIA,YAAY,CAACK,WAAW,EAAE;IAC5C,CAAC;EACH,CAAC,EAAE,CAACxC,KAAK,CAACoC,QAAQ,CAAC,CAAC;AACtB;;ACXA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BG;AACH,SAASK,YAAYA,CACnBzC,KAAuC;EAEvC,MAAM0C,OAAO,GAAG7C,cAAc,EAAgB;EAC9C,MAAM;IAAES,OAAO,GAAGoC,OAAO,CAACpC,OAAO;IAAE8B,QAAQ;IAAEjG,IAAI;IAAEuF;EAAK,CAAE,GAAG1B,KAAK,IAAI,EAAE;EACxE,MAAM,CAACK,SAAS,EAAEgB,eAAe,CAAC,GAAG1B,KAAK,CAACgD,QAAQ,CAACrC,OAAO,CAACsC,UAAU,CAAC;EACvE,MAAMC,QAAQ,GAAGlD,KAAK,CAACqC,MAAM,CAAC,IAAI,CAAC;EACnC,MAAMc,oBAAoB,GAAGnD,KAAK,CAACqC,MAAM,CAAC;IACxCe,OAAO,EAAE,KAAK;IACdC,SAAS,EAAE,KAAK;IAChBC,WAAW,EAAE,KAAK;IAClBC,aAAa,EAAE,KAAK;IACpBC,gBAAgB,EAAE,KAAK;IACvBC,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;EACT,EAAC;EACF,MAAMC,KAAK,GAAG5D,KAAK,CAACqC,MAAM,CAAC7F,IAAI,CAAC;EAEhCoH,KAAK,CAACtB,OAAO,GAAG9F,IAAI;EAEpB2F,YAAY,CAAC;IACXM,QAAQ;IACRG,IAAI,EACFhH,KAAsE,IAEtEsH,QAAQ,CAACZ,OAAO,IAChBT,qBAAqB,CACnB+B,KAAK,CAACtB,OAA4B,EAClC1G,KAAK,CAACY,IAAI,EACVuF,KAAK,CACN,IACDP,qBAAqB,CACnB5F,KAAK,EACLuH,oBAAoB,CAACb,OAAO,EAC5B3B,OAAO,CAACkD,gBAAgB,CACzB,IACDnC,eAAe,CAAC;MACd,GAAGf,OAAO,CAACsC,UAAU;MACrB,GAAGrH;KACJ,CAAC;IACJ8G,OAAO,EAAE/B,OAAO,CAACmD,SAAS,CAACC;EAC5B,EAAC;EAEF/D,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnBW,QAAQ,CAACZ,OAAO,GAAG,IAAI;IACvBa,oBAAoB,CAACb,OAAO,CAACoB,OAAO,IAAI/C,OAAO,CAACqD,YAAY,CAAC,IAAI,CAAC;IAElE,OAAO,MAAK;MACVd,QAAQ,CAACZ,OAAO,GAAG,KAAK;IAC1B,CAAC;EACH,CAAC,EAAE,CAAC3B,OAAO,CAAC,CAAC;EAEb,OAAOF,iBAAiB,CACtBC,SAAS,EACTC,OAAO,EACPwC,oBAAoB,CAACb,OAAO,EAC5B,KAAK,CACN;AACH;ACxGA,IAAA2B,QAAA,GAAgBrI,KAAc,IAAsB,OAAOA,KAAK,KAAK,QAAQ;ACI7E,IAAAsI,mBAAA,GAAeA,CACbtH,KAAoC,EACpCuH,MAAa,EACbC,UAAwB,EACxBC,QAAkB,EAClB7F,YAAuC,KACrC;EACF,IAAIyF,QAAQ,CAACrH,KAAK,CAAC,EAAE;IACnByH,QAAQ,IAAIF,MAAM,CAACG,KAAK,CAACC,GAAG,CAAC3H,KAAK,CAAC;IACnC,OAAOyB,GAAG,CAAC+F,UAAU,EAAExH,KAAK,EAAE4B,YAAY,CAAC;;EAG7C,IAAIvC,KAAK,CAACC,OAAO,CAACU,KAAK,CAAC,EAAE;IACxB,OAAOA,KAAK,CAAC4H,GAAG,CACbC,SAAS,KACRJ,QAAQ,IAAIF,MAAM,CAACG,KAAK,CAACC,GAAG,CAACE,SAAS,CAAC,EAAEpG,GAAG,CAAC+F,UAAU,EAAEK,SAAS,CAAC,CACpE,CACF;;EAGHJ,QAAQ,KAAKF,MAAM,CAACO,QAAQ,GAAG,IAAI,CAAC;EAEpC,OAAON,UAAU;AACnB,CAAC;;ACmGD;;;;;;;;;;;;;;;AAeG;AACG,SAAUO,QAAQA,CACtBtE,KAAmC;EAEnC,MAAM0C,OAAO,GAAG7C,cAAc,EAAE;EAChC,MAAM;IACJS,OAAO,GAAGoC,OAAO,CAACpC,OAAO;IACzBnE,IAAI;IACJgC,YAAY;IACZiE,QAAQ;IACRV;EAAK,CACN,GAAG1B,KAAK,IAAI,EAAE;EACf,MAAMuD,KAAK,GAAG5D,KAAK,CAACqC,MAAM,CAAC7F,IAAI,CAAC;EAEhCoH,KAAK,CAACtB,OAAO,GAAG9F,IAAI;EAEpB2F,YAAY,CAAC;IACXM,QAAQ;IACRC,OAAO,EAAE/B,OAAO,CAACmD,SAAS,CAACc,MAAM;IACjChC,IAAI,EAAGlC,SAA6D,IAAI;MACtE,IACEmB,qBAAqB,CACnB+B,KAAK,CAACtB,OAA4B,EAClC5B,SAAS,CAAClE,IAAI,EACduF,KAAK,CACN,EACD;QACA8C,WAAW,CACTrH,WAAW,CACT0G,mBAAmB,CACjBN,KAAK,CAACtB,OAAkD,EACxD3B,OAAO,CAACwD,MAAM,EACdzD,SAAS,CAACkE,MAAM,IAAIjE,OAAO,CAACmE,WAAW,EACvC,KAAK,EACLtG,YAAY,CACb,CACF,CACF;;;EAGN,EAAC;EAEF,MAAM,CAAC5C,KAAK,EAAEiJ,WAAW,CAAC,GAAG7E,KAAK,CAACgD,QAAQ,CACzCrC,OAAO,CAACoE,SAAS,CACfvI,IAAyB,EACzBgC,YAAqD,CACtD,CACF;EAEDwB,KAAK,CAACuC,SAAS,CAAC,MAAM5B,OAAO,CAACqE,gBAAgB,EAAE,CAAC;EAEjD,OAAOpJ,KAAK;AACd;ACjMA,IAAAqJ,KAAA,GAAgBrJ,KAAa,IAAK,OAAO,CAACsJ,IAAI,CAACtJ,KAAK,CAAC;ACErD,IAAAuJ,YAAA,GAAgBC,KAAa,IAC3BrH,OAAO,CAACqH,KAAK,CAACC,OAAO,CAAC,WAAW,EAAE,EAAE,CAAC,CAAC3G,KAAK,CAAC,OAAO,CAAC,CAAC;ACGxD,IAAA4G,GAAA,GAAeA,CAAChH,MAAmB,EAAEC,IAAY,EAAE3C,KAAe,KAAI;EACpE,IAAI2J,KAAK,GAAG,CAAC,CAAC;EACd,MAAMC,QAAQ,GAAGP,KAAK,CAAC1G,IAAI,CAAC,GAAG,CAACA,IAAI,CAAC,GAAG4G,YAAY,CAAC5G,IAAI,CAAC;EAC1D,MAAMwC,MAAM,GAAGyE,QAAQ,CAACzE,MAAM;EAC9B,MAAM0E,SAAS,GAAG1E,MAAM,GAAG,CAAC;EAE5B,OAAO,EAAEwE,KAAK,GAAGxE,MAAM,EAAE;IACvB,MAAMjD,GAAG,GAAG0H,QAAQ,CAACD,KAAK,CAAC;IAC3B,IAAIG,QAAQ,GAAG9J,KAAK;IAEpB,IAAI2J,KAAK,KAAKE,SAAS,EAAE;MACvB,MAAME,QAAQ,GAAGrH,MAAM,CAACR,GAAG,CAAC;MAC5B4H,QAAQ,GACN1J,QAAQ,CAAC2J,QAAQ,CAAC,IAAI1J,KAAK,CAACC,OAAO,CAACyJ,QAAQ,CAAC,GACzCA,QAAQ,GACR,CAACC,KAAK,CAAC,CAACJ,QAAQ,CAACD,KAAK,GAAG,CAAC,CAAC,CAAC,GAC5B,EAAE,GACF,EAAE;;IAEVjH,MAAM,CAACR,GAAG,CAAC,GAAG4H,QAAQ;IACtBpH,MAAM,GAAGA,MAAM,CAACR,GAAG,CAAC;;EAEtB,OAAOQ,MAAM;AACf,CAAC;;ACLD;;;;;;;;;;;;;;;;;;;;;;;AAuBG;AACG,SAAUuH,aAAaA,CAI3BxF,KAA8C;EAE9C,MAAM0C,OAAO,GAAG7C,cAAc,EAAgB;EAC9C,MAAM;IAAE1D,IAAI;IAAEiG,QAAQ;IAAE9B,OAAO,GAAGoC,OAAO,CAACpC,OAAO;IAAEmF;EAAgB,CAAE,GAAGzF,KAAK;EAC7E,MAAM0F,YAAY,GAAGpJ,kBAAkB,CAACgE,OAAO,CAACwD,MAAM,CAAC6B,KAAK,EAAExJ,IAAI,CAAC;EACnE,MAAMZ,KAAK,GAAG+I,QAAQ,CAAC;IACrBhE,OAAO;IACPnE,IAAI;IACJgC,YAAY,EAAEH,GAAG,CACfsC,OAAO,CAACmE,WAAW,EACnBtI,IAAI,EACJ6B,GAAG,CAACsC,OAAO,CAACM,cAAc,EAAEzE,IAAI,EAAE6D,KAAK,CAAC7B,YAAY,CAAC,CACtD;IACDuD,KAAK,EAAE;EACR,EAAwC;EACzC,MAAMrB,SAAS,GAAGoC,YAAY,CAAC;IAC7BnC,OAAO;IACPnE;EACD,EAAC;EAEF,MAAMyJ,cAAc,GAAGjG,KAAK,CAACqC,MAAM,CACjC1B,OAAO,CAACuF,QAAQ,CAAC1J,IAAI,EAAE;IACrB,GAAG6D,KAAK,CAAC8F,KAAK;IACdvK,KAAK;IACL,IAAIgD,SAAS,CAACyB,KAAK,CAACoC,QAAQ,CAAC,GAAG;MAAEA,QAAQ,EAAEpC,KAAK,CAACoC;IAAQ,CAAE,GAAG,EAAE;EAClE,EAAC,CACH;EAEDzC,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,MAAM6D,sBAAsB,GAC1BzF,OAAO,CAAC0F,QAAQ,CAACP,gBAAgB,IAAIA,gBAAgB;IAEvD,MAAMQ,aAAa,GAAGA,CAAC9J,IAAuB,EAAEZ,KAAc,KAAI;MAChE,MAAM2K,KAAK,GAAUlI,GAAG,CAACsC,OAAO,CAAC6F,OAAO,EAAEhK,IAAI,CAAC;MAE/C,IAAI+J,KAAK,EAAE;QACTA,KAAK,CAACE,EAAE,CAACC,KAAK,GAAG9K,KAAK;;IAE1B,CAAC;IAED0K,aAAa,CAAC9J,IAAI,EAAE,IAAI,CAAC;IAEzB,IAAI4J,sBAAsB,EAAE;MAC1B,MAAMxK,KAAK,GAAG4B,WAAW,CAACa,GAAG,CAACsC,OAAO,CAAC0F,QAAQ,CAACrF,aAAa,EAAExE,IAAI,CAAC,CAAC;MACpE8I,GAAG,CAAC3E,OAAO,CAACM,cAAc,EAAEzE,IAAI,EAAEZ,KAAK,CAAC;MACxC,IAAIsC,WAAW,CAACG,GAAG,CAACsC,OAAO,CAACmE,WAAW,EAAEtI,IAAI,CAAC,CAAC,EAAE;QAC/C8I,GAAG,CAAC3E,OAAO,CAACmE,WAAW,EAAEtI,IAAI,EAAEZ,KAAK,CAAC;;;IAIzC,OAAO,MAAK;MACV,CACEmK,YAAY,GACRK,sBAAsB,IAAI,CAACzF,OAAO,CAACgG,MAAM,CAACC,MAAM,GAChDR,sBAAsB,IAExBzF,OAAO,CAACkG,UAAU,CAACrK,IAAI,CAAC,GACxB8J,aAAa,CAAC9J,IAAI,EAAE,KAAK,CAAC;IAChC,CAAC;GACF,EAAE,CAACA,IAAI,EAAEmE,OAAO,EAAEoF,YAAY,EAAED,gBAAgB,CAAC,CAAC;EAEnD9F,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,IAAIlE,GAAG,CAACsC,OAAO,CAAC6F,OAAO,EAAEhK,IAAI,CAAC,EAAE;MAC9BmE,OAAO,CAACmG,oBAAoB,CAAC;QAC3BrE,QAAQ;QACRsE,MAAM,EAAEpG,OAAO,CAAC6F,OAAO;QACvBhK,IAAI;QACJZ,KAAK,EAAEyC,GAAG,CAACsC,OAAO,CAAC6F,OAAO,EAAEhK,IAAI,CAAC,CAACiK,EAAE,CAAC7K;MACtC,EAAC;;GAEL,EAAE,CAAC6G,QAAQ,EAAEjG,IAAI,EAAEmE,OAAO,CAAC,CAAC;EAE7B,OAAO;IACL4F,KAAK,EAAE;MACL/J,IAAI;MACJZ,KAAK;MACL,IAAIgD,SAAS,CAAC6D,QAAQ,CAAC,IAAI/B,SAAS,CAAC+B,QAAQ,GACzC;QAAEA,QAAQ,EAAE/B,SAAS,CAAC+B,QAAQ,IAAIA;MAAQ,CAAE,GAC5C,EAAE,CAAC;MACPtD,QAAQ,EAAEa,KAAK,CAACgH,WAAW,CACxB5K,KAAK,IACJ6J,cAAc,CAAC3D,OAAO,CAACnD,QAAQ,CAAC;QAC9B9C,MAAM,EAAE;UACNT,KAAK,EAAEO,aAAa,CAACC,KAAK,CAAC;UAC3BI,IAAI,EAAEA;QACP;QACDd,IAAI,EAAEmD,MAAM,CAACG;MACd,EAAC,EACJ,CAACxC,IAAI,CAAC,CACP;MACD0C,MAAM,EAAEc,KAAK,CAACgH,WAAW,CACvB,MACEf,cAAc,CAAC3D,OAAO,CAACpD,MAAM,CAAC;QAC5B7C,MAAM,EAAE;UACNT,KAAK,EAAEyC,GAAG,CAACsC,OAAO,CAACmE,WAAW,EAAEtI,IAAI,CAAC;UACrCA,IAAI,EAAEA;QACP;QACDd,IAAI,EAAEmD,MAAM,CAACC;MACd,EAAC,EACJ,CAACtC,IAAI,EAAEmE,OAAO,CAAC,CAChB;MACDsG,GAAG,EAAGC,GAAG,IAAI;QACX,MAAMX,KAAK,GAAGlI,GAAG,CAACsC,OAAO,CAAC6F,OAAO,EAAEhK,IAAI,CAAC;QAExC,IAAI+J,KAAK,IAAIW,GAAG,EAAE;UAChBX,KAAK,CAACE,EAAE,CAACQ,GAAG,GAAG;YACbE,KAAK,EAAEA,CAAA,KAAMD,GAAG,CAACC,KAAK,EAAE;YACxBC,MAAM,EAAEA,CAAA,KAAMF,GAAG,CAACE,MAAM,EAAE;YAC1BC,iBAAiB,EAAGC,OAAe,IACjCJ,GAAG,CAACG,iBAAiB,CAACC,OAAO,CAAC;YAChCC,cAAc,EAAEA,CAAA,KAAML,GAAG,CAACK,cAAc;WACzC;;;IAGN;IACD7G,SAAS;IACT8G,UAAU,EAAEtG,MAAM,CAACuG,gBAAgB,CACjC,EAAE,EACF;MACEC,OAAO,EAAE;QACPC,UAAU,EAAE,IAAI;QAChBtJ,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAACqC,SAAS,CAACiD,MAAM,EAAEnH,IAAI;MACxC;MACD4G,OAAO,EAAE;QACPuE,UAAU,EAAE,IAAI;QAChBtJ,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAACqC,SAAS,CAAC4C,WAAW,EAAE9G,IAAI;MAC7C;MACDoL,SAAS,EAAE;QACTD,UAAU,EAAE,IAAI;QAChBtJ,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAACqC,SAAS,CAAC6C,aAAa,EAAE/G,IAAI;MAC/C;MACDiH,YAAY,EAAE;QACZkE,UAAU,EAAE,IAAI;QAChBtJ,GAAG,EAAEA,CAAA,KAAM,CAAC,CAACA,GAAG,CAACqC,SAAS,CAAC8C,gBAAgB,EAAEhH,IAAI;MAClD;MACDqL,KAAK,EAAE;QACLF,UAAU,EAAE,IAAI;QAChBtJ,GAAG,EAAEA,CAAA,KAAMA,GAAG,CAACqC,SAAS,CAACiD,MAAM,EAAEnH,IAAI;MACtC;KACF;GAEJ;AACH;;AC/LA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyCG;AACH,MAAMsL,UAAU,GAIdzH,KAA2C,IACxCA,KAAK,CAAC0H,MAAM,CAAClC,aAAa,CAAsBxF,KAAK,CAAC;AC5C3D,MAAM2H,YAAY,GAAG,MAAM;AAE3B;;;;;;;;;;;;;;;;;;;;;AAqBG;AACH,SAASC,IAAIA,CAGX5H,KAAsB;EACtB,MAAM0C,OAAO,GAAG7C,cAAc,EAAK;EACnC,MAAM,CAACgI,OAAO,EAAEC,UAAU,CAAC,GAAGnI,KAAK,CAACgD,QAAQ,CAAC,KAAK,CAAC;EACnD,MAAM;IACJrC,OAAO,GAAGoC,OAAO,CAACpC,OAAO;IACzBvB,QAAQ;IACRkB,QAAQ;IACRsG,MAAM;IACNwB,MAAM,GAAGJ,YAAY;IACrBK,OAAO;IACPC,OAAO;IACPC,OAAO;IACPR,MAAM;IACNS,SAAS;IACTC,cAAc;IACd,GAAGC;EAAI,CACR,GAAGrI,KAAK;EAET,MAAMsI,MAAM,GAAG,MAAOvM,KAAgC,IAAI;IACxD,IAAIwM,QAAQ,GAAG,KAAK;IACpB,IAAIlN,IAAI,GAAG,EAAE;IAEb,MAAMiF,OAAO,CAACkI,YAAY,CAAC,MAAOpL,IAAI,IAAI;MACxC,MAAMqL,QAAQ,GAAG,IAAIC,QAAQ,EAAE;MAC/B,IAAIC,YAAY,GAAG,EAAE;MAErB,IAAI;QACFA,YAAY,GAAGC,IAAI,CAACC,SAAS,CAACzL,IAAI,CAAC;OACpC,CAAC,OAAM0L,EAAA;MAER,KAAK,MAAM3M,IAAI,IAAImE,OAAO,CAACwD,MAAM,CAACuC,KAAK,EAAE;QACvCoC,QAAQ,CAACM,MAAM,CAAC5M,IAAI,EAAE6B,GAAG,CAACZ,IAAI,EAAEjB,IAAI,CAAC,CAAC;;MAGxC,IAAI4C,QAAQ,EAAE;QACZ,MAAMA,QAAQ,CAAC;UACb3B,IAAI;UACJrB,KAAK;UACLgM,MAAM;UACNU,QAAQ;UACRE;QACD,EAAC;;MAGJ,IAAIpC,MAAM,EAAE;QACV,IAAI;UACF,MAAMyC,6BAA6B,GAAG,CACpChB,OAAO,IAAIA,OAAO,CAAC,cAAc,CAAC,EAClCC,OAAO,CACR,CAACtG,IAAI,CAAEpG,KAAK,IAAKA,KAAK,IAAIA,KAAK,CAAC0N,QAAQ,CAAC,MAAM,CAAC,CAAC;UAElD,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC5C,MAAM,EAAE;YACnCwB,MAAM;YACNC,OAAO,EAAE;cACP,GAAGA,OAAO;cACV,IAAIC,OAAO,GAAG;gBAAE,cAAc,EAAEA;cAAO,CAAE,GAAG,EAAE;YAC/C;YACDmB,IAAI,EAAEJ,6BAA6B,GAAGL,YAAY,GAAGF;UACtD,EAAC;UAEF,IACES,QAAQ,KACPd,cAAc,GACX,CAACA,cAAc,CAACc,QAAQ,CAACG,MAAM,CAAC,GAChCH,QAAQ,CAACG,MAAM,GAAG,GAAG,IAAIH,QAAQ,CAACG,MAAM,IAAI,GAAG,CAAC,EACpD;YACAd,QAAQ,GAAG,IAAI;YACfL,OAAO,IAAIA,OAAO,CAAC;cAAEgB;YAAQ,CAAE,CAAC;YAChC7N,IAAI,GAAGiO,MAAM,CAACJ,QAAQ,CAACG,MAAM,CAAC;WAC/B,MAAM;YACLlB,SAAS,IAAIA,SAAS,CAAC;cAAEe;YAAQ,CAAE,CAAC;;SAEvC,CAAC,OAAO1B,KAAc,EAAE;UACvBe,QAAQ,GAAG,IAAI;UACfL,OAAO,IAAIA,OAAO,CAAC;YAAEV;UAAK,CAAE,CAAC;;;IAGnC,CAAC,CAAC,CAACzL,KAAK,CAAC;IAET,IAAIwM,QAAQ,IAAIvI,KAAK,CAACM,OAAO,EAAE;MAC7BN,KAAK,CAACM,OAAO,CAACmD,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;QACjCgH,kBAAkB,EAAE;MACrB,EAAC;MACFvJ,KAAK,CAACM,OAAO,CAACkJ,QAAQ,CAAC,aAAa,EAAE;QACpCnO;MACD,EAAC;;EAEN,CAAC;EAEDsE,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB4F,UAAU,CAAC,IAAI,CAAC;GACjB,EAAE,EAAE,CAAC;EAEN,OAAOJ,MAAM,GACX/H,KAAA,CAAAO,aAAA,CAAAP,KAAA,CAAA8J,QAAA,QACG/B,MAAM,CAAC;IACNY;EACD,EAAC,CACD,GAEH3I,KAAA,CAAAO,aAAA;IACEwJ,UAAU,EAAE7B,OAAO;IACnBtB,MAAM,EAAEA,MAAM;IACdwB,MAAM,EAAEA,MAAM;IACdE,OAAO,EAAEA,OAAO;IAChBlJ,QAAQ,EAAEuJ,MAAM;IAAA,GACZD;EAAI,GAEPpI,QAAQ,CAEZ;AACH;AC1IA,IAAA0J,YAAA,GAAeA,CACbxN,IAAuB,EACvByN,wBAAiC,EACjCtG,MAA2B,EAC3BjI,IAAY,EACZ4L,OAAuB,KAEvB2C,wBAAwB,GACpB;EACE,GAAGtG,MAAM,CAACnH,IAAI,CAAC;EACf0N,KAAK,EAAE;IACL,IAAIvG,MAAM,CAACnH,IAAI,CAAC,IAAImH,MAAM,CAACnH,IAAI,CAAE,CAAC0N,KAAK,GAAGvG,MAAM,CAACnH,IAAI,CAAE,CAAC0N,KAAK,GAAG,EAAE,CAAC;IACnE,CAACxO,IAAI,GAAG4L,OAAO,IAAI;EACpB;AACF,IACD,EAAE;ACrBR,IAAA6C,UAAA,GAAeA,CAAA,KAAK;EAClB,MAAMC,CAAC,GACL,OAAOC,WAAW,KAAK,WAAW,GAAGxO,IAAI,CAACyO,GAAG,EAAE,GAAGD,WAAW,CAACC,GAAG,EAAE,GAAG,IAAI;EAE5E,OAAO,sCAAsC,CAACjF,OAAO,CAAC,OAAO,EAAGkF,CAAC,IAAI;IACnE,MAAMC,CAAC,GAAG,CAACC,IAAI,CAACC,MAAM,EAAE,GAAG,EAAE,GAAGN,CAAC,IAAI,EAAE,GAAG,CAAC;IAE3C,OAAO,CAACG,CAAC,IAAI,GAAG,GAAGC,CAAC,GAAIA,CAAC,GAAG,GAAG,GAAI,GAAG,EAAEG,QAAQ,CAAC,EAAE,CAAC;EACtD,CAAC,CAAC;AACJ,CAAC;ACND,IAAAC,iBAAA,GAAe,SAAAA,CACbpO,IAAuB,EACvB+I,KAAa;EAAA,IACbsF,OAAA,GAAA/J,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAiC,EAAE;EAAA,OAEnC+J,OAAO,CAACC,WAAW,IAAI5M,WAAW,CAAC2M,OAAO,CAACC,WAAW,CAAC,GACnDD,OAAO,CAACE,SAAS,OAAAC,MAAA,CACdxO,IAAI,OAAAwO,MAAA,CAAI9M,WAAW,CAAC2M,OAAO,CAACI,UAAU,CAAC,GAAG1F,KAAK,GAAGsF,OAAO,CAACI,UAAU,MAAG,GAC1E,EAAE;AAAA;ACRR,IAAAC,kBAAA,GAAgBC,IAAW,KAA2B;EACpDC,UAAU,EAAE,CAACD,IAAI,IAAIA,IAAI,KAAKlM,eAAe,CAACG,QAAQ;EACtDiM,QAAQ,EAAEF,IAAI,KAAKlM,eAAe,CAACC,MAAM;EACzCoM,UAAU,EAAEH,IAAI,KAAKlM,eAAe,CAACE,QAAQ;EAC7CoM,OAAO,EAAEJ,IAAI,KAAKlM,eAAe,CAACK,GAAG;EACrCkM,SAAS,EAAEL,IAAI,KAAKlM,eAAe,CAACI;AACrC,EAAC;ACPF,IAAAoM,SAAA,GAAeA,CACbjP,IAAuB,EACvB2H,MAAa,EACbuH,WAAqB,KAErB,CAACA,WAAW,KACXvH,MAAM,CAACO,QAAQ,IACdP,MAAM,CAACG,KAAK,CAACzH,GAAG,CAACL,IAAI,CAAC,IACtB,CAAC,GAAG2H,MAAM,CAACG,KAAK,CAAC,CAACtC,IAAI,CACnB2J,SAAS,IACRnP,IAAI,CAAC0F,UAAU,CAACyJ,SAAS,CAAC,IAC1B,QAAQ,CAACzG,IAAI,CAAC1I,IAAI,CAACoP,KAAK,CAACD,SAAS,CAAC5K,MAAM,CAAC,CAAC,CAC9C,CAAC;ACVN,MAAM8K,qBAAqB,GAAGA,CAC5B9E,MAAiB,EACjBH,MAAwD,EACxDkF,WAA8D,EAC9DC,UAAoB,KAClB;EACF,KAAK,MAAMjO,GAAG,IAAIgO,WAAW,IAAI5K,MAAM,CAACK,IAAI,CAACwF,MAAM,CAAC,EAAE;IACpD,MAAMR,KAAK,GAAGlI,GAAG,CAAC0I,MAAM,EAAEjJ,GAAG,CAAC;IAE9B,IAAIyI,KAAK,EAAE;MACT,MAAM;QAAEE,EAAE;QAAE,GAAGuF;MAAY,CAAE,GAAGzF,KAAK;MAErC,IAAIE,EAAE,EAAE;QACN,IAAIA,EAAE,CAACwF,IAAI,IAAIxF,EAAE,CAACwF,IAAI,CAAC,CAAC,CAAC,IAAIrF,MAAM,CAACH,EAAE,CAACwF,IAAI,CAAC,CAAC,CAAC,EAAEnO,GAAG,CAAC,IAAI,CAACiO,UAAU,EAAE;UACnE;SACD,MAAM,IAAItF,EAAE,CAACQ,GAAG,IAAIL,MAAM,CAACH,EAAE,CAACQ,GAAG,EAAER,EAAE,CAACjK,IAAI,CAAC,IAAI,CAACuP,UAAU,EAAE;UAC3D;SACD,MAAM;UACLF,qBAAqB,CAACG,YAAY,EAAEpF,MAAM,CAAC;;OAE9C,MAAM,IAAI5K,QAAQ,CAACgQ,YAAY,CAAC,EAAE;QACjCH,qBAAqB,CAACG,YAAY,EAAEpF,MAAM,CAAC;;;;AAInD,CAAC;ACnBD,IAAAsF,yBAAA,GAAeA,CACbvI,MAAsB,EACtBkE,KAA0C,EAC1CrL,IAAuB,KACL;EAClB,MAAM2P,gBAAgB,GAAGpO,OAAO,CAACM,GAAG,CAACsF,MAAM,EAAEnH,IAAI,CAAC,CAAC;EACnD8I,GAAG,CAAC6G,gBAAgB,EAAE,MAAM,EAAEtE,KAAK,CAACrL,IAAI,CAAC,CAAC;EAC1C8I,GAAG,CAAC3B,MAAM,EAAEnH,IAAI,EAAE2P,gBAAgB,CAAC;EACnC,OAAOxI,MAAM;AACf,CAAC;ACjBD,IAAAyI,WAAA,GAAgB3Q,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,MAAM;ACHzB,IAAA2Q,UAAA,GAAgBzQ,KAAc,IAC5B,OAAOA,KAAK,KAAK,UAAU;ACC7B,IAAA0Q,aAAA,GAAgB1Q,KAAc,IAA0B;EACtD,IAAI,CAACwB,KAAK,EAAE;IACV,OAAO,KAAK;;EAGd,MAAMmP,KAAK,GAAG3Q,KAAK,GAAKA,KAAqB,CAAC4Q,aAA0B,GAAG,CAAC;EAC5E,OACE5Q,KAAK,aACJ2Q,KAAK,IAAIA,KAAK,CAACE,WAAW,GAAGF,KAAK,CAACE,WAAW,CAACnP,WAAW,GAAGA,WAAW,CAAC;AAE9E,CAAC;ACTD,IAAAoP,SAAA,GAAgB9Q,KAAc,IAAuBqI,QAAQ,CAACrI,KAAK,CAAC;ACDpE,IAAA+Q,YAAA,GAAgBlR,OAAqB,IACnCA,OAAO,CAACC,IAAI,KAAK,OAAO;ACH1B,IAAAkR,OAAA,GAAgBhR,KAAc,IAAsBA,KAAK,YAAYiR,MAAM;ACO3E,MAAMC,aAAa,GAAwB;EACzClR,KAAK,EAAE,KAAK;EACZ8H,OAAO,EAAE;CACV;AAED,MAAMqJ,WAAW,GAAG;EAAEnR,KAAK,EAAE,IAAI;EAAE8H,OAAO,EAAE;AAAI,CAAE;AAElD,IAAAsJ,gBAAA,GAAgBnC,OAA4B,IAAyB;EACnE,IAAI5O,KAAK,CAACC,OAAO,CAAC2O,OAAO,CAAC,EAAE;IAC1B,IAAIA,OAAO,CAAC9J,MAAM,GAAG,CAAC,EAAE;MACtB,MAAM6D,MAAM,GAAGiG,OAAO,CACnB7M,MAAM,CAAEiP,MAAM,IAAKA,MAAM,IAAIA,MAAM,CAAC3Q,OAAO,IAAI,CAAC2Q,MAAM,CAACxK,QAAQ,CAAC,CAChE+B,GAAG,CAAEyI,MAAM,IAAKA,MAAM,CAACrR,KAAK,CAAC;MAChC,OAAO;QAAEA,KAAK,EAAEgJ,MAAM;QAAElB,OAAO,EAAE,CAAC,CAACkB,MAAM,CAAC7D;MAAM,CAAE;;IAGpD,OAAO8J,OAAO,CAAC,CAAC,CAAC,CAACvO,OAAO,IAAI,CAACuO,OAAO,CAAC,CAAC,CAAC,CAACpI,QAAQ;IAC/C;IACEoI,OAAO,CAAC,CAAC,CAAC,CAACqC,UAAU,IAAI,CAAChP,WAAW,CAAC2M,OAAO,CAAC,CAAC,CAAC,CAACqC,UAAU,CAACtR,KAAK,CAAC,GAChEsC,WAAW,CAAC2M,OAAO,CAAC,CAAC,CAAC,CAACjP,KAAK,CAAC,IAAIiP,OAAO,CAAC,CAAC,CAAC,CAACjP,KAAK,KAAK,EAAE,GACtDmR,WAAW,GACX;MAAEnR,KAAK,EAAEiP,OAAO,CAAC,CAAC,CAAC,CAACjP,KAAK;MAAE8H,OAAO,EAAE;IAAI,CAAE,GAC5CqJ,WAAW,GACbD,aAAa;;EAGnB,OAAOA,aAAa;AACtB,CAAC;AC7BD,MAAMK,aAAa,GAAqB;EACtCzJ,OAAO,EAAE,KAAK;EACd9H,KAAK,EAAE;CACR;AAED,IAAAwR,aAAA,GAAgBvC,OAA4B,IAC1C5O,KAAK,CAACC,OAAO,CAAC2O,OAAO,CAAC,GAClBA,OAAO,CAAClM,MAAM,CACZ,CAAC0O,QAAQ,EAAEJ,MAAM,KACfA,MAAM,IAAIA,MAAM,CAAC3Q,OAAO,IAAI,CAAC2Q,MAAM,CAACxK,QAAQ,GACxC;EACEiB,OAAO,EAAE,IAAI;EACb9H,KAAK,EAAEqR,MAAM,CAACrR;AACf,IACDyR,QAAQ,EACdF,aAAa,CACd,GACDA,aAAa;AClBL,SAAUG,gBAAgBA,CACtC7O,MAAsB,EACtBwI,GAAQ,EACS;EAAA,IAAjBvL,IAAI,GAAAoF,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAG,UAAU;EAEjB,IACE4L,SAAS,CAACjO,MAAM,CAAC,IAChBxC,KAAK,CAACC,OAAO,CAACuC,MAAM,CAAC,IAAIA,MAAM,CAAC8O,KAAK,CAACb,SAAS,CAAE,IACjD9N,SAAS,CAACH,MAAM,CAAC,IAAI,CAACA,MAAO,EAC9B;IACA,OAAO;MACL/C,IAAI;MACJ4L,OAAO,EAAEoF,SAAS,CAACjO,MAAM,CAAC,GAAGA,MAAM,GAAG,EAAE;MACxCwI;KACD;;AAEL;AChBA,IAAAuG,kBAAA,GAAgBC,cAA+B,IAC7CzR,QAAQ,CAACyR,cAAc,CAAC,IAAI,CAACb,OAAO,CAACa,cAAc,CAAC,GAChDA,cAAc,GACd;EACE7R,KAAK,EAAE6R,cAAc;EACrBnG,OAAO,EAAE;CACV;ACsBP,IAAAoG,aAAA,GAAe,MAAAA,CACbnH,KAAY,EACZnC,UAAa,EACb6F,wBAAiC,EACjC0D,yBAAmC,EACnCC,YAAsB,KACU;EAChC,MAAM;IACJ3G,GAAG;IACHgF,IAAI;IACJpM,QAAQ;IACRH,SAAS;IACTC,SAAS;IACTF,GAAG;IACHD,GAAG;IACHI,OAAO;IACPE,QAAQ;IACRtD,IAAI;IACJqR,aAAa;IACbnH,KAAK;IACLjE;EAAQ,CACT,GAAG8D,KAAK,CAACE,EAAE;EACZ,MAAMqH,UAAU,GAAqBzP,GAAG,CAAC+F,UAAU,EAAE5H,IAAI,CAAC;EAC1D,IAAI,CAACkK,KAAK,IAAIjE,QAAQ,EAAE;IACtB,OAAO,EAAE;;EAEX,MAAMsL,QAAQ,GAAqB9B,IAAI,GAAGA,IAAI,CAAC,CAAC,CAAC,GAAIhF,GAAwB;EAC7E,MAAMI,iBAAiB,GAAIC,OAA0B,IAAI;IACvD,IAAIqG,yBAAyB,IAAII,QAAQ,CAACxG,cAAc,EAAE;MACxDwG,QAAQ,CAAC1G,iBAAiB,CAACzI,SAAS,CAAC0I,OAAO,CAAC,GAAG,EAAE,GAAGA,OAAO,IAAI,EAAE,CAAC;MACnEyG,QAAQ,CAACxG,cAAc,EAAE;;EAE7B,CAAC;EACD,MAAMM,KAAK,GAAwB,EAAE;EACrC,MAAMmG,OAAO,GAAGrB,YAAY,CAAC1F,GAAG,CAAC;EACjC,MAAMgH,UAAU,GAAGzS,eAAe,CAACyL,GAAG,CAAC;EACvC,MAAMiH,iBAAiB,GAAGF,OAAO,IAAIC,UAAU;EAC/C,MAAME,OAAO,GACV,CAACN,aAAa,IAAIzB,WAAW,CAACnF,GAAG,CAAC,KACjC/I,WAAW,CAAC+I,GAAG,CAACrL,KAAK,CAAC,IACtBsC,WAAW,CAAC4P,UAAU,CAAC,IACxBxB,aAAa,CAACrF,GAAG,CAAC,IAAIA,GAAG,CAACrL,KAAK,KAAK,EAAG,IACxCkS,UAAU,KAAK,EAAE,IAChB7R,KAAK,CAACC,OAAO,CAAC4R,UAAU,CAAC,IAAI,CAACA,UAAU,CAAC/M,MAAO;EACnD,MAAMqN,iBAAiB,GAAGpE,YAAY,CAACqE,IAAI,CACzC,IAAI,EACJ7R,IAAI,EACJyN,wBAAwB,EACxBpC,KAAK,CACN;EACD,MAAMyG,gBAAgB,GAAG,SAAAA,CACvBC,SAAkB,EAClBC,gBAAyB,EACzBC,gBAAyB,EAGvB;IAAA,IAFFC,OAAA,GAAA5N,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAmBvB,sBAAsB,CAACG,SAAS;IAAA,IACnDiP,OAAA,GAAA7N,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAmBvB,sBAAsB,CAACI,SAAS;IAEnD,MAAM2H,OAAO,GAAGiH,SAAS,GAAGC,gBAAgB,GAAGC,gBAAgB;IAC/D5G,KAAK,CAACrL,IAAI,CAAC,GAAG;MACZd,IAAI,EAAE6S,SAAS,GAAGG,OAAO,GAAGC,OAAO;MACnCrH,OAAO;MACPL,GAAG;MACH,GAAGmH,iBAAiB,CAACG,SAAS,GAAGG,OAAO,GAAGC,OAAO,EAAErH,OAAO;KAC5D;EACH,CAAC;EAED,IACEsG,YAAY,GACR,CAAC3R,KAAK,CAACC,OAAO,CAAC4R,UAAU,CAAC,IAAI,CAACA,UAAU,CAAC/M,MAAM,GAChDlB,QAAQ,KACN,CAACqO,iBAAiB,KAAKC,OAAO,IAAIrS,iBAAiB,CAACgS,UAAU,CAAC,CAAC,IAC/DlP,SAAS,CAACkP,UAAU,CAAC,IAAI,CAACA,UAAW,IACrCG,UAAU,IAAI,CAACjB,gBAAgB,CAACf,IAAI,CAAC,CAACvI,OAAQ,IAC9CsK,OAAO,IAAI,CAACZ,aAAa,CAACnB,IAAI,CAAC,CAACvI,OAAQ,CAAC,EAChD;IACA,MAAM;MAAE9H,KAAK;MAAE0L;IAAO,CAAE,GAAGoF,SAAS,CAAC7M,QAAQ,CAAC,GAC1C;MAAEjE,KAAK,EAAE,CAAC,CAACiE,QAAQ;MAAEyH,OAAO,EAAEzH;IAAQ,CAAE,GACxC2N,kBAAkB,CAAC3N,QAAQ,CAAC;IAEhC,IAAIjE,KAAK,EAAE;MACTiM,KAAK,CAACrL,IAAI,CAAC,GAAG;QACZd,IAAI,EAAE6D,sBAAsB,CAACM,QAAQ;QACrCyH,OAAO;QACPL,GAAG,EAAE8G,QAAQ;QACb,GAAGK,iBAAiB,CAAC7O,sBAAsB,CAACM,QAAQ,EAAEyH,OAAO;OAC9D;MACD,IAAI,CAAC2C,wBAAwB,EAAE;QAC7B5C,iBAAiB,CAACC,OAAO,CAAC;QAC1B,OAAOO,KAAK;;;;EAKlB,IAAI,CAACsG,OAAO,KAAK,CAACrS,iBAAiB,CAAC2D,GAAG,CAAC,IAAI,CAAC3D,iBAAiB,CAAC0D,GAAG,CAAC,CAAC,EAAE;IACpE,IAAI+O,SAAS;IACb,IAAIK,SAAS;IACb,MAAMC,SAAS,GAAGrB,kBAAkB,CAAChO,GAAG,CAAC;IACzC,MAAMsP,SAAS,GAAGtB,kBAAkB,CAAC/N,GAAG,CAAC;IAEzC,IAAI,CAAC3D,iBAAiB,CAACgS,UAAU,CAAC,IAAI,CAAClI,KAAK,CAACkI,UAAoB,CAAC,EAAE;MAClE,MAAMiB,WAAW,GACd9H,GAAwB,CAAC4G,aAAa,KACtCC,UAAU,GAAG,CAACA,UAAU,GAAGA,UAAU,CAAC;MACzC,IAAI,CAAChS,iBAAiB,CAAC+S,SAAS,CAACjT,KAAK,CAAC,EAAE;QACvC2S,SAAS,GAAGQ,WAAW,GAAGF,SAAS,CAACjT,KAAK;;MAE3C,IAAI,CAACE,iBAAiB,CAACgT,SAAS,CAAClT,KAAK,CAAC,EAAE;QACvCgT,SAAS,GAAGG,WAAW,GAAGD,SAAS,CAAClT,KAAK;;KAE5C,MAAM;MACL,MAAMoT,SAAS,GACZ/H,GAAwB,CAACgI,WAAW,IAAI,IAAIpT,IAAI,CAACiS,UAAoB,CAAC;MACzE,MAAMoB,iBAAiB,GAAIC,IAAa,IACtC,IAAItT,IAAI,CAAC,IAAIA,IAAI,EAAE,CAACuT,YAAY,EAAE,GAAG,GAAG,GAAGD,IAAI,CAAC;MAClD,MAAME,MAAM,GAAGpI,GAAG,CAACvL,IAAI,IAAI,MAAM;MACjC,MAAM4T,MAAM,GAAGrI,GAAG,CAACvL,IAAI,IAAI,MAAM;MAEjC,IAAIuI,QAAQ,CAAC4K,SAAS,CAACjT,KAAK,CAAC,IAAIkS,UAAU,EAAE;QAC3CS,SAAS,GAAGc,MAAM,GACdH,iBAAiB,CAACpB,UAAU,CAAC,GAAGoB,iBAAiB,CAACL,SAAS,CAACjT,KAAK,CAAC,GAClE0T,MAAM,GACNxB,UAAU,GAAGe,SAAS,CAACjT,KAAK,GAC5BoT,SAAS,GAAG,IAAInT,IAAI,CAACgT,SAAS,CAACjT,KAAK,CAAC;;MAG3C,IAAIqI,QAAQ,CAAC6K,SAAS,CAAClT,KAAK,CAAC,IAAIkS,UAAU,EAAE;QAC3Cc,SAAS,GAAGS,MAAM,GACdH,iBAAiB,CAACpB,UAAU,CAAC,GAAGoB,iBAAiB,CAACJ,SAAS,CAAClT,KAAK,CAAC,GAClE0T,MAAM,GACNxB,UAAU,GAAGgB,SAAS,CAAClT,KAAK,GAC5BoT,SAAS,GAAG,IAAInT,IAAI,CAACiT,SAAS,CAAClT,KAAK,CAAC;;;IAI7C,IAAI2S,SAAS,IAAIK,SAAS,EAAE;MAC1BN,gBAAgB,CACd,CAAC,CAACC,SAAS,EACXM,SAAS,CAACvH,OAAO,EACjBwH,SAAS,CAACxH,OAAO,EACjB/H,sBAAsB,CAACC,GAAG,EAC1BD,sBAAsB,CAACE,GAAG,CAC3B;MACD,IAAI,CAACwK,wBAAwB,EAAE;QAC7B5C,iBAAiB,CAACQ,KAAK,CAACrL,IAAI,CAAE,CAAC8K,OAAO,CAAC;QACvC,OAAOO,KAAK;;;;EAKlB,IACE,CAACnI,SAAS,IAAIC,SAAS,KACvB,CAACwO,OAAO,KACPlK,QAAQ,CAAC6J,UAAU,CAAC,IAAKF,YAAY,IAAI3R,KAAK,CAACC,OAAO,CAAC4R,UAAU,CAAE,CAAC,EACrE;IACA,MAAMyB,eAAe,GAAG/B,kBAAkB,CAAC9N,SAAS,CAAC;IACrD,MAAM8P,eAAe,GAAGhC,kBAAkB,CAAC7N,SAAS,CAAC;IACrD,MAAM4O,SAAS,GACb,CAACzS,iBAAiB,CAACyT,eAAe,CAAC3T,KAAK,CAAC,IACzCkS,UAAU,CAAC/M,MAAM,GAAG,CAACwO,eAAe,CAAC3T,KAAK;IAC5C,MAAMgT,SAAS,GACb,CAAC9S,iBAAiB,CAAC0T,eAAe,CAAC5T,KAAK,CAAC,IACzCkS,UAAU,CAAC/M,MAAM,GAAG,CAACyO,eAAe,CAAC5T,KAAK;IAE5C,IAAI2S,SAAS,IAAIK,SAAS,EAAE;MAC1BN,gBAAgB,CACdC,SAAS,EACTgB,eAAe,CAACjI,OAAO,EACvBkI,eAAe,CAAClI,OAAO,CACxB;MACD,IAAI,CAAC2C,wBAAwB,EAAE;QAC7B5C,iBAAiB,CAACQ,KAAK,CAACrL,IAAI,CAAE,CAAC8K,OAAO,CAAC;QACvC,OAAOO,KAAK;;;;EAKlB,IAAIjI,OAAO,IAAI,CAACuO,OAAO,IAAIlK,QAAQ,CAAC6J,UAAU,CAAC,EAAE;IAC/C,MAAM;MAAElS,KAAK,EAAE6T,YAAY;MAAEnI;IAAO,CAAE,GAAGkG,kBAAkB,CAAC5N,OAAO,CAAC;IAEpE,IAAIgN,OAAO,CAAC6C,YAAY,CAAC,IAAI,CAAC3B,UAAU,CAAC4B,KAAK,CAACD,YAAY,CAAC,EAAE;MAC5D5H,KAAK,CAACrL,IAAI,CAAC,GAAG;QACZd,IAAI,EAAE6D,sBAAsB,CAACK,OAAO;QACpC0H,OAAO;QACPL,GAAG;QACH,GAAGmH,iBAAiB,CAAC7O,sBAAsB,CAACK,OAAO,EAAE0H,OAAO;OAC7D;MACD,IAAI,CAAC2C,wBAAwB,EAAE;QAC7B5C,iBAAiB,CAACC,OAAO,CAAC;QAC1B,OAAOO,KAAK;;;;EAKlB,IAAI/H,QAAQ,EAAE;IACZ,IAAIuM,UAAU,CAACvM,QAAQ,CAAC,EAAE;MACxB,MAAMrB,MAAM,GAAG,MAAMqB,QAAQ,CAACgO,UAAU,EAAE1J,UAAU,CAAC;MACrD,MAAMuL,aAAa,GAAGrC,gBAAgB,CAAC7O,MAAM,EAAEsP,QAAQ,CAAC;MAExD,IAAI4B,aAAa,EAAE;QACjB9H,KAAK,CAACrL,IAAI,CAAC,GAAG;UACZ,GAAGmT,aAAa;UAChB,GAAGvB,iBAAiB,CAClB7O,sBAAsB,CAACO,QAAQ,EAC/B6P,aAAa,CAACrI,OAAO;SAExB;QACD,IAAI,CAAC2C,wBAAwB,EAAE;UAC7B5C,iBAAiB,CAACsI,aAAa,CAACrI,OAAO,CAAC;UACxC,OAAOO,KAAK;;;KAGjB,MAAM,IAAI7L,QAAQ,CAAC8D,QAAQ,CAAC,EAAE;MAC7B,IAAI8P,gBAAgB,GAAG,EAAgB;MAEvC,KAAK,MAAM9R,GAAG,IAAIgC,QAAQ,EAAE;QAC1B,IAAI,CAACwB,aAAa,CAACsO,gBAAgB,CAAC,IAAI,CAAC3F,wBAAwB,EAAE;UACjE;;QAGF,MAAM0F,aAAa,GAAGrC,gBAAgB,CACpC,MAAMxN,QAAQ,CAAChC,GAAG,CAAC,CAACgQ,UAAU,EAAE1J,UAAU,CAAC,EAC3C2J,QAAQ,EACRjQ,GAAG,CACJ;QAED,IAAI6R,aAAa,EAAE;UACjBC,gBAAgB,GAAG;YACjB,GAAGD,aAAa;YAChB,GAAGvB,iBAAiB,CAACtQ,GAAG,EAAE6R,aAAa,CAACrI,OAAO;WAChD;UAEDD,iBAAiB,CAACsI,aAAa,CAACrI,OAAO,CAAC;UAExC,IAAI2C,wBAAwB,EAAE;YAC5BpC,KAAK,CAACrL,IAAI,CAAC,GAAGoT,gBAAgB;;;;MAKpC,IAAI,CAACtO,aAAa,CAACsO,gBAAgB,CAAC,EAAE;QACpC/H,KAAK,CAACrL,IAAI,CAAC,GAAG;UACZyK,GAAG,EAAE8G,QAAQ;UACb,GAAG6B;SACJ;QACD,IAAI,CAAC3F,wBAAwB,EAAE;UAC7B,OAAOpC,KAAK;;;;;EAMpBR,iBAAiB,CAAC,IAAI,CAAC;EACvB,OAAOQ,KAAK;AACd,CAAC;AC3RD,IAAAgI,QAAA,GAAeA,CAAIpS,IAAS,EAAE7B,KAAc,KAAU,CACpD,GAAG6B,IAAI,EACP,GAAGmE,qBAAqB,CAAChG,KAAK,CAAC,CAChC;ACLD,IAAAkU,cAAA,GAAmBlU,KAAc,IAC/BK,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,CAAC4I,GAAG,CAAC,MAAMpG,SAAS,CAAC,GAAGA,SAAS;ACOvC,SAAA2R,MAAMA,CAC5BtS,IAAS,EACT8H,KAAa,EACb3J,KAAe;EAEf,OAAO,CACL,GAAG6B,IAAI,CAACmO,KAAK,CAAC,CAAC,EAAErG,KAAK,CAAC,EACvB,GAAG3D,qBAAqB,CAAChG,KAAK,CAAC,EAC/B,GAAG6B,IAAI,CAACmO,KAAK,CAACrG,KAAK,CAAC,CACrB;AACH;AChBA,IAAAyK,WAAA,GAAeA,CACbvS,IAAuB,EACvBwS,IAAY,EACZC,EAAU,KACW;EACrB,IAAI,CAACjU,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC,EAAE;IACxB,OAAO,EAAE;;EAGX,IAAIS,WAAW,CAACT,IAAI,CAACyS,EAAE,CAAC,CAAC,EAAE;IACzBzS,IAAI,CAACyS,EAAE,CAAC,GAAG9R,SAAS;;EAEtBX,IAAI,CAAC0S,MAAM,CAACD,EAAE,EAAE,CAAC,EAAEzS,IAAI,CAAC0S,MAAM,CAACF,IAAI,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;EAE3C,OAAOxS,IAAI;AACb,CAAC;ACfD,IAAA2S,SAAA,GAAeA,CAAI3S,IAAS,EAAE7B,KAAc,KAAU,CACpD,GAAGgG,qBAAqB,CAAChG,KAAK,CAAC,EAC/B,GAAGgG,qBAAqB,CAACnE,IAAI,CAAC,CAC/B;ACDD,SAAS4S,eAAeA,CAAI5S,IAAS,EAAE6S,OAAiB;EACtD,IAAIC,CAAC,GAAG,CAAC;EACT,MAAMC,IAAI,GAAG,CAAC,GAAG/S,IAAI,CAAC;EAEtB,KAAK,MAAM8H,KAAK,IAAI+K,OAAO,EAAE;IAC3BE,IAAI,CAACL,MAAM,CAAC5K,KAAK,GAAGgL,CAAC,EAAE,CAAC,CAAC;IACzBA,CAAC,EAAE;;EAGL,OAAOxS,OAAO,CAACyS,IAAI,CAAC,CAACzP,MAAM,GAAGyP,IAAI,GAAG,EAAE;AACzC;AAEA,IAAAC,aAAA,GAAeA,CAAIhT,IAAS,EAAE8H,KAAyB,KACrDrH,WAAW,CAACqH,KAAK,CAAC,GACd,EAAE,GACF8K,eAAe,CACb5S,IAAI,EACHmE,qBAAqB,CAAC2D,KAAK,CAAc,CAACmL,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC,CACjE;ACtBP,IAAAC,WAAA,GAAeA,CAAIpT,IAAS,EAAEqT,MAAc,EAAEC,MAAc,KAAU;EACpE,CAACtT,IAAI,CAACqT,MAAM,CAAC,EAAErT,IAAI,CAACsT,MAAM,CAAC,CAAC,GAAG,CAACtT,IAAI,CAACsT,MAAM,CAAC,EAAEtT,IAAI,CAACqT,MAAM,CAAC,CAAC;AAC7D,CAAC;ACID,SAASE,OAAOA,CAAC1S,MAAW,EAAE2S,UAA+B;EAC3D,MAAMlQ,MAAM,GAAGkQ,UAAU,CAACrF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC7K,MAAM;EAC7C,IAAIwE,KAAK,GAAG,CAAC;EAEb,OAAOA,KAAK,GAAGxE,MAAM,EAAE;IACrBzC,MAAM,GAAGJ,WAAW,CAACI,MAAM,CAAC,GAAGiH,KAAK,EAAE,GAAGjH,MAAM,CAAC2S,UAAU,CAAC1L,KAAK,EAAE,CAAC,CAAC;;EAGtE,OAAOjH,MAAM;AACf;AAEA,SAAS4S,YAAYA,CAACC,GAAc;EAClC,KAAK,MAAMrT,GAAG,IAAIqT,GAAG,EAAE;IACrB,IAAIA,GAAG,CAAChU,cAAc,CAACW,GAAG,CAAC,IAAI,CAACI,WAAW,CAACiT,GAAG,CAACrT,GAAG,CAAC,CAAC,EAAE;MACrD,OAAO,KAAK;;;EAGhB,OAAO,IAAI;AACb;AAEc,SAAUsT,KAAKA,CAAC9S,MAAW,EAAEC,IAAkC;EAC3E,MAAM8S,KAAK,GAAGpV,KAAK,CAACC,OAAO,CAACqC,IAAI,CAAC,GAC7BA,IAAI,GACJ0G,KAAK,CAAC1G,IAAI,CAAC,GACX,CAACA,IAAI,CAAC,GACN4G,YAAY,CAAC5G,IAAI,CAAC;EAEtB,MAAM+S,WAAW,GAAGD,KAAK,CAACtQ,MAAM,KAAK,CAAC,GAAGzC,MAAM,GAAG0S,OAAO,CAAC1S,MAAM,EAAE+S,KAAK,CAAC;EAExE,MAAM9L,KAAK,GAAG8L,KAAK,CAACtQ,MAAM,GAAG,CAAC;EAC9B,MAAMjD,GAAG,GAAGuT,KAAK,CAAC9L,KAAK,CAAC;EAExB,IAAI+L,WAAW,EAAE;IACf,OAAOA,WAAW,CAACxT,GAAG,CAAC;;EAGzB,IACEyH,KAAK,KAAK,CAAC,KACTvJ,QAAQ,CAACsV,WAAW,CAAC,IAAIhQ,aAAa,CAACgQ,WAAW,CAAC,IAClDrV,KAAK,CAACC,OAAO,CAACoV,WAAW,CAAC,IAAIJ,YAAY,CAACI,WAAW,CAAE,CAAC,EAC5D;IACAF,KAAK,CAAC9S,MAAM,EAAE+S,KAAK,CAACzF,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;;EAGnC,OAAOtN,MAAM;AACf;ACnDA,IAAAiT,QAAA,GAAeA,CAAIC,WAAgB,EAAEjM,KAAa,EAAE3J,KAAQ,KAAI;EAC9D4V,WAAW,CAACjM,KAAK,CAAC,GAAG3J,KAAK;EAC1B,OAAO4V,WAAW;AACpB,CAAC;;ACwCD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoCG;AACG,SAAUC,aAAaA,CAK3BpR,KAAkE;EAElE,MAAM0C,OAAO,GAAG7C,cAAc,EAAE;EAChC,MAAM;IACJS,OAAO,GAAGoC,OAAO,CAACpC,OAAO;IACzBnE,IAAI;IACJkV,OAAO,GAAG,IAAI;IACd5L;EAAgB,CACjB,GAAGzF,KAAK;EACT,MAAM,CAAC0G,MAAM,EAAE4K,SAAS,CAAC,GAAG3R,KAAK,CAACgD,QAAQ,CAACrC,OAAO,CAACiR,cAAc,CAACpV,IAAI,CAAC,CAAC;EACxE,MAAMqV,GAAG,GAAG7R,KAAK,CAACqC,MAAM,CACtB1B,OAAO,CAACiR,cAAc,CAACpV,IAAI,CAAC,CAACgI,GAAG,CAAC2F,UAAU,CAAC,CAC7C;EACD,MAAM2H,SAAS,GAAG9R,KAAK,CAACqC,MAAM,CAAC0E,MAAM,CAAC;EACtC,MAAMnD,KAAK,GAAG5D,KAAK,CAACqC,MAAM,CAAC7F,IAAI,CAAC;EAChC,MAAMuV,SAAS,GAAG/R,KAAK,CAACqC,MAAM,CAAC,KAAK,CAAC;EAErCuB,KAAK,CAACtB,OAAO,GAAG9F,IAAI;EACpBsV,SAAS,CAACxP,OAAO,GAAGyE,MAAM;EAC1BpG,OAAO,CAACwD,MAAM,CAAC6B,KAAK,CAACzB,GAAG,CAAC/H,IAAI,CAAC;EAE9B6D,KAAK,CAAC8F,KAAK,IACRxF,OAAiC,CAACuF,QAAQ,CACzC1J,IAA+B,EAC/B6D,KAAK,CAAC8F,KAAsC,CAC7C;EAEHhE,YAAY,CAAC;IACXS,IAAI,EAAEoP,IAAA,IAMD;MAAA,IANE;QACLpN,MAAM;QACNpI,IAAI,EAAEyV;MAAc,CAIrB,GAAAD,IAAA;MACC,IAAIC,cAAc,KAAKrO,KAAK,CAACtB,OAAO,IAAI,CAAC2P,cAAc,EAAE;QACvD,MAAMT,WAAW,GAAGnT,GAAG,CAACuG,MAAM,EAAEhB,KAAK,CAACtB,OAAO,CAAC;QAC9C,IAAIrG,KAAK,CAACC,OAAO,CAACsV,WAAW,CAAC,EAAE;UAC9BG,SAAS,CAACH,WAAW,CAAC;UACtBK,GAAG,CAACvP,OAAO,GAAGkP,WAAW,CAAChN,GAAG,CAAC2F,UAAU,CAAC;;;KAG9C;IACDzH,OAAO,EAAE/B,OAAO,CAACmD,SAAS,CAACkC;EAC5B,EAAC;EAEF,MAAMkM,YAAY,GAAGlS,KAAK,CAACgH,WAAW,CAMlCmL,uBAA0B,IACxB;IACFJ,SAAS,CAACzP,OAAO,GAAG,IAAI;IACxB3B,OAAO,CAACyR,iBAAiB,CAAC5V,IAAI,EAAE2V,uBAAuB,CAAC;EAC1D,CAAC,EACD,CAACxR,OAAO,EAAEnE,IAAI,CAAC,CAChB;EAED,MAAM4M,MAAM,GAAGA,CACbxN,KAEwD,EACxDiP,OAA+B,KAC7B;IACF,MAAMwH,WAAW,GAAGzQ,qBAAqB,CAACpE,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC7D,MAAMuW,uBAAuB,GAAGtC,QAAQ,CACtClP,OAAO,CAACiR,cAAc,CAACpV,IAAI,CAAC,EAC5B6V,WAAW,CACZ;IACD1R,OAAO,CAACwD,MAAM,CAACgD,KAAK,GAAGyD,iBAAiB,CACtCpO,IAAI,EACJ2V,uBAAuB,CAACpR,MAAM,GAAG,CAAC,EAClC8J,OAAO,CACR;IACDgH,GAAG,CAACvP,OAAO,GAAGuN,QAAQ,CAACgC,GAAG,CAACvP,OAAO,EAAE+P,WAAW,CAAC7N,GAAG,CAAC2F,UAAU,CAAC,CAAC;IAChE+H,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClCxR,OAAO,CAACyR,iBAAiB,CAAC5V,IAAI,EAAE2V,uBAAuB,EAAEtC,QAAQ,EAAE;MACjEyC,IAAI,EAAExC,cAAc,CAAClU,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAM2W,OAAO,GAAGA,CACd3W,KAEwD,EACxDiP,OAA+B,KAC7B;IACF,MAAM2H,YAAY,GAAG5Q,qBAAqB,CAACpE,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC9D,MAAMuW,uBAAuB,GAAG/B,SAAS,CACvCzP,OAAO,CAACiR,cAAc,CAACpV,IAAI,CAAC,EAC5BgW,YAAY,CACb;IACD7R,OAAO,CAACwD,MAAM,CAACgD,KAAK,GAAGyD,iBAAiB,CAACpO,IAAI,EAAE,CAAC,EAAEqO,OAAO,CAAC;IAC1DgH,GAAG,CAACvP,OAAO,GAAG8N,SAAS,CAACyB,GAAG,CAACvP,OAAO,EAAEkQ,YAAY,CAAChO,GAAG,CAAC2F,UAAU,CAAC,CAAC;IAClE+H,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClCxR,OAAO,CAACyR,iBAAiB,CAAC5V,IAAI,EAAE2V,uBAAuB,EAAE/B,SAAS,EAAE;MAClEkC,IAAI,EAAExC,cAAc,CAAClU,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAM6W,MAAM,GAAIlN,KAAyB,IAAI;IAC3C,MAAM4M,uBAAuB,GAEvB1B,aAAa,CAAC9P,OAAO,CAACiR,cAAc,CAACpV,IAAI,CAAC,EAAE+I,KAAK,CAAC;IACxDsM,GAAG,CAACvP,OAAO,GAAGmO,aAAa,CAACoB,GAAG,CAACvP,OAAO,EAAEiD,KAAK,CAAC;IAC/C2M,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClCxR,OAAO,CAACyR,iBAAiB,CAAC5V,IAAI,EAAE2V,uBAAuB,EAAE1B,aAAa,EAAE;MACtE6B,IAAI,EAAE/M;IACP,EAAC;EACJ,CAAC;EAED,MAAMmN,QAAM,GAAG3C,CACbxK,KAAa,EACb3J,KAEwD,EACxDiP,OAA+B,KAC7B;IACF,MAAM8H,WAAW,GAAG/Q,qBAAqB,CAACpE,WAAW,CAAC5B,KAAK,CAAC,CAAC;IAC7D,MAAMuW,uBAAuB,GAAGpC,MAAQ,CACtCpP,OAAO,CAACiR,cAAc,CAACpV,IAAI,CAAC,EAC5B+I,KAAK,EACLoN,WAAW,CACZ;IACDhS,OAAO,CAACwD,MAAM,CAACgD,KAAK,GAAGyD,iBAAiB,CAACpO,IAAI,EAAE+I,KAAK,EAAEsF,OAAO,CAAC;IAC9DgH,GAAG,CAACvP,OAAO,GAAGyN,MAAQ,CAAC8B,GAAG,CAACvP,OAAO,EAAEiD,KAAK,EAAEoN,WAAW,CAACnO,GAAG,CAAC2F,UAAU,CAAC,CAAC;IACvE+H,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClCxR,OAAO,CAACyR,iBAAiB,CAAC5V,IAAI,EAAE2V,uBAAuB,EAAEpC,MAAQ,EAAE;MACjEuC,IAAI,EAAE/M,KAAK;MACXqN,IAAI,EAAE9C,cAAc,CAAClU,KAAK;IAC3B,EAAC;EACJ,CAAC;EAED,MAAMiX,IAAI,GAAGA,CAAC/B,MAAc,EAAEC,MAAc,KAAI;IAC9C,MAAMoB,uBAAuB,GAAGxR,OAAO,CAACiR,cAAc,CAACpV,IAAI,CAAC;IAC5DqU,WAAW,CAACsB,uBAAuB,EAAErB,MAAM,EAAEC,MAAM,CAAC;IACpDF,WAAW,CAACgB,GAAG,CAACvP,OAAO,EAAEwO,MAAM,EAAEC,MAAM,CAAC;IACxCmB,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClCxR,OAAO,CAACyR,iBAAiB,CACvB5V,IAAI,EACJ2V,uBAAuB,EACvBtB,WAAW,EACX;MACEyB,IAAI,EAAExB,MAAM;MACZ8B,IAAI,EAAE7B;KACP,EACD,KAAK,CACN;EACH,CAAC;EAED,MAAM+B,IAAI,GAAGA,CAAC7C,IAAY,EAAEC,EAAU,KAAI;IACxC,MAAMiC,uBAAuB,GAAGxR,OAAO,CAACiR,cAAc,CAACpV,IAAI,CAAC;IAC5DwT,WAAW,CAACmC,uBAAuB,EAAElC,IAAI,EAAEC,EAAE,CAAC;IAC9CF,WAAW,CAAC6B,GAAG,CAACvP,OAAO,EAAE2N,IAAI,EAAEC,EAAE,CAAC;IAClCgC,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAACQ,uBAAuB,CAAC;IAClCxR,OAAO,CAACyR,iBAAiB,CACvB5V,IAAI,EACJ2V,uBAAuB,EACvBnC,WAAW,EACX;MACEsC,IAAI,EAAErC,IAAI;MACV2C,IAAI,EAAE1C;KACP,EACD,KAAK,CACN;EACH,CAAC;EAED,MAAM6C,MAAM,GAAGA,CACbxN,KAAa,EACb3J,KAAgD,KAC9C;IACF,MAAMiJ,WAAW,GAAGrH,WAAW,CAAC5B,KAAK,CAAC;IACtC,MAAMuW,uBAAuB,GAAGZ,QAAQ,CACtC5Q,OAAO,CAACiR,cAAc,CAEpBpV,IAAI,CAAC,EACP+I,KAAK,EACLV,WAAwE,CACzE;IACDgN,GAAG,CAACvP,OAAO,GAAG,CAAC,GAAG6P,uBAAuB,CAAC,CAAC3N,GAAG,CAAC,CAACwO,IAAI,EAAEzC,CAAC,KACrD,CAACyC,IAAI,IAAIzC,CAAC,KAAKhL,KAAK,GAAG4E,UAAU,EAAE,GAAG0H,GAAG,CAACvP,OAAO,CAACiO,CAAC,CAAC,CACrD;IACD2B,YAAY,CAACC,uBAAuB,CAAC;IACrCR,SAAS,CAAC,CAAC,GAAGQ,uBAAuB,CAAC,CAAC;IACvCxR,OAAO,CAACyR,iBAAiB,CACvB5V,IAAI,EACJ2V,uBAAuB,EACvBZ,QAAQ,EACR;MACEe,IAAI,EAAE/M,KAAK;MACXqN,IAAI,EAAE/N;IACP,GACD,IAAI,EACJ,KAAK,CACN;EACH,CAAC;EAED,MAAMQ,OAAO,GACXzJ,KAEwD,IACtD;IACF,MAAMuW,uBAAuB,GAAGvQ,qBAAqB,CAACpE,WAAW,CAAC5B,KAAK,CAAC,CAAC;IACzEiW,GAAG,CAACvP,OAAO,GAAG6P,uBAAuB,CAAC3N,GAAG,CAAC2F,UAAU,CAAC;IACrD+H,YAAY,CAAC,CAAC,GAAGC,uBAAuB,CAAC,CAAC;IAC1CR,SAAS,CAAC,CAAC,GAAGQ,uBAAuB,CAAC,CAAC;IACvCxR,OAAO,CAACyR,iBAAiB,CACvB5V,IAAI,EACJ,CAAC,GAAG2V,uBAAuB,CAAC,EACxB1U,IAAO,IAAQA,IAAI,EACvB,EAAE,EACF,IAAI,EACJ,KAAK,CACN;EACH,CAAC;EAEDuC,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB5B,OAAO,CAACgG,MAAM,CAACC,MAAM,GAAG,KAAK;IAE7B6E,SAAS,CAACjP,IAAI,EAAEmE,OAAO,CAACwD,MAAM,CAAC,IAC7BxD,OAAO,CAACmD,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MAC3B,GAAGjC,OAAO,CAACsC;IACe,EAAC;IAE/B,IACE8O,SAAS,CAACzP,OAAO,KAChB,CAAC4I,kBAAkB,CAACvK,OAAO,CAAC0F,QAAQ,CAAC8E,IAAI,CAAC,CAACC,UAAU,IACpDzK,OAAO,CAACsC,UAAU,CAACgQ,WAAW,CAAC,EACjC;MACA,IAAItS,OAAO,CAAC0F,QAAQ,CAAC6M,QAAQ,EAAE;QAC7BvS,OAAO,CAACwS,cAAc,CAAC,CAAC3W,IAAI,CAAC,CAAC,CAAC4W,IAAI,CAAE3U,MAAM,IAAI;UAC7C,MAAMoJ,KAAK,GAAGxJ,GAAG,CAACI,MAAM,CAACkF,MAAM,EAAEnH,IAAI,CAAC;UACtC,MAAM6W,aAAa,GAAGhV,GAAG,CAACsC,OAAO,CAACsC,UAAU,CAACU,MAAM,EAAEnH,IAAI,CAAC;UAE1D,IACE6W,aAAa,GACR,CAACxL,KAAK,IAAIwL,aAAa,CAAC3X,IAAI,IAC5BmM,KAAK,KACHwL,aAAa,CAAC3X,IAAI,KAAKmM,KAAK,CAACnM,IAAI,IAChC2X,aAAa,CAAC/L,OAAO,KAAKO,KAAK,CAACP,OAAO,CAAE,GAC7CO,KAAK,IAAIA,KAAK,CAACnM,IAAI,EACvB;YACAmM,KAAK,GACDvC,GAAG,CAAC3E,OAAO,CAACsC,UAAU,CAACU,MAAM,EAAEnH,IAAI,EAAEqL,KAAK,CAAC,GAC3CuJ,KAAK,CAACzQ,OAAO,CAACsC,UAAU,CAACU,MAAM,EAAEnH,IAAI,CAAC;YAC1CmE,OAAO,CAACmD,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;cAC3Be,MAAM,EAAEhD,OAAO,CAACsC,UAAU,CAACU;YAC5B,EAAC;;QAEN,CAAC,CAAC;OACH,MAAM;QACL,MAAM4C,KAAK,GAAUlI,GAAG,CAACsC,OAAO,CAAC6F,OAAO,EAAEhK,IAAI,CAAC;QAC/C,IACE+J,KAAK,IACLA,KAAK,CAACE,EAAE,IACR,EACEyE,kBAAkB,CAACvK,OAAO,CAAC0F,QAAQ,CAACiN,cAAc,CAAC,CAAClI,UAAU,IAC9DF,kBAAkB,CAACvK,OAAO,CAAC0F,QAAQ,CAAC8E,IAAI,CAAC,CAACC,UAAU,CACrD,EACD;UACAsC,aAAa,CACXnH,KAAK,EACL5F,OAAO,CAACmE,WAAW,EACnBnE,OAAO,CAAC0F,QAAQ,CAACkN,YAAY,KAAKtU,eAAe,CAACK,GAAG,EACrDqB,OAAO,CAAC0F,QAAQ,CAACsH,yBAAyB,EAC1C,IAAI,CACL,CAACyF,IAAI,CACHvL,KAAK,IACJ,CAACvG,aAAa,CAACuG,KAAK,CAAC,IACrBlH,OAAO,CAACmD,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;YAC3Be,MAAM,EAAEuI,yBAAyB,CAC/BvL,OAAO,CAACsC,UAAU,CAACU,MAAmC,EACtDkE,KAAK,EACLrL,IAAI;UAEP,EAAC,CACL;;;;IAKPmE,OAAO,CAACmD,SAAS,CAACc,MAAM,CAAChC,IAAI,CAAC;MAC5BpG,IAAI;MACJoI,MAAM,EAAE;QAAE,GAAGjE,OAAO,CAACmE;MAAW;IACjC,EAAC;IAEFnE,OAAO,CAACwD,MAAM,CAACgD,KAAK,IAClB0E,qBAAqB,CAAClL,OAAO,CAAC6F,OAAO,EAAE,CAACS,GAAG,EAAEnJ,GAAW,KAAI;MAC1D,IACE6C,OAAO,CAACwD,MAAM,CAACgD,KAAK,IACpBrJ,GAAG,CAACoE,UAAU,CAACvB,OAAO,CAACwD,MAAM,CAACgD,KAAK,CAAC,IACpCF,GAAG,CAACE,KAAK,EACT;QACAF,GAAG,CAACE,KAAK,EAAE;QACX,OAAO,CAAC;;MAEV;IACF,CAAC,CAAC;IAEJxG,OAAO,CAACwD,MAAM,CAACgD,KAAK,GAAG,EAAE;IAEzBxG,OAAO,CAACqD,YAAY,EAAE;IACtB+N,SAAS,CAACzP,OAAO,GAAG,KAAK;GAC1B,EAAE,CAACyE,MAAM,EAAEvK,IAAI,EAAEmE,OAAO,CAAC,CAAC;EAE3BX,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,CAAClE,GAAG,CAACsC,OAAO,CAACmE,WAAW,EAAEtI,IAAI,CAAC,IAAImE,OAAO,CAACyR,iBAAiB,CAAC5V,IAAI,CAAC;IAElE,OAAO,MAAK;MACV,CAACmE,OAAO,CAAC0F,QAAQ,CAACP,gBAAgB,IAAIA,gBAAgB,KACpDnF,OAAO,CAACkG,UAAU,CAACrK,IAA+B,CAAC;IACvD,CAAC;GACF,EAAE,CAACA,IAAI,EAAEmE,OAAO,EAAE+Q,OAAO,EAAE5L,gBAAgB,CAAC,CAAC;EAE9C,OAAO;IACL+M,IAAI,EAAE7S,KAAK,CAACgH,WAAW,CAAC6L,IAAI,EAAE,CAACX,YAAY,EAAE1V,IAAI,EAAEmE,OAAO,CAAC,CAAC;IAC5DmS,IAAI,EAAE9S,KAAK,CAACgH,WAAW,CAAC8L,IAAI,EAAE,CAACZ,YAAY,EAAE1V,IAAI,EAAEmE,OAAO,CAAC,CAAC;IAC5D4R,OAAO,EAAEvS,KAAK,CAACgH,WAAW,CAACuL,OAAO,EAAE,CAACL,YAAY,EAAE1V,IAAI,EAAEmE,OAAO,CAAC,CAAC;IAClEyI,MAAM,EAAEpJ,KAAK,CAACgH,WAAW,CAACoC,MAAM,EAAE,CAAC8I,YAAY,EAAE1V,IAAI,EAAEmE,OAAO,CAAC,CAAC;IAChE8R,MAAM,EAAEzS,KAAK,CAACgH,WAAW,CAACyL,MAAM,EAAE,CAACP,YAAY,EAAE1V,IAAI,EAAEmE,OAAO,CAAC,CAAC;IAChEoP,MAAM,EAAE/P,KAAK,CAACgH,WAAW,CAAC0L,QAAM,EAAE,CAACR,YAAY,EAAE1V,IAAI,EAAEmE,OAAO,CAAC,CAAC;IAChEoS,MAAM,EAAE/S,KAAK,CAACgH,WAAW,CAAC+L,MAAM,EAAE,CAACb,YAAY,EAAE1V,IAAI,EAAEmE,OAAO,CAAC,CAAC;IAChE0E,OAAO,EAAErF,KAAK,CAACgH,WAAW,CAAC3B,OAAO,EAAE,CAAC6M,YAAY,EAAE1V,IAAI,EAAEmE,OAAO,CAAC,CAAC;IAClEoG,MAAM,EAAE/G,KAAK,CAACwT,OAAO,CACnB,MACEzM,MAAM,CAACvC,GAAG,CAAC,CAAC+B,KAAK,EAAEhB,KAAK,MAAM;MAC5B,GAAGgB,KAAK;MACR,CAACmL,OAAO,GAAGG,GAAG,CAACvP,OAAO,CAACiD,KAAK,CAAC,IAAI4E,UAAU;IAC5C,EAAC,CAAgE,EACpE,CAACpD,MAAM,EAAE2K,OAAO,CAAC;GAEpB;AACH;ACzZA,IAAA+B,aAAA,GAAeA,CAAA,KAAoB;EACjC,IAAIC,UAAU,GAAkB,EAAE;EAElC,MAAM9Q,IAAI,GAAIhH,KAAQ,IAAI;IACxB,KAAK,MAAM+X,QAAQ,IAAID,UAAU,EAAE;MACjCC,QAAQ,CAAC/Q,IAAI,IAAI+Q,QAAQ,CAAC/Q,IAAI,CAAChH,KAAK,CAAC;;EAEzC,CAAC;EAED,MAAM+G,SAAS,GAAIgR,QAAqB,IAAkB;IACxDD,UAAU,CAACE,IAAI,CAACD,QAAQ,CAAC;IACzB,OAAO;MACL9Q,WAAW,EAAEA,CAAA,KAAK;QAChB6Q,UAAU,GAAGA,UAAU,CAAC1V,MAAM,CAAE6V,CAAC,IAAKA,CAAC,KAAKF,QAAQ,CAAC;;KAExD;EACH,CAAC;EAED,MAAM9Q,WAAW,GAAGA,CAAA,KAAK;IACvB6Q,UAAU,GAAG,EAAE;EACjB,CAAC;EAED,OAAO;IACL,IAAII,SAASA,CAAA;MACX,OAAOJ,UAAU;KAClB;IACD9Q,IAAI;IACJD,SAAS;IACTE;GACD;AACH,CAAC;ACzCD,IAAAkR,WAAA,GAAgBnY,KAAc,IAC5BE,iBAAiB,CAACF,KAAK,CAAC,IAAI,CAACG,YAAY,CAACH,KAAK,CAAC;ACDpC,SAAUoY,SAASA,CAACC,OAAY,EAAEC,OAAY;EAC1D,IAAIH,WAAW,CAACE,OAAO,CAAC,IAAIF,WAAW,CAACG,OAAO,CAAC,EAAE;IAChD,OAAOD,OAAO,KAAKC,OAAO;;EAG5B,IAAIvY,YAAY,CAACsY,OAAO,CAAC,IAAItY,YAAY,CAACuY,OAAO,CAAC,EAAE;IAClD,OAAOD,OAAO,CAACE,OAAO,EAAE,KAAKD,OAAO,CAACC,OAAO,EAAE;;EAGhD,MAAMC,KAAK,GAAGlT,MAAM,CAACK,IAAI,CAAC0S,OAAO,CAAC;EAClC,MAAMI,KAAK,GAAGnT,MAAM,CAACK,IAAI,CAAC2S,OAAO,CAAC;EAElC,IAAIE,KAAK,CAACrT,MAAM,KAAKsT,KAAK,CAACtT,MAAM,EAAE;IACjC,OAAO,KAAK;;EAGd,KAAK,MAAMjD,GAAG,IAAIsW,KAAK,EAAE;IACvB,MAAME,IAAI,GAAGL,OAAO,CAACnW,GAAG,CAAC;IAEzB,IAAI,CAACuW,KAAK,CAAC/K,QAAQ,CAACxL,GAAG,CAAC,EAAE;MACxB,OAAO,KAAK;;IAGd,IAAIA,GAAG,KAAK,KAAK,EAAE;MACjB,MAAMyW,IAAI,GAAGL,OAAO,CAACpW,GAAG,CAAC;MAEzB,IACGnC,YAAY,CAAC2Y,IAAI,CAAC,IAAI3Y,YAAY,CAAC4Y,IAAI,CAAC,IACxCvY,QAAQ,CAACsY,IAAI,CAAC,IAAItY,QAAQ,CAACuY,IAAI,CAAE,IACjCtY,KAAK,CAACC,OAAO,CAACoY,IAAI,CAAC,IAAIrY,KAAK,CAACC,OAAO,CAACqY,IAAI,CAAE,GACxC,CAACP,SAAS,CAACM,IAAI,EAAEC,IAAI,CAAC,GACtBD,IAAI,KAAKC,IAAI,EACjB;QACA,OAAO,KAAK;;;;EAKlB,OAAO,IAAI;AACb;AC1CA,IAAAC,gBAAA,GAAgB/Y,OAAqB,IACnCA,OAAO,CAACC,IAAI,sBAAsB;ACEpC,IAAAwS,iBAAA,GAAgBjH,GAAiB,IAC/B0F,YAAY,CAAC1F,GAAG,CAAC,IAAIzL,eAAe,CAACyL,GAAG,CAAC;ACF3C,IAAAwN,IAAA,GAAgBxN,GAAQ,IAAKqF,aAAa,CAACrF,GAAG,CAAC,IAAIA,GAAG,CAACyN,WAAW;ACFlE,IAAAC,oBAAA,GAAgB/Y,KAAc,IAC5BI,QAAQ,CAACJ,KAAK,CAAC,IAAIsF,MAAM,CAAC0D,MAAM,CAAChJ,KAAK,CAAC,CAACoG,IAAI,CAAE7D,GAAG,IAAKA,GAAG,CAAC;ACD5D,IAAAyW,iBAAA,GAAmBnX,IAAO,IAAa;EACrC,KAAK,MAAMK,GAAG,IAAIL,IAAI,EAAE;IACtB,IAAI4O,UAAU,CAAC5O,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE;MACzB,OAAO,IAAI;;;EAGf,OAAO,KAAK;AACd,CAAC;ACFD,SAAS+W,eAAeA,CAAIpX,IAAO,EAAkC;EAAA,IAAhCsJ,MAAA,GAAAjG,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAA8B,EAAE;EACnE,MAAMgU,iBAAiB,GAAG7Y,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EAE7C,IAAIzB,QAAQ,CAACyB,IAAI,CAAC,IAAIqX,iBAAiB,EAAE;IACvC,KAAK,MAAMhX,GAAG,IAAIL,IAAI,EAAE;MACtB,IACExB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,IACvB9B,QAAQ,CAACyB,IAAI,CAACK,GAAG,CAAC,CAAC,IAAI,CAAC8W,iBAAiB,CAACnX,IAAI,CAACK,GAAG,CAAC,CAAE,EACtD;QACAiJ,MAAM,CAACjJ,GAAG,CAAC,GAAG7B,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,GAAG,EAAE,GAAG,EAAE;QAChD+W,eAAe,CAACpX,IAAI,CAACK,GAAG,CAAC,EAAEiJ,MAAM,CAACjJ,GAAG,CAAC,CAAC;OACxC,MAAM,IAAI,CAAChC,iBAAiB,CAAC2B,IAAI,CAACK,GAAG,CAAC,CAAC,EAAE;QACxCiJ,MAAM,CAACjJ,GAAG,CAAC,GAAG,IAAI;;;;EAKxB,OAAOiJ,MAAM;AACf;AAEA,SAASgO,+BAA+BA,CACtCtX,IAAO,EACP2G,UAAa,EACb4Q,qBAGC;EAED,MAAMF,iBAAiB,GAAG7Y,KAAK,CAACC,OAAO,CAACuB,IAAI,CAAC;EAE7C,IAAIzB,QAAQ,CAACyB,IAAI,CAAC,IAAIqX,iBAAiB,EAAE;IACvC,KAAK,MAAMhX,GAAG,IAAIL,IAAI,EAAE;MACtB,IACExB,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,IACvB9B,QAAQ,CAACyB,IAAI,CAACK,GAAG,CAAC,CAAC,IAAI,CAAC8W,iBAAiB,CAACnX,IAAI,CAACK,GAAG,CAAC,CAAE,EACtD;QACA,IACEI,WAAW,CAACkG,UAAU,CAAC,IACvB2P,WAAW,CAACiB,qBAAqB,CAAClX,GAAG,CAAC,CAAC,EACvC;UACAkX,qBAAqB,CAAClX,GAAG,CAAC,GAAG7B,KAAK,CAACC,OAAO,CAACuB,IAAI,CAACK,GAAG,CAAC,CAAC,GACjD+W,eAAe,CAACpX,IAAI,CAACK,GAAG,CAAC,EAAE,EAAE,CAAC,GAC9B;YAAE,GAAG+W,eAAe,CAACpX,IAAI,CAACK,GAAG,CAAC;UAAC,CAAE;SACtC,MAAM;UACLiX,+BAA+B,CAC7BtX,IAAI,CAACK,GAAG,CAAC,EACThC,iBAAiB,CAACsI,UAAU,CAAC,GAAG,EAAE,GAAGA,UAAU,CAACtG,GAAG,CAAC,EACpDkX,qBAAqB,CAAClX,GAAG,CAAC,CAC3B;;OAEJ,MAAM;QACLkX,qBAAqB,CAAClX,GAAG,CAAC,GAAG,CAACkW,SAAS,CAACvW,IAAI,CAACK,GAAG,CAAC,EAAEsG,UAAU,CAACtG,GAAG,CAAC,CAAC;;;;EAKzE,OAAOkX,qBAAqB;AAC9B;AAEA,IAAAC,cAAA,GAAeA,CAAIjU,aAAgB,EAAEoD,UAAa,KAChD2Q,+BAA+B,CAC7B/T,aAAa,EACboD,UAAU,EACVyQ,eAAe,CAACzQ,UAAU,CAAC,CAC5B;ACnEH,IAAA8Q,eAAA,GAAeA,CACbtZ,KAAQ,EAAAuZ,KAAA;EAAA,IACR;IAAEtH,aAAa;IAAEoB,WAAW;IAAEmG;EAAU,CAAe,GAAAD,KAAA;EAAA,OAEvDjX,WAAW,CAACtC,KAAK,CAAC,GACdA,KAAK,GACLiS,aAAa,GACbjS,KAAK,KAAK,EAAE,GACVyZ,GAAG,GACHzZ,KAAK,GACL,CAACA,KAAK,GACNA,KAAK,GACPqT,WAAW,IAAIhL,QAAQ,CAACrI,KAAK,CAAC,GAC9B,IAAIC,IAAI,CAACD,KAAK,CAAC,GACfwZ,UAAU,GACVA,UAAU,CAACxZ,KAAK,CAAC,GACjBA,KAAK;AAAA;ACTa,SAAA0Z,aAAaA,CAAC7O,EAAe;EACnD,MAAMQ,GAAG,GAAGR,EAAE,CAACQ,GAAG;EAElB,IAAIR,EAAE,CAACwF,IAAI,GAAGxF,EAAE,CAACwF,IAAI,CAACsB,KAAK,CAAEtG,GAAG,IAAKA,GAAG,CAACxE,QAAQ,CAAC,GAAGwE,GAAG,CAACxE,QAAQ,EAAE;IACjE;;EAGF,IAAI2J,WAAW,CAACnF,GAAG,CAAC,EAAE;IACpB,OAAOA,GAAG,CAACsO,KAAK;;EAGlB,IAAI5I,YAAY,CAAC1F,GAAG,CAAC,EAAE;IACrB,OAAOmG,aAAa,CAAC3G,EAAE,CAACwF,IAAI,CAAC,CAACrQ,KAAK;;EAGrC,IAAI4Y,gBAAgB,CAACvN,GAAG,CAAC,EAAE;IACzB,OAAO,CAAC,GAAGA,GAAG,CAACuO,eAAe,CAAC,CAAChR,GAAG,CAACiR,KAAA;MAAA,IAAC;QAAE7Z;MAAK,CAAE,GAAA6Z,KAAA;MAAA,OAAK7Z,KAAK;IAAA,EAAC;;EAG3D,IAAIJ,eAAU,CAACyL,GAAG,CAAC,EAAE;IACnB,OAAO+F,gBAAgB,CAACvG,EAAE,CAACwF,IAAI,CAAC,CAACrQ,KAAK;;EAGxC,OAAOsZ,eAAe,CAAChX,WAAW,CAAC+I,GAAG,CAACrL,KAAK,CAAC,GAAG6K,EAAE,CAACQ,GAAG,CAACrL,KAAK,GAAGqL,GAAG,CAACrL,KAAK,EAAE6K,EAAE,CAAC;AAC/E;ACxBA,IAAAiP,kBAAA,GAAeA,CACb5J,WAAyD,EACzDtF,OAAkB,EAClB+M,YAA2B,EAC3B5F,yBAA+C,KAC7C;EACF,MAAM5G,MAAM,GAA2C,EAAE;EAEzD,KAAK,MAAMvK,IAAI,IAAIsP,WAAW,EAAE;IAC9B,MAAMvF,KAAK,GAAUlI,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC;IAEvC+J,KAAK,IAAIjB,GAAG,CAACyB,MAAM,EAAEvK,IAAI,EAAE+J,KAAK,CAACE,EAAE,CAAC;;EAGtC,OAAO;IACL8M,YAAY;IACZ3W,KAAK,EAAE,CAAC,GAAGkP,WAAW,CAA8B;IACpD/E,MAAM;IACN4G;GACD;AACH,CAAC;ACtBD,IAAAgI,YAAA,GACEC,IAAoD,IAEpD1X,WAAW,CAAC0X,IAAI,CAAC,GACbA,IAAI,GACJhJ,OAAO,CAACgJ,IAAI,CAAC,GACbA,IAAI,CAACC,MAAM,GACX7Z,QAAQ,CAAC4Z,IAAI,CAAC,GACdhJ,OAAO,CAACgJ,IAAI,CAACha,KAAK,CAAC,GACjBga,IAAI,CAACha,KAAK,CAACia,MAAM,GACjBD,IAAI,CAACha,KAAK,GACZga,IAAI;AClBV,IAAAE,aAAA,GAAgBjL,OAAoB,IAClCA,OAAO,CAACnE,KAAK,KACZmE,OAAO,CAAChL,QAAQ,IACfgL,OAAO,CAACpL,GAAG,IACXoL,OAAO,CAACrL,GAAG,IACXqL,OAAO,CAACnL,SAAS,IACjBmL,OAAO,CAAClL,SAAS,IACjBkL,OAAO,CAACjL,OAAO,IACfiL,OAAO,CAAC/K,QAAQ,CAAC;ACNG,SAAAiW,iBAAiBA,CACvCpS,MAAsB,EACtB6C,OAAoB,EACpBhK,IAAY;EAKZ,MAAMqL,KAAK,GAAGxJ,GAAG,CAACsF,MAAM,EAAEnH,IAAI,CAAC;EAE/B,IAAIqL,KAAK,IAAI5C,KAAK,CAACzI,IAAI,CAAC,EAAE;IACxB,OAAO;MACLqL,KAAK;MACLrL;KACD;;EAGH,MAAMI,KAAK,GAAGJ,IAAI,CAACkC,KAAK,CAAC,GAAG,CAAC;EAE7B,OAAO9B,KAAK,CAACmE,MAAM,EAAE;IACnB,MAAM0D,SAAS,GAAG7H,KAAK,CAACoZ,IAAI,CAAC,GAAG,CAAC;IACjC,MAAMzP,KAAK,GAAGlI,GAAG,CAACmI,OAAO,EAAE/B,SAAS,CAAC;IACrC,MAAMwR,UAAU,GAAG5X,GAAG,CAACsF,MAAM,EAAEc,SAAS,CAAC;IAEzC,IAAI8B,KAAK,IAAI,CAACtK,KAAK,CAACC,OAAO,CAACqK,KAAK,CAAC,IAAI/J,IAAI,KAAKiI,SAAS,EAAE;MACxD,OAAO;QAAEjI;MAAI,CAAE;;IAGjB,IAAIyZ,UAAU,IAAIA,UAAU,CAACva,IAAI,EAAE;MACjC,OAAO;QACLc,IAAI,EAAEiI,SAAS;QACfoD,KAAK,EAAEoO;OACR;;IAGHrZ,KAAK,CAACsZ,GAAG,EAAE;;EAGb,OAAO;IACL1Z;GACD;AACH;AC3CA,IAAA2Z,cAAA,GAAeA,CACbzK,WAAoB,EACpB9D,SAAkB,EAClBqL,WAAoB,EACpBK,cAGC,EACDnI,IAAkC,KAChC;EACF,IAAIA,IAAI,CAACI,OAAO,EAAE;IAChB,OAAO,KAAK;GACb,MAAM,IAAI,CAAC0H,WAAW,IAAI9H,IAAI,CAACK,SAAS,EAAE;IACzC,OAAO,EAAE5D,SAAS,IAAI8D,WAAW,CAAC;GACnC,MAAM,IAAIuH,WAAW,GAAGK,cAAc,CAACjI,QAAQ,GAAGF,IAAI,CAACE,QAAQ,EAAE;IAChE,OAAO,CAACK,WAAW;GACpB,MAAM,IAAIuH,WAAW,GAAGK,cAAc,CAAChI,UAAU,GAAGH,IAAI,CAACG,UAAU,EAAE;IACpE,OAAOI,WAAW;;EAEpB,OAAO,IAAI;AACb,CAAC;AClBD,IAAA0K,eAAA,GAAeA,CAAInP,GAAM,EAAEzK,IAAY,KACrC,CAACuB,OAAO,CAACM,GAAG,CAAC4I,GAAG,EAAEzK,IAAI,CAAC,CAAC,CAACuE,MAAM,IAAIqQ,KAAK,CAACnK,GAAG,EAAEzK,IAAI,CAAC;ACmFrD,MAAM6Z,cAAc,GAAG;EACrBlL,IAAI,EAAElM,eAAe,CAACG,QAAQ;EAC9BkU,cAAc,EAAErU,eAAe,CAACE,QAAQ;EACxCmX,gBAAgB,EAAE;CACV;AAEM,SAAAC,iBAAiBA,CAAA,EAIiB;EAAA,IAAhDlW,KAAA,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAA8C,EAAE;EAEhD,IAAIuF,QAAQ,GAAG;IACb,GAAGgQ,cAAc;IACjB,GAAGhW;GACJ;EACD,IAAI4C,UAAU,GAA4B;IACxCuT,WAAW,EAAE,CAAC;IACdpT,OAAO,EAAE,KAAK;IACdC,SAAS,EAAEgJ,UAAU,CAAChG,QAAQ,CAACrF,aAAa,CAAC;IAC7CyC,YAAY,EAAE,KAAK;IACnBwP,WAAW,EAAE,KAAK;IAClBwD,YAAY,EAAE,KAAK;IACnB7M,kBAAkB,EAAE,KAAK;IACzBlG,OAAO,EAAE,KAAK;IACdH,aAAa,EAAE,EAAE;IACjBD,WAAW,EAAE,EAAE;IACfE,gBAAgB,EAAE,EAAE;IACpBG,MAAM,EAAE0C,QAAQ,CAAC1C,MAAM,IAAI,EAAE;IAC7BlB,QAAQ,EAAE4D,QAAQ,CAAC5D,QAAQ,IAAI;GAChC;EACD,IAAI+D,OAAO,GAAc,EAAE;EAC3B,IAAIvF,cAAc,GAChBjF,QAAQ,CAACqK,QAAQ,CAACrF,aAAa,CAAC,IAAIhF,QAAQ,CAACqK,QAAQ,CAACzB,MAAM,CAAC,GACzDpH,WAAW,CAAC6I,QAAQ,CAACrF,aAAa,IAAIqF,QAAQ,CAACzB,MAAM,CAAC,IAAI,EAAE,GAC5D,EAAE;EACR,IAAIE,WAAW,GAAGuB,QAAQ,CAACP,gBAAgB,GACvC,EAAE,GACFtI,WAAW,CAACyD,cAAc,CAAC;EAC/B,IAAI0F,MAAM,GAAG;IACXC,MAAM,EAAE,KAAK;IACbF,KAAK,EAAE,KAAK;IACZpC,KAAK,EAAE;GACR;EACD,IAAIH,MAAM,GAAU;IAClBuC,KAAK,EAAE,IAAI/I,GAAG,EAAE;IAChB+Y,OAAO,EAAE,IAAI/Y,GAAG,EAAE;IAClBqI,KAAK,EAAE,IAAIrI,GAAG,EAAE;IAChB2G,KAAK,EAAE,IAAI3G,GAAG;GACf;EACD,IAAIgZ,kBAAwC;EAC5C,IAAIC,KAAK,GAAG,CAAC;EACb,MAAMvV,eAAe,GAAkB;IACrC+B,OAAO,EAAE,KAAK;IACdE,WAAW,EAAE,KAAK;IAClBE,gBAAgB,EAAE,KAAK;IACvBD,aAAa,EAAE,KAAK;IACpBE,YAAY,EAAE,KAAK;IACnBC,OAAO,EAAE,KAAK;IACdC,MAAM,EAAE;GACT;EACD,MAAMG,SAAS,GAA2B;IACxCc,MAAM,EAAE6O,aAAa,EAAE;IACvBzN,KAAK,EAAEyN,aAAa,EAAE;IACtB1P,KAAK,EAAE0P,aAAa;GACrB;EACD,MAAMoD,0BAA0B,GAAG3L,kBAAkB,CAAC7E,QAAQ,CAAC8E,IAAI,CAAC;EACpE,MAAM2L,yBAAyB,GAAG5L,kBAAkB,CAAC7E,QAAQ,CAACiN,cAAc,CAAC;EAC7E,MAAMyD,gCAAgC,GACpC1Q,QAAQ,CAACkN,YAAY,KAAKtU,eAAe,CAACK,GAAG;EAE/C,MAAM0X,QAAQ,GACSC,QAAW,IAC/BC,IAAY,IAAI;IACfC,YAAY,CAACP,KAAK,CAAC;IACnBA,KAAK,GAAGQ,UAAU,CAACH,QAAQ,EAAEC,IAAI,CAAC;EACpC,CAAC;EAEH,MAAMlT,YAAY,GAAG,MAAOqT,iBAA2B,IAAI;IACzD,IAAIhW,eAAe,CAACqC,OAAO,IAAI2T,iBAAiB,EAAE;MAChD,MAAM3T,OAAO,GAAG2C,QAAQ,CAAC6M,QAAQ,GAC7B5R,aAAa,CAAC,CAAC,MAAM6R,cAAc,EAAE,EAAExP,MAAM,CAAC,GAC9C,MAAM2T,wBAAwB,CAAC9Q,OAAO,EAAE,IAAI,CAAC;MAEjD,IAAI9C,OAAO,KAAKT,UAAU,CAACS,OAAO,EAAE;QAClCI,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;UACnBc;QACD,EAAC;;;EAGR,CAAC;EAED,MAAM6T,mBAAmB,GAAGA,CAAC9T,YAAqB,EAAE7G,KAAe,KAAI;IACrE,IAAI,EAAEyE,eAAe,CAACoC,YAAY,IAAIpC,eAAe,CAACmC,gBAAgB,CAAC,EAAE;MACvE;;IAEF5G,KAAK,CAAC4a,OAAO,CAAEhb,IAAI,IAAI;MACrB8I,GAAG,CAACrC,UAAU,CAACO,gBAAgB,EAAEhH,IAAI,EAAEiH,YAAY,CAAC;IACtD,CAAC,CAAC;IACFR,UAAU,CAACQ,YAAY,GAAGkR,oBAAoB,CAAC1R,UAAU,CAACO,gBAAgB,CAAC;IAC3EM,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MACnBY,gBAAgB,EAAEP,UAAU,CAACO,gBAAgB;MAC7CC,YAAY,EAAER,UAAU,CAACQ;IAC1B,EAAC;EACJ,CAAC;EAED,MAAM2O,iBAAiB,GAA0B,SAAAA,CAC/C5V,IAAI,EAMF;IAAA,IALFoI,MAAM,GAAA9D,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAG,EAAE;IAAA,IACXsH,MAAM,GAAAtH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAA1C,SAAA;IAAA,IACNqZ,IAAI,GAAA3W,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAA1C,SAAA;IAAA,IACJsZ,eAAe,GAAA5W,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAG,IAAI;IAAA,IACtB6W,0BAA0B,GAAA7W,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAG,IAAI;IAEjC,IAAI2W,IAAI,IAAIrP,MAAM,EAAE;MAClBzB,MAAM,CAACC,MAAM,GAAG,IAAI;MACpB,IAAI+Q,0BAA0B,IAAI1b,KAAK,CAACC,OAAO,CAACmC,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC,CAAC,EAAE;QACnE,MAAMgV,WAAW,GAAGpJ,MAAM,CAAC/J,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC,EAAEib,IAAI,CAACnF,IAAI,EAAEmF,IAAI,CAAC7E,IAAI,CAAC;QACpE8E,eAAe,IAAIpS,GAAG,CAACkB,OAAO,EAAEhK,IAAI,EAAEgV,WAAW,CAAC;;MAGpD,IACEmG,0BAA0B,IAC1B1b,KAAK,CAACC,OAAO,CAACmC,GAAG,CAAC4E,UAAU,CAACU,MAAM,EAAEnH,IAAI,CAAC,CAAC,EAC3C;QACA,MAAMmH,MAAM,GAAGyE,MAAM,CACnB/J,GAAG,CAAC4E,UAAU,CAACU,MAAM,EAAEnH,IAAI,CAAC,EAC5Bib,IAAI,CAACnF,IAAI,EACTmF,IAAI,CAAC7E,IAAI,CACV;QACD8E,eAAe,IAAIpS,GAAG,CAACrC,UAAU,CAACU,MAAM,EAAEnH,IAAI,EAAEmH,MAAM,CAAC;QACvDyS,eAAe,CAACnT,UAAU,CAACU,MAAM,EAAEnH,IAAI,CAAC;;MAG1C,IACE6E,eAAe,CAACkC,aAAa,IAC7BoU,0BAA0B,IAC1B1b,KAAK,CAACC,OAAO,CAACmC,GAAG,CAAC4E,UAAU,CAACM,aAAa,EAAE/G,IAAI,CAAC,CAAC,EAClD;QACA,MAAM+G,aAAa,GAAG6E,MAAM,CAC1B/J,GAAG,CAAC4E,UAAU,CAACM,aAAa,EAAE/G,IAAI,CAAC,EACnCib,IAAI,CAACnF,IAAI,EACTmF,IAAI,CAAC7E,IAAI,CACV;QACD8E,eAAe,IAAIpS,GAAG,CAACrC,UAAU,CAACM,aAAa,EAAE/G,IAAI,EAAE+G,aAAa,CAAC;;MAGvE,IAAIlC,eAAe,CAACiC,WAAW,EAAE;QAC/BL,UAAU,CAACK,WAAW,GAAG2R,cAAc,CAAChU,cAAc,EAAE6D,WAAW,CAAC;;MAGtEhB,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;QACnBpG,IAAI;QACJ4G,OAAO,EAAEwU,SAAS,CAACpb,IAAI,EAAEoI,MAAM,CAAC;QAChCtB,WAAW,EAAEL,UAAU,CAACK,WAAW;QACnCK,MAAM,EAAEV,UAAU,CAACU,MAAM;QACzBD,OAAO,EAAET,UAAU,CAACS;MACrB,EAAC;KACH,MAAM;MACL4B,GAAG,CAACR,WAAW,EAAEtI,IAAI,EAAEoI,MAAM,CAAC;;EAElC,CAAC;EAED,MAAMiT,YAAY,GAAGA,CAACrb,IAAuB,EAAEqL,KAAiB,KAAI;IAClEvC,GAAG,CAACrC,UAAU,CAACU,MAAM,EAAEnH,IAAI,EAAEqL,KAAK,CAAC;IACnC/D,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MACnBe,MAAM,EAAEV,UAAU,CAACU;IACpB,EAAC;EACJ,CAAC;EAED,MAAMmU,UAAU,GAAInU,MAAiC,IAAI;IACvDV,UAAU,CAACU,MAAM,GAAGA,MAAM;IAC1BG,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MACnBe,MAAM,EAAEV,UAAU,CAACU,MAAM;MACzBD,OAAO,EAAE;IACV,EAAC;EACJ,CAAC;EAED,MAAMqU,mBAAmB,GAAGA,CAC1Bvb,IAAuB,EACvBwb,oBAA6B,EAC7Bpc,KAAe,EACfqL,GAAS,KACP;IACF,MAAMV,KAAK,GAAUlI,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC;IAEvC,IAAI+J,KAAK,EAAE;MACT,MAAM/H,YAAY,GAAGH,GAAG,CACtByG,WAAW,EACXtI,IAAI,EACJ0B,WAAW,CAACtC,KAAK,CAAC,GAAGyC,GAAG,CAAC4C,cAAc,EAAEzE,IAAI,CAAC,GAAGZ,KAAK,CACvD;MAEDsC,WAAW,CAACM,YAAY,CAAC,IACxByI,GAAG,IAAKA,GAAwB,CAACgR,cAAe,IACjDD,oBAAoB,GAChB1S,GAAG,CACDR,WAAW,EACXtI,IAAI,EACJwb,oBAAoB,GAAGxZ,YAAY,GAAG8W,aAAa,CAAC/O,KAAK,CAACE,EAAE,CAAC,CAC9D,GACDyR,aAAa,CAAC1b,IAAI,EAAEgC,YAAY,CAAC;MAErCmI,MAAM,CAACD,KAAK,IAAI1C,YAAY,EAAE;;EAElC,CAAC;EAED,MAAMmU,mBAAmB,GAAGA,CAC1B3b,IAAuB,EACvB4b,UAAmB,EACnB1M,WAAqB,EACrB2M,WAAqB,EACrBC,YAAsB,KAGpB;IACF,IAAIC,iBAAiB,GAAG,KAAK;IAC7B,IAAIC,eAAe,GAAG,KAAK;IAC3B,MAAMC,MAAM,GAAwD;MAClEjc;KACD;IACD,MAAMkc,aAAa,GAAG,CAAC,EACrBra,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC,IAAI6B,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC,CAACiK,EAAE,CAAChE,QAAQ,CACrD;IAED,IAAI,CAACiJ,WAAW,IAAI2M,WAAW,EAAE;MAC/B,IAAIhX,eAAe,CAAC+B,OAAO,EAAE;QAC3BoV,eAAe,GAAGvV,UAAU,CAACG,OAAO;QACpCH,UAAU,CAACG,OAAO,GAAGqV,MAAM,CAACrV,OAAO,GAAGwU,SAAS,EAAE;QACjDW,iBAAiB,GAAGC,eAAe,KAAKC,MAAM,CAACrV,OAAO;;MAGxD,MAAMuV,sBAAsB,GAC1BD,aAAa,IAAI1E,SAAS,CAAC3V,GAAG,CAAC4C,cAAc,EAAEzE,IAAI,CAAC,EAAE4b,UAAU,CAAC;MAEnEI,eAAe,GAAG,CAAC,EAAE,CAACE,aAAa,IAAIra,GAAG,CAAC4E,UAAU,CAACK,WAAW,EAAE9G,IAAI,CAAC,CAAC;MACzEmc,sBAAsB,IAAID,aAAa,GACnCtH,KAAK,CAACnO,UAAU,CAACK,WAAW,EAAE9G,IAAI,CAAC,GACnC8I,GAAG,CAACrC,UAAU,CAACK,WAAW,EAAE9G,IAAI,EAAE,IAAI,CAAC;MAC3Cic,MAAM,CAACnV,WAAW,GAAGL,UAAU,CAACK,WAAW;MAC3CiV,iBAAiB,GACfA,iBAAiB,IAChBlX,eAAe,CAACiC,WAAW,IAC1BkV,eAAe,KAAK,CAACG,sBAAuB;;IAGlD,IAAIjN,WAAW,EAAE;MACf,MAAMkN,sBAAsB,GAAGva,GAAG,CAAC4E,UAAU,CAACM,aAAa,EAAE/G,IAAI,CAAC;MAElE,IAAI,CAACoc,sBAAsB,EAAE;QAC3BtT,GAAG,CAACrC,UAAU,CAACM,aAAa,EAAE/G,IAAI,EAAEkP,WAAW,CAAC;QAChD+M,MAAM,CAAClV,aAAa,GAAGN,UAAU,CAACM,aAAa;QAC/CgV,iBAAiB,GACfA,iBAAiB,IAChBlX,eAAe,CAACkC,aAAa,IAC5BqV,sBAAsB,KAAKlN,WAAY;;;IAI/C6M,iBAAiB,IAAID,YAAY,IAAIxU,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC6V,MAAM,CAAC;IAEjE,OAAOF,iBAAiB,GAAGE,MAAM,GAAG,EAAE;EACxC,CAAC;EAED,MAAMI,mBAAmB,GAAGA,CAC1Brc,IAAuB,EACvBkH,OAAiB,EACjBmE,KAAkB,EAClBL,UAIC,KACC;IACF,MAAMsR,kBAAkB,GAAGza,GAAG,CAAC4E,UAAU,CAACU,MAAM,EAAEnH,IAAI,CAAC;IACvD,MAAM6a,iBAAiB,GACrBhW,eAAe,CAACqC,OAAO,IACvB9E,SAAS,CAAC8E,OAAO,CAAC,IAClBT,UAAU,CAACS,OAAO,KAAKA,OAAO;IAEhC,IAAIrD,KAAK,CAAC0Y,UAAU,IAAIlR,KAAK,EAAE;MAC7B8O,kBAAkB,GAAGK,QAAQ,CAAC,MAAMa,YAAY,CAACrb,IAAI,EAAEqL,KAAK,CAAC,CAAC;MAC9D8O,kBAAkB,CAACtW,KAAK,CAAC0Y,UAAU,CAAC;KACrC,MAAM;MACL5B,YAAY,CAACP,KAAK,CAAC;MACnBD,kBAAkB,GAAG,IAAI;MACzB9O,KAAK,GACDvC,GAAG,CAACrC,UAAU,CAACU,MAAM,EAAEnH,IAAI,EAAEqL,KAAK,CAAC,GACnCuJ,KAAK,CAACnO,UAAU,CAACU,MAAM,EAAEnH,IAAI,CAAC;;IAGpC,IACE,CAACqL,KAAK,GAAG,CAACmM,SAAS,CAAC8E,kBAAkB,EAAEjR,KAAK,CAAC,GAAGiR,kBAAkB,KACnE,CAACxX,aAAa,CAACkG,UAAU,CAAC,IAC1B6P,iBAAiB,EACjB;MACA,MAAM2B,gBAAgB,GAAG;QACvB,GAAGxR,UAAU;QACb,IAAI6P,iBAAiB,IAAIzY,SAAS,CAAC8E,OAAO,CAAC,GAAG;UAAEA;QAAO,CAAE,GAAG,EAAE,CAAC;QAC/DC,MAAM,EAAEV,UAAU,CAACU,MAAM;QACzBnH;OACD;MAEDyG,UAAU,GAAG;QACX,GAAGA,UAAU;QACb,GAAG+V;OACJ;MAEDlV,SAAS,CAACC,KAAK,CAACnB,IAAI,CAACoW,gBAAgB,CAAC;;IAGxCzB,mBAAmB,CACjB,KAAK,EACLrW,MAAM,CAACK,IAAI,CAAC0B,UAAU,CAACO,gBAAgB,CAAC,CAACxF,MAAM,CAAEF,GAAG,IAAKA,GAAG,KAAKtB,IAAI,CAAC,CACvE;EACH,CAAC;EAED,MAAM2W,cAAc,GAAG,MAAO3W,IAA0B,IACtD6J,QAAQ,CAAC6M,QAAS,CAChBpO,WAA2B,EAC3BuB,QAAQ,CAAC4S,OAAO,EAChBvD,kBAAkB,CAChBlZ,IAAI,IAAI2H,MAAM,CAACuC,KAAK,EACpBF,OAAO,EACPH,QAAQ,CAACkN,YAAY,EACrBlN,QAAQ,CAACsH,yBAAyB,CACnC,CACF;EAEH,MAAMuL,2BAA2B,GAAG,MAAOtc,KAA2B,IAAI;IACxE,MAAM;MAAE+G;IAAM,CAAE,GAAG,MAAMwP,cAAc,CAACvW,KAAK,CAAC;IAE9C,IAAIA,KAAK,EAAE;MACT,KAAK,MAAMJ,IAAI,IAAII,KAAK,EAAE;QACxB,MAAMiL,KAAK,GAAGxJ,GAAG,CAACsF,MAAM,EAAEnH,IAAI,CAAC;QAC/BqL,KAAK,GACDvC,GAAG,CAACrC,UAAU,CAACU,MAAM,EAAEnH,IAAI,EAAEqL,KAAK,CAAC,GACnCuJ,KAAK,CAACnO,UAAU,CAACU,MAAM,EAAEnH,IAAI,CAAC;;KAErC,MAAM;MACLyG,UAAU,CAACU,MAAM,GAAGA,MAAM;;IAG5B,OAAOA,MAAM;EACf,CAAC;EAED,MAAM2T,wBAAwB,GAAG,eAAAA,CAC/BvQ,MAAiB,EACjBoS,oBAA8B,EAM5B;IAAA,IALFF,OAEI,GAAAnY,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA;MACFsY,KAAK,EAAE;IACR;IAED,KAAK,MAAM5c,IAAI,IAAIuK,MAAM,EAAE;MACzB,MAAMR,KAAK,GAAGQ,MAAM,CAACvK,IAAI,CAAC;MAE1B,IAAI+J,KAAK,EAAE;QACT,MAAM;UAAEE,EAAE;UAAE,GAAG2R;QAAU,CAAE,GAAG7R,KAAK;QAEnC,IAAIE,EAAE,EAAE;UACN,MAAM4S,gBAAgB,GAAGlV,MAAM,CAAC6B,KAAK,CAACnJ,GAAG,CAAC4J,EAAE,CAACjK,IAAI,CAAC;UAClD,MAAM8c,UAAU,GAAG,MAAM5L,aAAa,CACpCnH,KAAK,EACLzB,WAAW,EACXiS,gCAAgC,EAChC1Q,QAAQ,CAACsH,yBAAyB,IAAI,CAACwL,oBAAoB,EAC3DE,gBAAgB,CACjB;UAED,IAAIC,UAAU,CAAC7S,EAAE,CAACjK,IAAI,CAAC,EAAE;YACvByc,OAAO,CAACG,KAAK,GAAG,KAAK;YACrB,IAAID,oBAAoB,EAAE;cACxB;;;UAIJ,CAACA,oBAAoB,KAClB9a,GAAG,CAACib,UAAU,EAAE7S,EAAE,CAACjK,IAAI,CAAC,GACrB6c,gBAAgB,GACdnN,yBAAyB,CACvBjJ,UAAU,CAACU,MAAM,EACjB2V,UAAU,EACV7S,EAAE,CAACjK,IAAI,CACR,GACD8I,GAAG,CAACrC,UAAU,CAACU,MAAM,EAAE8C,EAAE,CAACjK,IAAI,EAAE8c,UAAU,CAAC7S,EAAE,CAACjK,IAAI,CAAC,CAAC,GACtD4U,KAAK,CAACnO,UAAU,CAACU,MAAM,EAAE8C,EAAE,CAACjK,IAAI,CAAC,CAAC;;QAG1C4b,UAAU,KACP,MAAMd,wBAAwB,CAC7Bc,UAAU,EACVe,oBAAoB,EACpBF,OAAO,CACR,CAAC;;;IAIR,OAAOA,OAAO,CAACG,KAAK;EACtB,CAAC;EAED,MAAMpU,gBAAgB,GAAGA,CAAA,KAAK;IAC5B,KAAK,MAAMxI,IAAI,IAAI2H,MAAM,CAACuS,OAAO,EAAE;MACjC,MAAMnQ,KAAK,GAAUlI,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC;MAEvC+J,KAAK,KACFA,KAAK,CAACE,EAAE,CAACwF,IAAI,GACV1F,KAAK,CAACE,EAAE,CAACwF,IAAI,CAACsB,KAAK,CAAEtG,GAAG,IAAK,CAACwN,IAAI,CAACxN,GAAG,CAAC,CAAC,GACxC,CAACwN,IAAI,CAAClO,KAAK,CAACE,EAAE,CAACQ,GAAG,CAAC,CAAC,IACxBJ,UAAU,CAACrK,IAA+B,CAAC;;IAG/C2H,MAAM,CAACuS,OAAO,GAAG,IAAI/Y,GAAG,EAAE;EAC5B,CAAC;EAED,MAAMia,SAAS,GAAeA,CAACpb,IAAI,EAAEiB,IAAI,MACvCjB,IAAI,IAAIiB,IAAI,IAAI6H,GAAG,CAACR,WAAW,EAAEtI,IAAI,EAAEiB,IAAI,CAAC,EAC5C,CAACuW,SAAS,CAACuF,SAAS,EAAE,EAAEtY,cAAc,CAAC,CACxC;EAED,MAAM8D,SAAS,GAAgCA,CAC7CnI,KAAK,EACL4B,YAAY,EACZ6F,QAAQ,KAERH,mBAAmB,CACjBtH,KAAK,EACLuH,MAAM,EACN;IACE,IAAIwC,MAAM,CAACD,KAAK,GACZ5B,WAAW,GACX5G,WAAW,CAACM,YAAY,CAAC,GACzByC,cAAc,GACdgD,QAAQ,CAACrH,KAAK,CAAC,GACf;MAAE,CAACA,KAAK,GAAG4B;IAAY,CAAE,GACzBA,YAAY;EACjB,GACD6F,QAAQ,EACR7F,YAAY,CACb;EAEH,MAAMoT,cAAc,GAClBpV,IAAuB,IAEvBuB,OAAO,CACLM,GAAG,CACDsI,MAAM,CAACD,KAAK,GAAG5B,WAAW,GAAG7D,cAAc,EAC3CzE,IAAI,EACJ6D,KAAK,CAACyF,gBAAgB,GAAGzH,GAAG,CAAC4C,cAAc,EAAEzE,IAAI,EAAE,EAAE,CAAC,GAAG,EAAE,CAC5D,CACF;EAEH,MAAM0b,aAAa,GAAG,SAAAA,CACpB1b,IAAuB,EACvBZ,KAAkC,EAEhC;IAAA,IADFiP,OAAA,GAAA/J,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAA0B,EAAE;IAE5B,MAAMyF,KAAK,GAAUlI,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC;IACvC,IAAI4b,UAAU,GAAYxc,KAAK;IAE/B,IAAI2K,KAAK,EAAE;MACT,MAAMiT,cAAc,GAAGjT,KAAK,CAACE,EAAE;MAE/B,IAAI+S,cAAc,EAAE;QAClB,CAACA,cAAc,CAAC/W,QAAQ,IACtB6C,GAAG,CAACR,WAAW,EAAEtI,IAAI,EAAE0Y,eAAe,CAACtZ,KAAK,EAAE4d,cAAc,CAAC,CAAC;QAEhEpB,UAAU,GACR9L,aAAa,CAACkN,cAAc,CAACvS,GAAG,CAAC,IAAInL,iBAAiB,CAACF,KAAK,CAAC,GACzD,EAAE,GACFA,KAAK;QAEX,IAAI4Y,gBAAgB,CAACgF,cAAc,CAACvS,GAAG,CAAC,EAAE;UACxC,CAAC,GAAGuS,cAAc,CAACvS,GAAG,CAAC4D,OAAO,CAAC,CAAC2M,OAAO,CACpCiC,SAAS,IACPA,SAAS,CAACC,QAAQ,GACjBtB,UACD,CAAC9O,QAAQ,CAACmQ,SAAS,CAAC7d,KAAK,CAAE,CAC/B;SACF,MAAM,IAAI4d,cAAc,CAACvN,IAAI,EAAE;UAC9B,IAAIzQ,eAAe,CAACge,cAAc,CAACvS,GAAG,CAAC,EAAE;YACvCuS,cAAc,CAACvN,IAAI,CAAClL,MAAM,GAAG,CAAC,GAC1ByY,cAAc,CAACvN,IAAI,CAACuL,OAAO,CACxBmC,WAAW,IACV,CAAC,CAACA,WAAW,CAAC1B,cAAc,IAAI,CAAC0B,WAAW,CAAClX,QAAQ,MACpDkX,WAAW,CAACrd,OAAO,GAAGL,KAAK,CAACC,OAAO,CAACkc,UAAU,CAAC,GAC5C,CAAC,CAAEA,UAAiB,CAACzW,IAAI,CACtBlE,IAAY,IAAKA,IAAI,KAAKkc,WAAW,CAAC/d,KAAK,CAC7C,GACDwc,UAAU,KAAKuB,WAAW,CAAC/d,KAAK,CAAC,CACxC,GACD4d,cAAc,CAACvN,IAAI,CAAC,CAAC,CAAC,KACrBuN,cAAc,CAACvN,IAAI,CAAC,CAAC,CAAC,CAAC3P,OAAO,GAAG,CAAC,CAAC8b,UAAU,CAAC;WACpD,MAAM;YACLoB,cAAc,CAACvN,IAAI,CAACuL,OAAO,CACxBoC,QAA0B,IACxBA,QAAQ,CAACtd,OAAO,GAAGsd,QAAQ,CAAChe,KAAK,KAAKwc,UAAW,CACrD;;SAEJ,MAAM,IAAIhM,WAAW,CAACoN,cAAc,CAACvS,GAAG,CAAC,EAAE;UAC1CuS,cAAc,CAACvS,GAAG,CAACrL,KAAK,GAAG,EAAE;SAC9B,MAAM;UACL4d,cAAc,CAACvS,GAAG,CAACrL,KAAK,GAAGwc,UAAU;UAErC,IAAI,CAACoB,cAAc,CAACvS,GAAG,CAACvL,IAAI,EAAE;YAC5BoI,SAAS,CAACc,MAAM,CAAChC,IAAI,CAAC;cACpBpG,IAAI;cACJoI,MAAM,EAAE;gBAAE,GAAGE;cAAW;YACzB,EAAC;;;;;IAMV,CAAC+F,OAAO,CAACwN,WAAW,IAAIxN,OAAO,CAACgP,WAAW,KACzC1B,mBAAmB,CACjB3b,IAAI,EACJ4b,UAAU,EACVvN,OAAO,CAACgP,WAAW,EACnBhP,OAAO,CAACwN,WAAW,EACnB,IAAI,CACL;IAEHxN,OAAO,CAACiP,cAAc,IAAIC,OAAO,CAACvd,IAA0B,CAAC;EAC/D,CAAC;EAED,MAAMwd,SAAS,GAAGA,CAKhBxd,IAAO,EACPZ,KAAQ,EACRiP,OAAU,KACR;IACF,KAAK,MAAMoP,QAAQ,IAAIre,KAAK,EAAE;MAC5B,MAAMwc,UAAU,GAAGxc,KAAK,CAACqe,QAAQ,CAAC;MAClC,MAAMxV,SAAS,MAAAuG,MAAA,CAAMxO,IAAI,OAAAwO,MAAA,CAAIiP,QAAQ,CAAE;MACvC,MAAM1T,KAAK,GAAGlI,GAAG,CAACmI,OAAO,EAAE/B,SAAS,CAAC;MAErC,CAACN,MAAM,CAAC6B,KAAK,CAACnJ,GAAG,CAACL,IAAI,CAAC,IACrB,CAACuX,WAAW,CAACqE,UAAU,CAAC,IACvB7R,KAAK,IAAI,CAACA,KAAK,CAACE,EAAG,KACtB,CAAC9K,YAAY,CAACyc,UAAU,CAAC,GACrB4B,SAAS,CAACvV,SAAS,EAAE2T,UAAU,EAAEvN,OAAO,CAAC,GACzCqN,aAAa,CAACzT,SAAS,EAAE2T,UAAU,EAAEvN,OAAO,CAAC;;EAErD,CAAC;EAED,MAAMqP,QAAQ,GAAkC,SAAAA,CAC9C1d,IAAI,EACJZ,KAAK,EAEH;IAAA,IADFiP,OAAO,GAAA/J,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAG,EAAE;IAEZ,MAAMyF,KAAK,GAAGlI,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC;IAChC,MAAMoR,YAAY,GAAGzJ,MAAM,CAAC6B,KAAK,CAACnJ,GAAG,CAACL,IAAI,CAAC;IAC3C,MAAM2d,UAAU,GAAG3c,WAAW,CAAC5B,KAAK,CAAC;IAErC0J,GAAG,CAACR,WAAW,EAAEtI,IAAI,EAAE2d,UAAU,CAAC;IAElC,IAAIvM,YAAY,EAAE;MAChB9J,SAAS,CAACkC,KAAK,CAACpD,IAAI,CAAC;QACnBpG,IAAI;QACJoI,MAAM,EAAE;UAAE,GAAGE;QAAW;MACzB,EAAC;MAEF,IACE,CAACzD,eAAe,CAAC+B,OAAO,IAAI/B,eAAe,CAACiC,WAAW,KACvDuH,OAAO,CAACwN,WAAW,EACnB;QACAvU,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;UACnBpG,IAAI;UACJ8G,WAAW,EAAE2R,cAAc,CAAChU,cAAc,EAAE6D,WAAW,CAAC;UACxD1B,OAAO,EAAEwU,SAAS,CAACpb,IAAI,EAAE2d,UAAU;QACpC,EAAC;;KAEL,MAAM;MACL5T,KAAK,IAAI,CAACA,KAAK,CAACE,EAAE,IAAI,CAAC3K,iBAAiB,CAACqe,UAAU,CAAC,GAChDH,SAAS,CAACxd,IAAI,EAAE2d,UAAU,EAAEtP,OAAO,CAAC,GACpCqN,aAAa,CAAC1b,IAAI,EAAE2d,UAAU,EAAEtP,OAAO,CAAC;;IAG9CY,SAAS,CAACjP,IAAI,EAAE2H,MAAM,CAAC,IAAIL,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MAAE,GAAGK;IAAU,CAAE,CAAC;IAClEa,SAAS,CAACc,MAAM,CAAChC,IAAI,CAAC;MACpBpG,IAAI,EAAEmK,MAAM,CAACD,KAAK,GAAGlK,IAAI,GAAG4B,SAAS;MACrCwG,MAAM,EAAE;QAAE,GAAGE;MAAW;IACzB,EAAC;EACJ,CAAC;EAED,MAAM3F,QAAQ,GAAkB,MAAO/C,KAAK,IAAI;IAC9C,MAAMC,MAAM,GAAGD,KAAK,CAACC,MAAM;IAC3B,IAAIG,IAAI,GAAGH,MAAM,CAACG,IAAc;IAChC,IAAI4d,mBAAmB,GAAG,IAAI;IAC9B,MAAM7T,KAAK,GAAUlI,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC;IACvC,MAAM6d,oBAAoB,GAAGA,CAAA,KAC3Bhe,MAAM,CAACX,IAAI,GAAG4Z,aAAa,CAAC/O,KAAK,CAACE,EAAE,CAAC,GAAGtK,aAAa,CAACC,KAAK,CAAC;IAC9D,MAAMke,0BAA0B,GAAIlC,UAAe,IAAU;MAC3DgC,mBAAmB,GACjBG,MAAM,CAAC3U,KAAK,CAACwS,UAAU,CAAC,IACxBA,UAAU,KAAK/Z,GAAG,CAACyG,WAAW,EAAEtI,IAAI,EAAE4b,UAAU,CAAC;IACrD,CAAC;IAED,IAAI7R,KAAK,EAAE;MACT,IAAIsB,KAAK;MACT,IAAInE,OAAO;MACX,MAAM0U,UAAU,GAAGiC,oBAAoB,EAAE;MACzC,MAAM3O,WAAW,GACftP,KAAK,CAACV,IAAI,KAAKmD,MAAM,CAACC,IAAI,IAAI1C,KAAK,CAACV,IAAI,KAAKmD,MAAM,CAACE,SAAS;MAC/D,MAAMyb,oBAAoB,GACvB,CAAC1E,aAAa,CAACvP,KAAK,CAACE,EAAE,CAAC,IACvB,CAACJ,QAAQ,CAAC6M,QAAQ,IAClB,CAAC7U,GAAG,CAAC4E,UAAU,CAACU,MAAM,EAAEnH,IAAI,CAAC,IAC7B,CAAC+J,KAAK,CAACE,EAAE,CAACgU,IAAI,IAChBtE,cAAc,CACZzK,WAAW,EACXrN,GAAG,CAAC4E,UAAU,CAACM,aAAa,EAAE/G,IAAI,CAAC,EACnCyG,UAAU,CAACgQ,WAAW,EACtB6D,yBAAyB,EACzBD,0BAA0B,CAC3B;MACH,MAAM6D,OAAO,GAAGjP,SAAS,CAACjP,IAAI,EAAE2H,MAAM,EAAEuH,WAAW,CAAC;MAEpDpG,GAAG,CAACR,WAAW,EAAEtI,IAAI,EAAE4b,UAAU,CAAC;MAElC,IAAI1M,WAAW,EAAE;QACfnF,KAAK,CAACE,EAAE,CAACvH,MAAM,IAAIqH,KAAK,CAACE,EAAE,CAACvH,MAAM,CAAC9C,KAAK,CAAC;QACzCua,kBAAkB,IAAIA,kBAAkB,CAAC,CAAC,CAAC;OAC5C,MAAM,IAAIpQ,KAAK,CAACE,EAAE,CAACtH,QAAQ,EAAE;QAC5BoH,KAAK,CAACE,EAAE,CAACtH,QAAQ,CAAC/C,KAAK,CAAC;;MAG1B,MAAMoL,UAAU,GAAG2Q,mBAAmB,CACpC3b,IAAI,EACJ4b,UAAU,EACV1M,WAAW,EACX,KAAK,CACN;MAED,MAAM4M,YAAY,GAAG,CAAChX,aAAa,CAACkG,UAAU,CAAC,IAAIkT,OAAO;MAE1D,CAAChP,WAAW,IACV5H,SAAS,CAACc,MAAM,CAAChC,IAAI,CAAC;QACpBpG,IAAI;QACJd,IAAI,EAAEU,KAAK,CAACV,IAAI;QAChBkJ,MAAM,EAAE;UAAE,GAAGE;QAAW;MACzB,EAAC;MAEJ,IAAI0V,oBAAoB,EAAE;QACxBnZ,eAAe,CAACqC,OAAO,IAAIM,YAAY,EAAE;QAEzC,OACEsU,YAAY,IACZxU,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;UAAEpG,IAAI;UAAE,IAAIke,OAAO,GAAG,EAAE,GAAGlT,UAAU;QAAC,CAAE,CAAC;;MAIlE,CAACkE,WAAW,IAAIgP,OAAO,IAAI5W,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;QAAE,GAAGK;MAAU,CAAE,CAAC;MAElEsU,mBAAmB,CAAC,IAAI,EAAE,CAAC/a,IAAI,CAAC,CAAC;MAEjC,IAAI6J,QAAQ,CAAC6M,QAAQ,EAAE;QACrB,MAAM;UAAEvP;QAAM,CAAE,GAAG,MAAMwP,cAAc,CAAC,CAAC3W,IAAI,CAAC,CAAC;QAE/C8d,0BAA0B,CAAClC,UAAU,CAAC;QAEtC,IAAIgC,mBAAmB,EAAE;UACvB,MAAMO,yBAAyB,GAAG5E,iBAAiB,CACjD9S,UAAU,CAACU,MAAM,EACjB6C,OAAO,EACPhK,IAAI,CACL;UACD,MAAMoe,iBAAiB,GAAG7E,iBAAiB,CACzCpS,MAAM,EACN6C,OAAO,EACPmU,yBAAyB,CAACne,IAAI,IAAIA,IAAI,CACvC;UAEDqL,KAAK,GAAG+S,iBAAiB,CAAC/S,KAAK;UAC/BrL,IAAI,GAAGoe,iBAAiB,CAACpe,IAAI;UAE7BkH,OAAO,GAAGpC,aAAa,CAACqC,MAAM,CAAC;;OAElC,MAAM;QACLkE,KAAK,GAAG,CACN,MAAM6F,aAAa,CACjBnH,KAAK,EACLzB,WAAW,EACXiS,gCAAgC,EAChC1Q,QAAQ,CAACsH,yBAAyB,CACnC,EACDnR,IAAI,CAAC;QAEP8d,0BAA0B,CAAClC,UAAU,CAAC;QAEtC,IAAIgC,mBAAmB,EAAE;UACvB,IAAIvS,KAAK,EAAE;YACTnE,OAAO,GAAG,KAAK;WAChB,MAAM,IAAIrC,eAAe,CAACqC,OAAO,EAAE;YAClCA,OAAO,GAAG,MAAM4T,wBAAwB,CAAC9Q,OAAO,EAAE,IAAI,CAAC;;;;MAK7D,IAAI4T,mBAAmB,EAAE;QACvB7T,KAAK,CAACE,EAAE,CAACgU,IAAI,IACXV,OAAO,CACLxT,KAAK,CAACE,EAAE,CAACgU,IAEoB,CAC9B;QACH5B,mBAAmB,CAACrc,IAAI,EAAEkH,OAAO,EAAEmE,KAAK,EAAEL,UAAU,CAAC;;;EAG3D,CAAC;EAED,MAAMqT,WAAW,GAAGA,CAAC5T,GAAQ,EAAEnJ,GAAW,KAAI;IAC5C,IAAIO,GAAG,CAAC4E,UAAU,CAACU,MAAM,EAAE7F,GAAG,CAAC,IAAImJ,GAAG,CAACE,KAAK,EAAE;MAC5CF,GAAG,CAACE,KAAK,EAAE;MACX,OAAO,CAAC;;IAEV;EACF,CAAC;EAED,MAAM4S,OAAO,GAAiC,eAAAA,CAAOvd,IAAI,EAAkB;IAAA,IAAhBqO,OAAO,GAAA/J,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAG,EAAE;IACrE,IAAI4C,OAAO;IACX,IAAIkM,gBAAgB;IACpB,MAAMkL,UAAU,GAAGlZ,qBAAqB,CAACpF,IAAI,CAAwB;IAErE+a,mBAAmB,CAAC,IAAI,EAAEuD,UAAU,CAAC;IAErC,IAAIzU,QAAQ,CAAC6M,QAAQ,EAAE;MACrB,MAAMvP,MAAM,GAAG,MAAMuV,2BAA2B,CAC9Chb,WAAW,CAAC1B,IAAI,CAAC,GAAGA,IAAI,GAAGse,UAAU,CACtC;MAEDpX,OAAO,GAAGpC,aAAa,CAACqC,MAAM,CAAC;MAC/BiM,gBAAgB,GAAGpT,IAAI,GACnB,CAACse,UAAU,CAAC9Y,IAAI,CAAExF,IAAI,IAAK6B,GAAG,CAACsF,MAAM,EAAEnH,IAAI,CAAC,CAAC,GAC7CkH,OAAO;KACZ,MAAM,IAAIlH,IAAI,EAAE;MACfoT,gBAAgB,GAAG,CACjB,MAAMmL,OAAO,CAACzb,GAAG,CACfwb,UAAU,CAACtW,GAAG,CAAC,MAAOC,SAAS,IAAI;QACjC,MAAM8B,KAAK,GAAGlI,GAAG,CAACmI,OAAO,EAAE/B,SAAS,CAAC;QACrC,OAAO,MAAM6S,wBAAwB,CACnC/Q,KAAK,IAAIA,KAAK,CAACE,EAAE,GAAG;UAAE,CAAChC,SAAS,GAAG8B;QAAK,CAAE,GAAGA,KAAK,CACnD;OACF,CAAC,CACH,EACDgH,KAAK,CAACtP,OAAO,CAAC;MAChB,EAAE,CAAC2R,gBAAgB,IAAI,CAAC3M,UAAU,CAACS,OAAO,CAAC,IAAIM,YAAY,EAAE;KAC9D,MAAM;MACL4L,gBAAgB,GAAGlM,OAAO,GAAG,MAAM4T,wBAAwB,CAAC9Q,OAAO,CAAC;;IAGtE1C,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MACnB,IAAI,CAACqB,QAAQ,CAACzH,IAAI,CAAC,IAClB6E,eAAe,CAACqC,OAAO,IAAIA,OAAO,KAAKT,UAAU,CAACS,OAAQ,GACvD,EAAE,GACF;QAAElH;MAAI,CAAE,CAAC;MACb,IAAI6J,QAAQ,CAAC6M,QAAQ,IAAI,CAAC1W,IAAI,GAAG;QAAEkH;MAAO,CAAE,GAAG,EAAE,CAAC;MAClDC,MAAM,EAAEV,UAAU,CAACU,MAAM;MACzBF,YAAY,EAAE;IACf,EAAC;IAEFoH,OAAO,CAACC,WAAW,IACjB,CAAC8E,gBAAgB,IACjB/D,qBAAqB,CACnBrF,OAAO,EACPqU,WAAW,EACXre,IAAI,GAAGse,UAAU,GAAG3W,MAAM,CAACuC,KAAK,CACjC;IAEH,OAAOkJ,gBAAgB;EACzB,CAAC;EAED,MAAM2J,SAAS,GACbuB,UAE0C,IACxC;IACF,MAAMlW,MAAM,GAAG;MACb,GAAG3D,cAAc;MACjB,IAAI0F,MAAM,CAACD,KAAK,GAAG5B,WAAW,GAAG,EAAE;KACpC;IAED,OAAO5G,WAAW,CAAC4c,UAAU,CAAC,GAC1BlW,MAAM,GACNX,QAAQ,CAAC6W,UAAU,CAAC,GACpBzc,GAAG,CAACuG,MAAM,EAAEkW,UAAU,CAAC,GACvBA,UAAU,CAACtW,GAAG,CAAEhI,IAAI,IAAK6B,GAAG,CAACuG,MAAM,EAAEpI,IAAI,CAAC,CAAC;EACjD,CAAC;EAED,MAAMwe,aAAa,GAAuCA,CACxDxe,IAAI,EACJkE,SAAS,MACL;IACJgH,OAAO,EAAE,CAAC,CAACrJ,GAAG,CAAC,CAACqC,SAAS,IAAIuC,UAAU,EAAEU,MAAM,EAAEnH,IAAI,CAAC;IACtD4G,OAAO,EAAE,CAAC,CAAC/E,GAAG,CAAC,CAACqC,SAAS,IAAIuC,UAAU,EAAEK,WAAW,EAAE9G,IAAI,CAAC;IAC3DoL,SAAS,EAAE,CAAC,CAACvJ,GAAG,CAAC,CAACqC,SAAS,IAAIuC,UAAU,EAAEM,aAAa,EAAE/G,IAAI,CAAC;IAC/DiH,YAAY,EAAE,CAAC,CAACpF,GAAG,CAAC,CAACqC,SAAS,IAAIuC,UAAU,EAAEO,gBAAgB,EAAEhH,IAAI,CAAC;IACrEqL,KAAK,EAAExJ,GAAG,CAAC,CAACqC,SAAS,IAAIuC,UAAU,EAAEU,MAAM,EAAEnH,IAAI;EAClD,EAAC;EAEF,MAAMye,WAAW,GAAsCze,IAAI,IAAI;IAC7DA,IAAI,IACFoF,qBAAqB,CAACpF,IAAI,CAAC,CAACgb,OAAO,CAAE0D,SAAS,IAC5C9J,KAAK,CAACnO,UAAU,CAACU,MAAM,EAAEuX,SAAS,CAAC,CACpC;IAEHpX,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MACnBe,MAAM,EAAEnH,IAAI,GAAGyG,UAAU,CAACU,MAAM,GAAG;IACpC,EAAC;EACJ,CAAC;EAED,MAAMkG,QAAQ,GAAkCA,CAACrN,IAAI,EAAEqL,KAAK,EAAEgD,OAAO,KAAI;IACvE,MAAM5D,GAAG,GAAG,CAAC5I,GAAG,CAACmI,OAAO,EAAEhK,IAAI,EAAE;MAAEiK,EAAE,EAAE;IAAE,CAAE,CAAC,CAACA,EAAE,IAAI,EAAE,EAAEQ,GAAG;IAEzD3B,GAAG,CAACrC,UAAU,CAACU,MAAM,EAAEnH,IAAI,EAAE;MAC3B,GAAGqL,KAAK;MACRZ;IACD,EAAC;IAEFnD,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MACnBpG,IAAI;MACJmH,MAAM,EAAEV,UAAU,CAACU,MAAM;MACzBD,OAAO,EAAE;IACV,EAAC;IAEFmH,OAAO,IAAIA,OAAO,CAACC,WAAW,IAAI7D,GAAG,IAAIA,GAAG,CAACE,KAAK,IAAIF,GAAG,CAACE,KAAK,EAAE;EACnE,CAAC;EAED,MAAM7C,KAAK,GAA+BA,CACxC9H,IAG+B,EAC/BgC,YAAwC,KAExC6N,UAAU,CAAC7P,IAAI,CAAC,GACZsH,SAAS,CAACc,MAAM,CAACjC,SAAS,CAAC;IACzBC,IAAI,EAAGuY,OAAO,IACZ3e,IAAI,CACFuI,SAAS,CAAC3G,SAAS,EAAEI,YAAY,CAAC,EAClC2c,OAIC;GAEN,CAAC,GACFpW,SAAS,CACPvI,IAA+C,EAC/CgC,YAAY,EACZ,IAAI,CACL;EAEP,MAAMqI,UAAU,GAAoC,SAAAA,CAACrK,IAAI,EAAkB;IAAA,IAAhBqO,OAAO,GAAA/J,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAG,EAAE;IACrE,KAAK,MAAM2D,SAAS,IAAIjI,IAAI,GAAGoF,qBAAqB,CAACpF,IAAI,CAAC,GAAG2H,MAAM,CAACuC,KAAK,EAAE;MACzEvC,MAAM,CAACuC,KAAK,CAAC0U,MAAM,CAAC3W,SAAS,CAAC;MAC9BN,MAAM,CAAC6B,KAAK,CAACoV,MAAM,CAAC3W,SAAS,CAAC;MAE9B,IAAI,CAACoG,OAAO,CAACwQ,SAAS,EAAE;QACtBjK,KAAK,CAAC5K,OAAO,EAAE/B,SAAS,CAAC;QACzB2M,KAAK,CAACtM,WAAW,EAAEL,SAAS,CAAC;;MAG/B,CAACoG,OAAO,CAACyQ,SAAS,IAAIlK,KAAK,CAACnO,UAAU,CAACU,MAAM,EAAEc,SAAS,CAAC;MACzD,CAACoG,OAAO,CAAC0Q,SAAS,IAAInK,KAAK,CAACnO,UAAU,CAACK,WAAW,EAAEmB,SAAS,CAAC;MAC9D,CAACoG,OAAO,CAAC2Q,WAAW,IAAIpK,KAAK,CAACnO,UAAU,CAACM,aAAa,EAAEkB,SAAS,CAAC;MAClE,CAACoG,OAAO,CAAC4Q,gBAAgB,IACvBrK,KAAK,CAACnO,UAAU,CAACO,gBAAgB,EAAEiB,SAAS,CAAC;MAC/C,CAAC4B,QAAQ,CAACP,gBAAgB,IACxB,CAAC+E,OAAO,CAAC6Q,gBAAgB,IACzBtK,KAAK,CAACnQ,cAAc,EAAEwD,SAAS,CAAC;;IAGpCX,SAAS,CAACc,MAAM,CAAChC,IAAI,CAAC;MACpBgC,MAAM,EAAE;QAAE,GAAGE;MAAW;IACzB,EAAC;IAEFhB,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MACnB,GAAGK,UAAU;MACb,IAAI,CAAC4H,OAAO,CAAC0Q,SAAS,GAAG,EAAE,GAAG;QAAEnY,OAAO,EAAEwU,SAAS;MAAE,CAAE;IACvD,EAAC;IAEF,CAAC/M,OAAO,CAAC8Q,WAAW,IAAI3X,YAAY,EAAE;EACxC,CAAC;EAED,MAAM8C,oBAAoB,GAAkD8U,KAAA,IAMvE;IAAA,IANwE;MAC3EnZ,QAAQ;MACRjG,IAAI;MACJ+J,KAAK;MACLQ,MAAM;MACNnL;IAAK,CACN,GAAAggB,KAAA;IACC,IAAIhd,SAAS,CAAC6D,QAAQ,CAAC,EAAE;MACvB,MAAMqL,UAAU,GAAGrL,QAAQ,GACvBrE,SAAS,GACTF,WAAW,CAACtC,KAAK,CAAC,GAClB0Z,aAAa,CAAC/O,KAAK,GAAGA,KAAK,CAACE,EAAE,GAAGpI,GAAG,CAAC0I,MAAM,EAAEvK,IAAI,CAAC,CAACiK,EAAE,CAAC,GACtD7K,KAAK;MACT0J,GAAG,CAACR,WAAW,EAAEtI,IAAI,EAAEsR,UAAU,CAAC;MAClCqK,mBAAmB,CAAC3b,IAAI,EAAEsR,UAAU,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,CAAC;;EAE7D,CAAC;EAED,MAAM5H,QAAQ,GAAkC,SAAAA,CAAC1J,IAAI,EAAkB;IAAA,IAAhBqO,OAAO,GAAA/J,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAG,EAAE;IACjE,IAAIyF,KAAK,GAAGlI,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC;IAC9B,MAAMqf,iBAAiB,GAAGjd,SAAS,CAACiM,OAAO,CAACpI,QAAQ,CAAC;IAErD6C,GAAG,CAACkB,OAAO,EAAEhK,IAAI,EAAE;MACjB,IAAI+J,KAAK,IAAI,EAAE,CAAC;MAChBE,EAAE,EAAE;QACF,IAAIF,KAAK,IAAIA,KAAK,CAACE,EAAE,GAAGF,KAAK,CAACE,EAAE,GAAG;UAAEQ,GAAG,EAAE;YAAEzK;UAAI;QAAE,CAAE,CAAC;QACrDA,IAAI;QACJkK,KAAK,EAAE,IAAI;QACX,GAAGmE;MACJ;IACF,EAAC;IACF1G,MAAM,CAACuC,KAAK,CAACnC,GAAG,CAAC/H,IAAI,CAAC;IAEtB,IAAI+J,KAAK,EAAE;MACTO,oBAAoB,CAAC;QACnBP,KAAK;QACL9D,QAAQ,EAAEoI,OAAO,CAACpI,QAAQ;QAC1BjG,IAAI;QACJZ,KAAK,EAAEiP,OAAO,CAACjP;MAChB,EAAC;KACH,MAAM;MACLmc,mBAAmB,CAACvb,IAAI,EAAE,IAAI,EAAEqO,OAAO,CAACjP,KAAK,CAAC;;IAGhD,OAAO;MACL,IAAIigB,iBAAiB,GAAG;QAAEpZ,QAAQ,EAAEoI,OAAO,CAACpI;MAAQ,CAAE,GAAG,EAAE,CAAC;MAC5D,IAAI4D,QAAQ,CAACyV,WAAW,GACpB;QACEjc,QAAQ,EAAE,CAAC,CAACgL,OAAO,CAAChL,QAAQ;QAC5BJ,GAAG,EAAEkW,YAAY,CAAC9K,OAAO,CAACpL,GAAG,CAAC;QAC9BD,GAAG,EAAEmW,YAAY,CAAC9K,OAAO,CAACrL,GAAG,CAAC;QAC9BG,SAAS,EAAEgW,YAAY,CAAS9K,OAAO,CAAClL,SAAS,CAAW;QAC5DD,SAAS,EAAEiW,YAAY,CAAC9K,OAAO,CAACnL,SAAS,CAAW;QACpDE,OAAO,EAAE+V,YAAY,CAAC9K,OAAO,CAACjL,OAAO;MACtC,IACD,EAAE,CAAC;MACPpD,IAAI;MACJ2C,QAAQ;MACRD,MAAM,EAAEC,QAAQ;MAChB8H,GAAG,EAAGA,GAA4B,IAAU;QAC1C,IAAIA,GAAG,EAAE;UACPf,QAAQ,CAAC1J,IAAI,EAAEqO,OAAO,CAAC;UACvBtE,KAAK,GAAGlI,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC;UAE1B,MAAMuf,QAAQ,GAAG7d,WAAW,CAAC+I,GAAG,CAACrL,KAAK,CAAC,GACnCqL,GAAG,CAAC+U,gBAAgB,GACjB/U,GAAG,CAAC+U,gBAAgB,CAAC,uBAAuB,CAAC,CAAC,CAAC,CAAS,IAAI/U,GAAG,GAChEA,GAAG,GACLA,GAAG;UACP,MAAMgV,eAAe,GAAG/N,iBAAiB,CAAC6N,QAAQ,CAAC;UACnD,MAAM9P,IAAI,GAAG1F,KAAK,CAACE,EAAE,CAACwF,IAAI,IAAI,EAAE;UAEhC,IACEgQ,eAAe,GACXhQ,IAAI,CAACtK,IAAI,CAAEsL,MAAW,IAAKA,MAAM,KAAK8O,QAAQ,CAAC,GAC/CA,QAAQ,KAAKxV,KAAK,CAACE,EAAE,CAACQ,GAAG,EAC7B;YACA;;UAGF3B,GAAG,CAACkB,OAAO,EAAEhK,IAAI,EAAE;YACjBiK,EAAE,EAAE;cACF,GAAGF,KAAK,CAACE,EAAE;cACX,IAAIwV,eAAe,GACf;gBACEhQ,IAAI,EAAE,CACJ,GAAGA,IAAI,CAACjO,MAAM,CAACyW,IAAI,CAAC,EACpBsH,QAAQ,EACR,IAAI9f,KAAK,CAACC,OAAO,CAACmC,GAAG,CAAC4C,cAAc,EAAEzE,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAC1D;gBACDyK,GAAG,EAAE;kBAAEvL,IAAI,EAAEqgB,QAAQ,CAACrgB,IAAI;kBAAEc;gBAAI;cACjC,IACD;gBAAEyK,GAAG,EAAE8U;cAAQ,CAAE;YACtB;UACF,EAAC;UAEFhE,mBAAmB,CAACvb,IAAI,EAAE,KAAK,EAAE4B,SAAS,EAAE2d,QAAQ,CAAC;SACtD,MAAM;UACLxV,KAAK,GAAGlI,GAAG,CAACmI,OAAO,EAAEhK,IAAI,EAAE,EAAE,CAAC;UAE9B,IAAI+J,KAAK,CAACE,EAAE,EAAE;YACZF,KAAK,CAACE,EAAE,CAACC,KAAK,GAAG,KAAK;;UAGxB,CAACL,QAAQ,CAACP,gBAAgB,IAAI+E,OAAO,CAAC/E,gBAAgB,KACpD,EAAEnJ,kBAAkB,CAACwH,MAAM,CAAC6B,KAAK,EAAExJ,IAAI,CAAC,IAAImK,MAAM,CAACC,MAAM,CAAC,IAC1DzC,MAAM,CAACuS,OAAO,CAACnS,GAAG,CAAC/H,IAAI,CAAC;;;KAG/B;EACH,CAAC;EAED,MAAM0f,WAAW,GAAGA,CAAA,KAClB7V,QAAQ,CAACiQ,gBAAgB,IACzBzK,qBAAqB,CAACrF,OAAO,EAAEqU,WAAW,EAAE1W,MAAM,CAACuC,KAAK,CAAC;EAE3D,MAAMyV,YAAY,GAAI1Z,QAAkB,IAAI;IAC1C,IAAI7D,SAAS,CAAC6D,QAAQ,CAAC,EAAE;MACvBqB,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;QAAEH;MAAQ,CAAE,CAAC;MAClCoJ,qBAAqB,CACnBrF,OAAO,EACP,CAACS,GAAG,EAAEzK,IAAI,KAAI;QACZ,IAAI4f,qBAAqB,GAAG3Z,QAAQ;QACpC,MAAMuJ,YAAY,GAAG3N,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC;QACvC,IAAIwP,YAAY,IAAIpN,SAAS,CAACoN,YAAY,CAACvF,EAAE,CAAChE,QAAQ,CAAC,EAAE;UACvD2Z,qBAAqB,KAArBA,qBAAqB,GAAKpQ,YAAY,CAACvF,EAAE,CAAChE,QAAQ,CAAC;;QAGrDwE,GAAG,CAACxE,QAAQ,GAAG2Z,qBAAqB;MACtC,CAAC,EACD,CAAC,EACD,KAAK,CACN;;EAEL,CAAC;EAED,MAAMvT,YAAY,GAChBA,CAACwT,OAAO,EAAEC,SAAS,KAAK,MAAOC,CAAC,IAAI;IAClC,IAAIC,YAAY,GAAGpe,SAAS;IAC5B,IAAIme,CAAC,EAAE;MACLA,CAAC,CAACE,cAAc,IAAIF,CAAC,CAACE,cAAc,EAAE;MACtCF,CAAC,CAACG,OAAO,IAAIH,CAAC,CAACG,OAAO,EAAE;;IAE1B,IAAIlL,WAAW,GAAGhU,WAAW,CAACsH,WAAW,CAAC;IAE1ChB,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MACnB6T,YAAY,EAAE;IACf,EAAC;IAEF,IAAIpQ,QAAQ,CAAC6M,QAAQ,EAAE;MACrB,MAAM;QAAEvP,MAAM;QAAEiB;MAAM,CAAE,GAAG,MAAMuO,cAAc,EAAE;MACjDlQ,UAAU,CAACU,MAAM,GAAGA,MAAM;MAC1B6N,WAAW,GAAG5M,MAAM;KACrB,MAAM;MACL,MAAM0S,wBAAwB,CAAC9Q,OAAO,CAAC;;IAGzC4K,KAAK,CAACnO,UAAU,CAACU,MAAM,EAAE,MAAM,CAAC;IAEhC,IAAIrC,aAAa,CAAC2B,UAAU,CAACU,MAAM,CAAC,EAAE;MACpCG,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;QACnBe,MAAM,EAAE;MACT,EAAC;MACF,IAAI;QACF,MAAM0Y,OAAO,CAAC7K,WAA2B,EAAE+K,CAAC,CAAC;OAC9C,CAAC,OAAO1U,KAAK,EAAE;QACd2U,YAAY,GAAG3U,KAAK;;KAEvB,MAAM;MACL,IAAIyU,SAAS,EAAE;QACb,MAAMA,SAAS,CAAC;UAAE,GAAGrZ,UAAU,CAACU;QAAM,CAAE,EAAE4Y,CAAC,CAAC;;MAE9CL,WAAW,EAAE;MACb9E,UAAU,CAAC8E,WAAW,CAAC;;IAGzBpY,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MACnBqQ,WAAW,EAAE,IAAI;MACjBwD,YAAY,EAAE,KAAK;MACnB7M,kBAAkB,EAAEtI,aAAa,CAAC2B,UAAU,CAACU,MAAM,CAAC,IAAI,CAAC6Y,YAAY;MACrEhG,WAAW,EAAEvT,UAAU,CAACuT,WAAW,GAAG,CAAC;MACvC7S,MAAM,EAAEV,UAAU,CAACU;IACpB,EAAC;IACF,IAAI6Y,YAAY,EAAE;MAChB,MAAMA,YAAY;;EAEtB,CAAC;EAEH,MAAMG,UAAU,GAAoC,SAAAA,CAACngB,IAAI,EAAkB;IAAA,IAAhBqO,OAAO,GAAA/J,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAG,EAAE;IACrE,IAAIzC,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC,EAAE;MACtB,IAAI0B,WAAW,CAAC2M,OAAO,CAACrM,YAAY,CAAC,EAAE;QACrC0b,QAAQ,CAAC1d,IAAI,EAAEgB,WAAW,CAACa,GAAG,CAAC4C,cAAc,EAAEzE,IAAI,CAAC,CAAC,CAAC;OACvD,MAAM;QACL0d,QAAQ,CACN1d,IAAI,EACJqO,OAAO,CAACrM,YAGP,CACF;QACD8G,GAAG,CAACrE,cAAc,EAAEzE,IAAI,EAAEgB,WAAW,CAACqN,OAAO,CAACrM,YAAY,CAAC,CAAC;;MAG9D,IAAI,CAACqM,OAAO,CAAC2Q,WAAW,EAAE;QACxBpK,KAAK,CAACnO,UAAU,CAACM,aAAa,EAAE/G,IAAI,CAAC;;MAGvC,IAAI,CAACqO,OAAO,CAAC0Q,SAAS,EAAE;QACtBnK,KAAK,CAACnO,UAAU,CAACK,WAAW,EAAE9G,IAAI,CAAC;QACnCyG,UAAU,CAACG,OAAO,GAAGyH,OAAO,CAACrM,YAAY,GACrCoZ,SAAS,CAACpb,IAAI,EAAEgB,WAAW,CAACa,GAAG,CAAC4C,cAAc,EAAEzE,IAAI,CAAC,CAAC,CAAC,GACvDob,SAAS,EAAE;;MAGjB,IAAI,CAAC/M,OAAO,CAACyQ,SAAS,EAAE;QACtBlK,KAAK,CAACnO,UAAU,CAACU,MAAM,EAAEnH,IAAI,CAAC;QAC9B6E,eAAe,CAACqC,OAAO,IAAIM,YAAY,EAAE;;MAG3CF,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;QAAE,GAAGK;MAAU,CAAE,CAAC;;EAE3C,CAAC;EAED,MAAM2Z,MAAM,GAA+B,SAAAA,CACzCxY,UAAU,EAER;IAAA,IADFyY,gBAAgB,GAAA/b,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAG,EAAE;IAErB,MAAMgc,aAAa,GAAG1Y,UAAU,GAAG5G,WAAW,CAAC4G,UAAU,CAAC,GAAGnD,cAAc;IAC3E,MAAM8b,kBAAkB,GAAGvf,WAAW,CAACsf,aAAa,CAAC;IACrD,MAAME,kBAAkB,GAAG1b,aAAa,CAAC8C,UAAU,CAAC;IACpD,MAAMQ,MAAM,GAAGoY,kBAAkB,GAAG/b,cAAc,GAAG8b,kBAAkB;IAEvE,IAAI,CAACF,gBAAgB,CAACI,iBAAiB,EAAE;MACvChc,cAAc,GAAG6b,aAAa;;IAGhC,IAAI,CAACD,gBAAgB,CAACK,UAAU,EAAE;MAChC,IAAIL,gBAAgB,CAACM,eAAe,EAAE;QACpC,KAAK,MAAM1Y,SAAS,IAAIN,MAAM,CAACuC,KAAK,EAAE;UACpCrI,GAAG,CAAC4E,UAAU,CAACK,WAAW,EAAEmB,SAAS,CAAC,GAClCa,GAAG,CAACV,MAAM,EAAEH,SAAS,EAAEpG,GAAG,CAACyG,WAAW,EAAEL,SAAS,CAAC,CAAC,GACnDyV,QAAQ,CACNzV,SAAoC,EACpCpG,GAAG,CAACuG,MAAM,EAAEH,SAAS,CAAC,CACvB;;OAER,MAAM;QACL,IAAIrH,KAAK,IAAIc,WAAW,CAACkG,UAAU,CAAC,EAAE;UACpC,KAAK,MAAM5H,IAAI,IAAI2H,MAAM,CAACuC,KAAK,EAAE;YAC/B,MAAMH,KAAK,GAAGlI,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC;YAChC,IAAI+J,KAAK,IAAIA,KAAK,CAACE,EAAE,EAAE;cACrB,MAAM+S,cAAc,GAAGvd,KAAK,CAACC,OAAO,CAACqK,KAAK,CAACE,EAAE,CAACwF,IAAI,CAAC,GAC/C1F,KAAK,CAACE,EAAE,CAACwF,IAAI,CAAC,CAAC,CAAC,GAChB1F,KAAK,CAACE,EAAE,CAACQ,GAAG;cAEhB,IAAIqF,aAAa,CAACkN,cAAc,CAAC,EAAE;gBACjC,MAAM4D,IAAI,GAAG5D,cAAc,CAAC6D,OAAO,CAAC,MAAM,CAAC;gBAC3C,IAAID,IAAI,EAAE;kBACRA,IAAI,CAACE,KAAK,EAAE;kBACZ;;;;;;QAOV9W,OAAO,GAAG,EAAE;;MAGd1B,WAAW,GAAGzE,KAAK,CAACyF,gBAAgB,GAChC+W,gBAAgB,CAACI,iBAAiB,GAChCzf,WAAW,CAACyD,cAAc,CAAC,GAC3B,EAAE,GACJzD,WAAW,CAACoH,MAAM,CAAC;MAEvBd,SAAS,CAACkC,KAAK,CAACpD,IAAI,CAAC;QACnBgC,MAAM,EAAE;UAAE,GAAGA;QAAM;MACpB,EAAC;MAEFd,SAAS,CAACc,MAAM,CAAChC,IAAI,CAAC;QACpBgC,MAAM,EAAE;UAAE,GAAGA;QAAM;MACpB,EAAC;;IAGJT,MAAM,GAAG;MACPuC,KAAK,EAAEmW,gBAAgB,CAACM,eAAe,GAAGhZ,MAAM,CAACuC,KAAK,GAAG,IAAI/I,GAAG,EAAE;MAClE+Y,OAAO,EAAE,IAAI/Y,GAAG,EAAE;MAClBqI,KAAK,EAAE,IAAIrI,GAAG,EAAE;MAChB2G,KAAK,EAAE,IAAI3G,GAAG,EAAE;MAChB+G,QAAQ,EAAE,KAAK;MACfyC,KAAK,EAAE;KACR;IAEDR,MAAM,CAACD,KAAK,GACV,CAACrF,eAAe,CAACqC,OAAO,IACxB,CAAC,CAACmZ,gBAAgB,CAAClB,WAAW,IAC9B,CAAC,CAACkB,gBAAgB,CAACM,eAAe;IAEpCxW,MAAM,CAACrC,KAAK,GAAG,CAAC,CAACjE,KAAK,CAACyF,gBAAgB;IAEvChC,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MACnB4T,WAAW,EAAEqG,gBAAgB,CAACU,eAAe,GACzCta,UAAU,CAACuT,WAAW,GACtB,CAAC;MACLpT,OAAO,EAAE4Z,kBAAkB,GACvB,KAAK,GACLH,gBAAgB,CAACtB,SAAS,GAC1BtY,UAAU,CAACG,OAAO,GAClB,CAAC,EACCyZ,gBAAgB,CAACI,iBAAiB,IAClC,CAACjJ,SAAS,CAAC5P,UAAU,EAAEnD,cAAc,CAAC,CACvC;MACLgS,WAAW,EAAE4J,gBAAgB,CAACW,eAAe,GACzCva,UAAU,CAACgQ,WAAW,GACtB,KAAK;MACT3P,WAAW,EAAE0Z,kBAAkB,GAC3B,EAAE,GACFH,gBAAgB,CAACM,eAAe,GAChCN,gBAAgB,CAACI,iBAAiB,IAAInY,WAAW,GAC/CmQ,cAAc,CAAChU,cAAc,EAAE6D,WAAW,CAAC,GAC3C7B,UAAU,CAACK,WAAW,GACxBuZ,gBAAgB,CAACI,iBAAiB,IAAI7Y,UAAU,GAChD6Q,cAAc,CAAChU,cAAc,EAAEmD,UAAU,CAAC,GAC1C,EAAE;MACNb,aAAa,EAAEsZ,gBAAgB,CAACrB,WAAW,GACvCvY,UAAU,CAACM,aAAa,GACxB,EAAE;MACNI,MAAM,EAAEkZ,gBAAgB,CAACY,UAAU,GAAGxa,UAAU,CAACU,MAAM,GAAG,EAAE;MAC5DiG,kBAAkB,EAAEiT,gBAAgB,CAACa,sBAAsB,GACvDza,UAAU,CAAC2G,kBAAkB,GAC7B,KAAK;MACT6M,YAAY,EAAE;IACf,EAAC;EACJ,CAAC;EAED,MAAM6G,KAAK,GAA+BA,CAAClZ,UAAU,EAAEyY,gBAAgB,KACrED,MAAM,CACJvQ,UAAU,CAACjI,UAAU,CAAC,GAClBA,UAAU,CAACU,WAA2B,CAAC,GACvCV,UAAU,EACdyY,gBAAgB,CACjB;EAEH,MAAMc,QAAQ,GAAkC,SAAAA,CAACnhB,IAAI,EAAkB;IAAA,IAAhBqO,OAAO,GAAA/J,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAAG,EAAE;IACjE,MAAMyF,KAAK,GAAGlI,GAAG,CAACmI,OAAO,EAAEhK,IAAI,CAAC;IAChC,MAAMgd,cAAc,GAAGjT,KAAK,IAAIA,KAAK,CAACE,EAAE;IAExC,IAAI+S,cAAc,EAAE;MAClB,MAAMuC,QAAQ,GAAGvC,cAAc,CAACvN,IAAI,GAChCuN,cAAc,CAACvN,IAAI,CAAC,CAAC,CAAC,GACtBuN,cAAc,CAACvS,GAAG;MAEtB,IAAI8U,QAAQ,CAAC5U,KAAK,EAAE;QAClB4U,QAAQ,CAAC5U,KAAK,EAAE;QAChB0D,OAAO,CAAC+S,YAAY,IAAI7B,QAAQ,CAAC3U,MAAM,EAAE;;;EAG/C,CAAC;EAED,MAAMvD,gBAAgB,GACpBmV,gBAAkD,IAChD;IACF/V,UAAU,GAAG;MACX,GAAGA,UAAU;MACb,GAAG+V;KACJ;EACH,CAAC;EAED,MAAM6E,mBAAmB,GAAGA,CAAA,KAC1BxR,UAAU,CAAChG,QAAQ,CAACrF,aAAa,CAAC,IAClCqF,QAAQ,CAACrF,aAAa,EAAE,CAACoS,IAAI,CAAExO,MAAM,IAAI;IACvC0Y,KAAK,CAAC1Y,MAAM,EAAEyB,QAAQ,CAACyX,YAAY,CAAC;IACpCha,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;MACnBS,SAAS,EAAE;IACZ,EAAC;EACJ,CAAC,CAAC;EAEJ,OAAO;IACL1C,OAAO,EAAE;MACPuF,QAAQ;MACRW,UAAU;MACVmU,aAAa;MACbnS,YAAY;MACZgB,QAAQ;MACRsJ,cAAc;MACdpO,SAAS;MACT6S,SAAS;MACT5T,YAAY;MACZgB,gBAAgB;MAChBoN,iBAAiB;MACjBtL,oBAAoB;MACpB8K,cAAc;MACdgL,MAAM;MACNiB,mBAAmB;MACnBha,gBAAgB;MAChBsY,YAAY;MACZrY,SAAS;MACTzC,eAAe;MACfyW,UAAU;MACV,IAAItR,OAAOA,CAAA;QACT,OAAOA,OAAO;OACf;MACD,IAAI1B,WAAWA,CAAA;QACb,OAAOA,WAAW;OACnB;MACD,IAAI6B,MAAMA,CAAA;QACR,OAAOA,MAAM;OACd;MACD,IAAIA,MAAMA,CAAC/K,KAAK;QACd+K,MAAM,GAAG/K,KAAK;OACf;MACD,IAAIqF,cAAcA,CAAA;QAChB,OAAOA,cAAc;OACtB;MACD,IAAIkD,MAAMA,CAAA;QACR,OAAOA,MAAM;OACd;MACD,IAAIA,MAAMA,CAACvI,KAAK;QACduI,MAAM,GAAGvI,KAAK;OACf;MACD,IAAIqH,UAAUA,CAAA;QACZ,OAAOA,UAAU;OAClB;MACD,IAAIA,UAAUA,CAACrH,KAAK;QAClBqH,UAAU,GAAGrH,KAAK;OACnB;MACD,IAAIyK,QAAQA,CAAA;QACV,OAAOA,QAAQ;OAChB;MACD,IAAIA,QAAQA,CAACzK,KAAK;QAChByK,QAAQ,GAAG;UACT,GAAGA,QAAQ;UACX,GAAGzK;SACJ;;IAEJ;IACDme,OAAO;IACP7T,QAAQ;IACR2C,YAAY;IACZvE,KAAK;IACL4V,QAAQ;IACRX,SAAS;IACT+D,KAAK;IACLX,UAAU;IACV1B,WAAW;IACXpU,UAAU;IACVgD,QAAQ;IACR8T,QAAQ;IACR3C;GACD;AACH;;ACp4CA;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BG;AACa,SAAA+C,OAAOA,CAAA,EAK2B;EAAA,IAAhD1d,KAAA,GAAAS,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAA1C,SAAA,GAAA0C,SAAA,MAA8C,EAAE;EAEhD,MAAMkd,YAAY,GAAGhe,KAAK,CAACqC,MAAM,EAE9B;EACH,MAAM4b,OAAO,GAAGje,KAAK,CAACqC,MAAM,EAAuB;EACnD,MAAM,CAAC3B,SAAS,EAAEgB,eAAe,CAAC,GAAG1B,KAAK,CAACgD,QAAQ,CAA0B;IAC3EI,OAAO,EAAE,KAAK;IACdK,YAAY,EAAE,KAAK;IACnBJ,SAAS,EAAEgJ,UAAU,CAAChM,KAAK,CAACW,aAAa,CAAC;IAC1CiS,WAAW,EAAE,KAAK;IAClBwD,YAAY,EAAE,KAAK;IACnB7M,kBAAkB,EAAE,KAAK;IACzBlG,OAAO,EAAE,KAAK;IACd8S,WAAW,EAAE,CAAC;IACdlT,WAAW,EAAE,EAAE;IACfC,aAAa,EAAE,EAAE;IACjBC,gBAAgB,EAAE,EAAE;IACpBG,MAAM,EAAEtD,KAAK,CAACsD,MAAM,IAAI,EAAE;IAC1BlB,QAAQ,EAAEpC,KAAK,CAACoC,QAAQ,IAAI,KAAK;IACjCzB,aAAa,EAAEqL,UAAU,CAAChM,KAAK,CAACW,aAAa,CAAC,GAC1C5C,SAAS,GACTiC,KAAK,CAACW;EACX,EAAC;EAEF,IAAI,CAACgd,YAAY,CAAC1b,OAAO,EAAE;IACzB0b,YAAY,CAAC1b,OAAO,GAAG;MACrB,GAAGiU,iBAAiB,CAAClW,KAAK,CAAC;MAC3BK;KACD;;EAGH,MAAMC,OAAO,GAAGqd,YAAY,CAAC1b,OAAO,CAAC3B,OAAO;EAC5CA,OAAO,CAAC0F,QAAQ,GAAGhG,KAAK;EAExB8B,YAAY,CAAC;IACXO,OAAO,EAAE/B,OAAO,CAACmD,SAAS,CAACC,KAAK;IAChCnB,IAAI,EACFhH,KAAsE,IACpE;MACF,IACE4F,qBAAqB,CACnB5F,KAAK,EACL+E,OAAO,CAACU,eAAe,EACvBV,OAAO,CAACkD,gBAAgB,EACxB,IAAI,CACL,EACD;QACAnC,eAAe,CAAC;UAAE,GAAGf,OAAO,CAACsC;QAAU,CAAE,CAAC;;;EAG/C,EAAC;EAEFjD,KAAK,CAACuC,SAAS,CACb,MAAM5B,OAAO,CAACwb,YAAY,CAAC9b,KAAK,CAACoC,QAAQ,CAAC,EAC1C,CAAC9B,OAAO,EAAEN,KAAK,CAACoC,QAAQ,CAAC,CAC1B;EAEDzC,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,IAAI5B,OAAO,CAACU,eAAe,CAAC+B,OAAO,EAAE;MACnC,MAAMA,OAAO,GAAGzC,OAAO,CAACiX,SAAS,EAAE;MACnC,IAAIxU,OAAO,KAAK1C,SAAS,CAAC0C,OAAO,EAAE;QACjCzC,OAAO,CAACmD,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;UAC3BQ;QACD,EAAC;;;GAGP,EAAE,CAACzC,OAAO,EAAED,SAAS,CAAC0C,OAAO,CAAC,CAAC;EAEhCpD,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,IAAIlC,KAAK,CAACuE,MAAM,IAAI,CAACoP,SAAS,CAAC3T,KAAK,CAACuE,MAAM,EAAEqZ,OAAO,CAAC3b,OAAO,CAAC,EAAE;MAC7D3B,OAAO,CAACic,MAAM,CAACvc,KAAK,CAACuE,MAAM,EAAEjE,OAAO,CAAC0F,QAAQ,CAACyX,YAAY,CAAC;MAC3DG,OAAO,CAAC3b,OAAO,GAAGjC,KAAK,CAACuE,MAAM;MAC9BlD,eAAe,CAAEqC,KAAK,KAAM;QAAE,GAAGA;MAAK,CAAE,CAAC,CAAC;KAC3C,MAAM;MACLpD,OAAO,CAACkd,mBAAmB,EAAE;;GAEhC,EAAE,CAACxd,KAAK,CAACuE,MAAM,EAAEjE,OAAO,CAAC,CAAC;EAE3BX,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,IAAIlC,KAAK,CAACsD,MAAM,EAAE;MAChBhD,OAAO,CAACmX,UAAU,CAACzX,KAAK,CAACsD,MAAM,CAAC;;GAEnC,EAAE,CAACtD,KAAK,CAACsD,MAAM,EAAEhD,OAAO,CAAC,CAAC;EAE3BX,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnB,IAAI,CAAC5B,OAAO,CAACgG,MAAM,CAACD,KAAK,EAAE;MACzB/F,OAAO,CAACqD,YAAY,EAAE;MACtBrD,OAAO,CAACgG,MAAM,CAACD,KAAK,GAAG,IAAI;;IAG7B,IAAI/F,OAAO,CAACgG,MAAM,CAACrC,KAAK,EAAE;MACxB3D,OAAO,CAACgG,MAAM,CAACrC,KAAK,GAAG,KAAK;MAC5B3D,OAAO,CAACmD,SAAS,CAACC,KAAK,CAACnB,IAAI,CAAC;QAAE,GAAGjC,OAAO,CAACsC;MAAU,CAAE,CAAC;;IAGzDtC,OAAO,CAACqE,gBAAgB,EAAE;EAC5B,CAAC,CAAC;EAEFhF,KAAK,CAACuC,SAAS,CAAC,MAAK;IACnBlC,KAAK,CAACyF,gBAAgB,IACpBnF,OAAO,CAACmD,SAAS,CAACc,MAAM,CAAChC,IAAI,CAAC;MAC5BgC,MAAM,EAAEjE,OAAO,CAACoE,SAAS;IAC1B,EAAC;GACL,EAAE,CAAC1E,KAAK,CAACyF,gBAAgB,EAAEnF,OAAO,CAAC,CAAC;EAErCqd,YAAY,CAAC1b,OAAO,CAAC5B,SAAS,GAAGD,iBAAiB,CAACC,SAAS,EAAEC,OAAO,CAAC;EAEtE,OAAOqd,YAAY,CAAC1b,OAAO;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}