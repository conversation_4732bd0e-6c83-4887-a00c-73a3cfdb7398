{"ast": null, "code": "export { default } from './formatMuiErrorMessage';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/formatMuiErrorMessage/index.js"], "sourcesContent": ["export { default } from './formatMuiErrorMessage';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,yBAAyB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}