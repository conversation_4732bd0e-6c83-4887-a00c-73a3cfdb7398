{"ast": null, "code": "'use client';\n\nexport { default } from './Collapse';\nexport { default as collapseClasses } from './collapseClasses';\nexport * from './collapseClasses';", "map": {"version": 3, "names": ["default", "collapseClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/Collapse/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Collapse';\nexport { default as collapseClasses } from './collapseClasses';\nexport * from './collapseClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASA,OAAO,IAAIC,eAAe,QAAQ,mBAAmB;AAC9D,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}