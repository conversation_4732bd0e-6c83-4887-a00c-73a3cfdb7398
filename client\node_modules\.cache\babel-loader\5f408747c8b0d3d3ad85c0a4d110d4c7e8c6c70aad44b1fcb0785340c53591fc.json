{"ast": null, "code": "// ImageKit configuration for client-side\nexport const imagekitConfig = {\n  publicKey: process.env.REACT_APP_IMAGEKIT_PUBLIC_KEY || 'public_XA3Jkld7ls8x1CsE5y+gjWj5I4k=',\n  urlEndpoint: process.env.REACT_APP_IMAGEKIT_URL_ENDPOINT || 'https://ik.imagekit.io/georgebobby/cluster-files/',\n  authenticationEndpoint: process.env.REACT_APP_BACKEND_URL ? `${process.env.REACT_APP_BACKEND_URL}/imagekit/auth` : 'http://localhost:8800/imagekit/auth'\n};\n\n// Utility function to get authentication parameters from server\nexport const getAuthenticationParameters = async () => {\n  try {\n    const response = await fetch(imagekitConfig.authenticationEndpoint);\n    if (!response.ok) {\n      throw new Error('Failed to get authentication parameters');\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error getting authentication parameters:', error);\n    throw error;\n  }\n};", "map": {"version": 3, "names": ["imagekitConfig", "public<PERSON>ey", "process", "env", "REACT_APP_IMAGEKIT_PUBLIC_KEY", "urlEndpoint", "REACT_APP_IMAGEKIT_URL_ENDPOINT", "authenticationEndpoint", "REACT_APP_BACKEND_URL", "getAuthenticationParameters", "response", "fetch", "ok", "Error", "json", "error", "console"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/config/imagekit.js"], "sourcesContent": ["// ImageKit configuration for client-side\nexport const imagekitConfig = {\n  publicKey: process.env.REACT_APP_IMAGEKIT_PUBLIC_KEY || 'public_XA3Jkld7ls8x1CsE5y+gjWj5I4k=',\n  urlEndpoint: process.env.REACT_APP_IMAGEKIT_URL_ENDPOINT || 'https://ik.imagekit.io/georgebobby/cluster-files/',\n  authenticationEndpoint: process.env.REACT_APP_BACKEND_URL ? \n    `${process.env.REACT_APP_BACKEND_URL}/imagekit/auth` : \n    'http://localhost:8800/imagekit/auth'\n};\n\n// Utility function to get authentication parameters from server\nexport const getAuthenticationParameters = async () => {\n  try {\n    const response = await fetch(imagekitConfig.authenticationEndpoint);\n    if (!response.ok) {\n      throw new Error('Failed to get authentication parameters');\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error getting authentication parameters:', error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,cAAc,GAAG;EAC5BC,SAAS,EAAEC,OAAO,CAACC,GAAG,CAACC,6BAA6B,IAAI,qCAAqC;EAC7FC,WAAW,EAAEH,OAAO,CAACC,GAAG,CAACG,+BAA+B,IAAI,mDAAmD;EAC/GC,sBAAsB,EAAEL,OAAO,CAACC,GAAG,CAACK,qBAAqB,GACtD,GAAEN,OAAO,CAACC,GAAG,CAACK,qBAAsB,gBAAe,GACpD;AACJ,CAAC;;AAED;AACA,OAAO,MAAMC,2BAA2B,GAAG,MAAAA,CAAA,KAAY;EACrD,IAAI;IACF,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAACX,cAAc,CAACO,sBAAsB,CAAC;IACnE,IAAI,CAACG,QAAQ,CAACE,EAAE,EAAE;MAChB,MAAM,IAAIC,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IACA,OAAO,MAAMH,QAAQ,CAACI,IAAI,CAAC,CAAC;EAC9B,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;IAChE,MAAMA,KAAK;EACb;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}