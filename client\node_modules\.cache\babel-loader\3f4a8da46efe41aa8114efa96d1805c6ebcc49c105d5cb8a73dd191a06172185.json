{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemIconUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemIcon', slot);\n}\nconst listItemIconClasses = generateUtilityClasses('MuiListItemIcon', ['root', 'alignItemsFlexStart']);\nexport default listItemIconClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getListItemIconUtilityClass", "slot", "listItemIconClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/ListItemIcon/listItemIconClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getListItemIconUtilityClass(slot) {\n  return generateUtilityClass('MuiListItemIcon', slot);\n}\nconst listItemIconClasses = generateUtilityClasses('MuiListItemIcon', ['root', 'alignItemsFlexStart']);\nexport default listItemIconClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,2BAA2BA,CAACC,IAAI,EAAE;EAChD,OAAOF,oBAAoB,CAAC,iBAAiB,EAAEE,IAAI,CAAC;AACtD;AACA,MAAMC,mBAAmB,GAAGJ,sBAAsB,CAAC,iBAAiB,EAAE,CAAC,MAAM,EAAE,qBAAqB,CAAC,CAAC;AACtG,eAAeI,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}