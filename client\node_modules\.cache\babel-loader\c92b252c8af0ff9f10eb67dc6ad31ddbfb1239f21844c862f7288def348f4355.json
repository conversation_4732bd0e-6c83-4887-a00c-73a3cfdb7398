{"ast": null, "code": "import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const Loading=()=>{return/*#__PURE__*/_jsxs(\"div\",{className:\"dots-container\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"dot\"}),/*#__PURE__*/_jsx(\"div\",{className:\"dot\"}),/*#__PURE__*/_jsx(\"div\",{className:\"dot\"}),/*#__PURE__*/_jsx(\"div\",{className:\"dot\"}),/*#__PURE__*/_jsx(\"div\",{className:\"dot\"})]});};export default Loading;", "map": {"version": 3, "names": ["Loading", "_jsxs", "className", "children", "_jsx"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/components/Loading.jsx"], "sourcesContent": ["const Loading = () => {\r\n  return (\r\n    <div className='dots-container'>\r\n      <div className='dot'></div>\r\n      <div className='dot'></div>\r\n      <div className='dot'></div>\r\n      <div className='dot'></div>\r\n      <div className='dot'></div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Loading;\r\n"], "mappings": "wFAAA,KAAM,CAAAA,OAAO,CAAGA,CAAA,GAAM,CACpB,mBACEC,KAAA,QAAKC,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BC,IAAA,QAAKF,SAAS,CAAC,KAAK,CAAM,CAAC,cAC3BE,IAAA,QAAKF,SAAS,CAAC,KAAK,CAAM,CAAC,cAC3BE,IAAA,QAAKF,SAAS,CAAC,KAAK,CAAM,CAAC,cAC3BE,IAAA,QAAKF,SAAS,CAAC,KAAK,CAAM,CAAC,cAC3BE,IAAA,QAAKF,SAAS,CAAC,KAAK,CAAM,CAAC,EACxB,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}