{"ast": null, "code": "export { default } from './useEventCallback';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/useEventCallback/index.js"], "sourcesContent": ["export { default } from './useEventCallback';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}