{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _linkifyIt = require('linkify-it');\nvar _linkifyIt2 = _interopRequireDefault(_linkifyIt);\nvar _tlds = require('tlds');\nvar _tlds2 = _interopRequireDefault(_tlds);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nvar linkify = new _linkifyIt2.default();\nlinkify.tlds(_tlds2.default);\nexports.default = function (text) {\n  return linkify.match(text);\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_linkifyIt", "require", "_linkifyIt2", "_interopRequireDefault", "_tlds", "_tlds2", "obj", "__esModule", "default", "linkify", "tlds", "text", "match"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/react-linkify/dist/decorators/defaultMatchDecorator.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _linkifyIt = require('linkify-it');\n\nvar _linkifyIt2 = _interopRequireDefault(_linkifyIt);\n\nvar _tlds = require('tlds');\n\nvar _tlds2 = _interopRequireDefault(_tlds);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nvar linkify = new _linkifyIt2.default();\nlinkify.tlds(_tlds2.default);\n\nexports.default = function (text) {\n  return linkify.match(text);\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,IAAIC,UAAU,GAAGC,OAAO,CAAC,YAAY,CAAC;AAEtC,IAAIC,WAAW,GAAGC,sBAAsB,CAACH,UAAU,CAAC;AAEpD,IAAII,KAAK,GAAGH,OAAO,CAAC,MAAM,CAAC;AAE3B,IAAII,MAAM,GAAGF,sBAAsB,CAACC,KAAK,CAAC;AAE1C,SAASD,sBAAsBA,CAACG,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9F,IAAIG,OAAO,GAAG,IAAIP,WAAW,CAACM,OAAO,CAAC,CAAC;AACvCC,OAAO,CAACC,IAAI,CAACL,MAAM,CAACG,OAAO,CAAC;AAE5BV,OAAO,CAACU,OAAO,GAAG,UAAUG,IAAI,EAAE;EAChC,OAAOF,OAAO,CAACG,KAAK,CAACD,IAAI,CAAC;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}