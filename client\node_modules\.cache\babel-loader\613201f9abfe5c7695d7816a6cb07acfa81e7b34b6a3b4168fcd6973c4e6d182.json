{"ast": null, "code": "'use client';\n\nexport { default } from './Menu';\nexport { default as menuClasses } from './menuClasses';\nexport * from './menuClasses';", "map": {"version": 3, "names": ["default", "menuClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/Menu/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Menu';\nexport { default as menuClasses } from './menuClasses';\nexport * from './menuClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,QAAQ;AAChC,SAASA,OAAO,IAAIC,WAAW,QAAQ,eAAe;AACtD,cAAc,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}