{"ast": null, "code": "'use client';\n\nexport { default } from './Paper';\nexport { default as paperClasses } from './paperClasses';\nexport * from './paperClasses';", "map": {"version": 3, "names": ["default", "paperClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/Paper/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './Paper';\nexport { default as paperClasses } from './paperClasses';\nexport * from './paperClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,SAAS;AACjC,SAASA,OAAO,IAAIC,YAAY,QAAQ,gBAAgB;AACxD,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}