{"ast": null, "code": "const teal = {\n  50: '#e0f2f1',\n  100: '#b2dfdb',\n  200: '#80cbc4',\n  300: '#4db6ac',\n  400: '#26a69a',\n  500: '#009688',\n  600: '#00897b',\n  700: '#00796b',\n  800: '#00695c',\n  900: '#004d40',\n  A100: '#a7ffeb',\n  A200: '#64ffda',\n  A400: '#1de9b6',\n  A700: '#00bfa5'\n};\nexport default teal;", "map": {"version": 3, "names": ["teal", "A100", "A200", "A400", "A700"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/colors/teal.js"], "sourcesContent": ["const teal = {\n  50: '#e0f2f1',\n  100: '#b2dfdb',\n  200: '#80cbc4',\n  300: '#4db6ac',\n  400: '#26a69a',\n  500: '#009688',\n  600: '#00897b',\n  700: '#00796b',\n  800: '#00695c',\n  900: '#004d40',\n  A100: '#a7ffeb',\n  A200: '#64ffda',\n  A400: '#1de9b6',\n  A700: '#00bfa5'\n};\nexport default teal;"], "mappings": "AAAA,MAAMA,IAAI,GAAG;EACX,EAAE,EAAE,SAAS;EACb,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACd,GAAG,EAAE,SAAS;EACdC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE,SAAS;EACfC,IAAI,EAAE;AACR,CAAC;AACD,eAAeJ,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}