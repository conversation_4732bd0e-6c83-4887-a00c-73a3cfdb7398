{"ast": null, "code": "export { Menu } from './Menu';\nexport * from './menuClasses';\nexport * from './Menu.types';", "map": {"version": 3, "names": ["<PERSON><PERSON>"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/Menu/index.js"], "sourcesContent": ["export { Menu } from './Menu';\nexport * from './menuClasses';\nexport * from './Menu.types';"], "mappings": "AAAA,SAASA,IAAI,QAAQ,QAAQ;AAC7B,cAAc,eAAe;AAC7B,cAAc,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}