{"ast": null, "code": "import React from\"react\";import{TbSocial}from\"react-icons/tb\";import{useDispatch,useSelector}from\"react-redux\";import{Link}from\"react-router-dom\";import TextInput from\"./TextInput\";import CustomButton from\"./CustomButton\";import{useForm}from\"react-hook-form\";import{BsMoon,BsSunFill}from\"react-icons/bs\";import{IoMdNotificationsOutline}from\"react-icons/io\";import{FaProjectDiagram}from\"react-icons/fa\";import{MdOutlineEventNote}from\"react-icons/md\";import{BiBookReader}from\"react-icons/bi\";import{SetTheme}from\"../redux/theme\";import{Logout}from\"../redux/userSlice\";import{jsx as _jsx}from\"react/jsx-runtime\";import{jsxs as _jsxs}from\"react/jsx-runtime\";const TopBar=()=>{const{theme}=useSelector(state=>state.theme);const{user}=useSelector(state=>state.user);const dispatch=useDispatch();const{register,handleSubmit,formState:{errors}}=useForm();const handleTheme=()=>{const themeValue=theme===\"light\"?\"dark\":\"light\";dispatch(SetTheme(themeValue));};const handleSearch=async data=>{};return/*#__PURE__*/_jsxs(\"div\",{className:\"topbar w-full flex items-center justify-between py-3 md:py-6 px-4 bg-primary\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/\",className:\"flex gap-2 items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"p-1 md:p-2 rounded text-white \".concat(theme===\"dark\"?\"text-black bg-[#000000]\":\"text-white bg-[#000000]\"),children:/*#__PURE__*/_jsx(TbSocial,{})}),/*#__PURE__*/_jsx(\"span\",{className:\"text-xl md:text-2xl \".concat(theme===\"dark\"?\"text-white\":\"text-[#000000]\",\" font-semibold\"),children:\"Cluster\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"hidden md:flex gap-6 items-center text-lg\",children:[/*#__PURE__*/_jsxs(Link,{to:\"/projects\",className:\"flex items-center gap-2 text-ascent-1\",children:[/*#__PURE__*/_jsx(FaProjectDiagram,{}),/*#__PURE__*/_jsx(\"span\",{children:\"Projects\"})]}),/*#__PURE__*/_jsxs(Link,{to:\"/hackathons\",className:\"flex items-center gap-2 text-ascent-1\",children:[/*#__PURE__*/_jsx(MdOutlineEventNote,{}),/*#__PURE__*/_jsx(\"span\",{children:\"Hackathons\"})]}),/*#__PURE__*/_jsxs(Link,{to:\"/research\",className:\"flex items-center gap-2 text-ascent-1\",children:[/*#__PURE__*/_jsx(BiBookReader,{}),/*#__PURE__*/_jsx(\"span\",{children:\"Research\"})]})]}),/*#__PURE__*/_jsxs(\"form\",{className:\"hidden md:flex items-center justify-center\",onSubmit:handleSubmit(handleSearch),children:[/*#__PURE__*/_jsx(TextInput,{placeholder:\"Search...\",styles:\"w-[12rem] lg:w-[24rem] rounded-l-full py-2 h-[40px]\"// Explicitly set height to 40px\n,register:register(\"search\")}),/*#__PURE__*/_jsx(CustomButton,{title:\"Search\",type:\"submit\",containerStyles:\"bg-[#0444a4] text-white px-6 py-2 rounded-r-full h-[40px]\"// Set same height as TextInput\n})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex gap-4 items-center text-ascent-1 text-md md:text-xl\",children:[/*#__PURE__*/_jsx(\"button\",{onClick:()=>handleTheme(),children:theme?/*#__PURE__*/_jsx(BsMoon,{}):/*#__PURE__*/_jsx(BsSunFill,{})}),/*#__PURE__*/_jsx(\"div\",{className:\"hidden lg:flex\",children:/*#__PURE__*/_jsx(IoMdNotificationsOutline,{})}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(CustomButton,{onClick:()=>dispatch(Logout()),title:\"Log Out\",containerStyles:\"text-sm text-ascent-1 px-4 md:px-6 py-1 md:py-2 border border-[#666] rounded-full\"})})]})]});};export default TopBar;", "map": {"version": 3, "names": ["React", "TbSocial", "useDispatch", "useSelector", "Link", "TextInput", "CustomButton", "useForm", "BsMoon", "BsSunFill", "IoMdNotificationsOutline", "FaProjectDiagram", "MdOutlineEventNote", "BiBookReader", "SetTheme", "Logout", "jsx", "_jsx", "jsxs", "_jsxs", "TopBar", "theme", "state", "user", "dispatch", "register", "handleSubmit", "formState", "errors", "handleTheme", "themeValue", "handleSearch", "data", "className", "children", "to", "concat", "onSubmit", "placeholder", "styles", "title", "type", "containerStyles", "onClick"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/components/TopBar.jsx"], "sourcesContent": ["import React from \"react\";\r\nimport { TbSocial } from \"react-icons/tb\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { Link } from \"react-router-dom\";\r\nimport TextInput from \"./TextInput\";\r\nimport CustomButton from \"./CustomButton\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { BsMoon, BsSunFill } from \"react-icons/bs\";\r\nimport { IoMdNotificationsOutline } from \"react-icons/io\";\r\nimport { FaProjectDiagram } from \"react-icons/fa\";\r\nimport { MdOutlineEventNote } from \"react-icons/md\";\r\nimport { BiBookReader } from \"react-icons/bi\";\r\nimport { SetTheme } from \"../redux/theme\";\r\nimport { Logout } from \"../redux/userSlice\";\r\n\r\nconst TopBar = () => {\r\n  const { theme } = useSelector((state) => state.theme);\r\n  const { user } = useSelector((state) => state.user);\r\n  const dispatch = useDispatch();\r\n  const {\r\n    register,\r\n    handleSubmit,\r\n    formState: { errors },\r\n  } = useForm();\r\n\r\n  const handleTheme = () => {\r\n    const themeValue = theme === \"light\" ? \"dark\" : \"light\";\r\n    dispatch(SetTheme(themeValue));\r\n  };\r\n\r\n  const handleSearch = async (data) => { };\r\n\r\n  return (\r\n    <div className='topbar w-full flex items-center justify-between py-3 md:py-6 px-4 bg-primary'>\r\n      {/* Logo and Name */}\r\n      <Link to='/' className='flex gap-2 items-center'>\r\n        <div\r\n          className={`p-1 md:p-2 rounded text-white ${theme === \"dark\" ? \"text-black bg-[#000000]\" : \"text-white bg-[#000000]\"\r\n            }`}\r\n        >\r\n          <TbSocial />\r\n        </div>\r\n        <span\r\n          className={`text-xl md:text-2xl ${theme === \"dark\" ? \"text-white\" : \"text-[#000000]\"\r\n            } font-semibold`}\r\n        >\r\n          Cluster\r\n        </span>\r\n      </Link>\r\n\r\n      {/* Navigation Links */}\r\n      <div className='hidden md:flex gap-6 items-center text-lg'>\r\n        <Link to='/projects' className='flex items-center gap-2 text-ascent-1'>\r\n          <FaProjectDiagram />\r\n          <span>Projects</span>\r\n        </Link>\r\n        <Link to='/hackathons' className='flex items-center gap-2 text-ascent-1'>\r\n          <MdOutlineEventNote />\r\n          <span>Hackathons</span>\r\n        </Link>\r\n        <Link to='/research' className='flex items-center gap-2 text-ascent-1'>\r\n          <BiBookReader />\r\n          <span>Research</span>\r\n        </Link>\r\n      </div>\r\n\r\n      {/* Search Bar */}\r\n      <form\r\n        className='hidden md:flex items-center justify-center'\r\n        onSubmit={handleSubmit(handleSearch)}\r\n      >\r\n        <TextInput\r\n          placeholder='Search...'\r\n          styles='w-[12rem] lg:w-[24rem] rounded-l-full py-2 h-[40px]'  // Explicitly set height to 40px\r\n          register={register(\"search\")}\r\n        />\r\n        <CustomButton\r\n          title='Search'\r\n          type='submit'\r\n          containerStyles='bg-[#0444a4] text-white px-6 py-2 rounded-r-full h-[40px]'  // Set same height as TextInput\r\n        />\r\n      </form>\r\n\r\n      {/* Icons */}\r\n      <div className='flex gap-4 items-center text-ascent-1 text-md md:text-xl'>\r\n        <button onClick={() => handleTheme()}>\r\n          {theme ? <BsMoon /> : <BsSunFill />}\r\n        </button>\r\n        <div className='hidden lg:flex'>\r\n          <IoMdNotificationsOutline />\r\n        </div>\r\n        <div>\r\n          <CustomButton\r\n            onClick={() => dispatch(Logout())}\r\n            title='Log Out'\r\n            containerStyles='text-sm text-ascent-1 px-4 md:px-6 py-1 md:py-2 border border-[#666] rounded-full'\r\n          />\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default TopBar;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,KAAQ,gBAAgB,CACzC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,IAAI,KAAQ,kBAAkB,CACvC,MAAO,CAAAC,SAAS,KAAM,aAAa,CACnC,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,OAASC,OAAO,KAAQ,iBAAiB,CACzC,OAASC,MAAM,CAAEC,SAAS,KAAQ,gBAAgB,CAClD,OAASC,wBAAwB,KAAQ,gBAAgB,CACzD,OAASC,gBAAgB,KAAQ,gBAAgB,CACjD,OAASC,kBAAkB,KAAQ,gBAAgB,CACnD,OAASC,YAAY,KAAQ,gBAAgB,CAC7C,OAASC,QAAQ,KAAQ,gBAAgB,CACzC,OAASC,MAAM,KAAQ,oBAAoB,CAAC,OAAAC,GAAA,IAAAC,IAAA,gCAAAC,IAAA,IAAAC,KAAA,yBAE5C,KAAM,CAAAC,MAAM,CAAGA,CAAA,GAAM,CACnB,KAAM,CAAEC,KAAM,CAAC,CAAGlB,WAAW,CAAEmB,KAAK,EAAKA,KAAK,CAACD,KAAK,CAAC,CACrD,KAAM,CAAEE,IAAK,CAAC,CAAGpB,WAAW,CAAEmB,KAAK,EAAKA,KAAK,CAACC,IAAI,CAAC,CACnD,KAAM,CAAAC,QAAQ,CAAGtB,WAAW,CAAC,CAAC,CAC9B,KAAM,CACJuB,QAAQ,CACRC,YAAY,CACZC,SAAS,CAAE,CAAEC,MAAO,CACtB,CAAC,CAAGrB,OAAO,CAAC,CAAC,CAEb,KAAM,CAAAsB,WAAW,CAAGA,CAAA,GAAM,CACxB,KAAM,CAAAC,UAAU,CAAGT,KAAK,GAAK,OAAO,CAAG,MAAM,CAAG,OAAO,CACvDG,QAAQ,CAACV,QAAQ,CAACgB,UAAU,CAAC,CAAC,CAChC,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,KAAO,CAAAC,IAAI,EAAK,CAAE,CAAC,CAExC,mBACEb,KAAA,QAAKc,SAAS,CAAC,8EAA8E,CAAAC,QAAA,eAE3Ff,KAAA,CAACf,IAAI,EAAC+B,EAAE,CAAC,GAAG,CAACF,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAC9CjB,IAAA,QACEgB,SAAS,kCAAAG,MAAA,CAAmCf,KAAK,GAAK,MAAM,CAAG,yBAAyB,CAAG,yBAAyB,CAC/G,CAAAa,QAAA,cAELjB,IAAA,CAAChB,QAAQ,GAAE,CAAC,CACT,CAAC,cACNgB,IAAA,SACEgB,SAAS,wBAAAG,MAAA,CAAyBf,KAAK,GAAK,MAAM,CAAG,YAAY,CAAG,gBAAgB,kBACjE,CAAAa,QAAA,CACpB,SAED,CAAM,CAAC,EACH,CAAC,cAGPf,KAAA,QAAKc,SAAS,CAAC,2CAA2C,CAAAC,QAAA,eACxDf,KAAA,CAACf,IAAI,EAAC+B,EAAE,CAAC,WAAW,CAACF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpEjB,IAAA,CAACN,gBAAgB,GAAE,CAAC,cACpBM,IAAA,SAAAiB,QAAA,CAAM,UAAQ,CAAM,CAAC,EACjB,CAAC,cACPf,KAAA,CAACf,IAAI,EAAC+B,EAAE,CAAC,aAAa,CAACF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACtEjB,IAAA,CAACL,kBAAkB,GAAE,CAAC,cACtBK,IAAA,SAAAiB,QAAA,CAAM,YAAU,CAAM,CAAC,EACnB,CAAC,cACPf,KAAA,CAACf,IAAI,EAAC+B,EAAE,CAAC,WAAW,CAACF,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eACpEjB,IAAA,CAACJ,YAAY,GAAE,CAAC,cAChBI,IAAA,SAAAiB,QAAA,CAAM,UAAQ,CAAM,CAAC,EACjB,CAAC,EACJ,CAAC,cAGNf,KAAA,SACEc,SAAS,CAAC,4CAA4C,CACtDI,QAAQ,CAAEX,YAAY,CAACK,YAAY,CAAE,CAAAG,QAAA,eAErCjB,IAAA,CAACZ,SAAS,EACRiC,WAAW,CAAC,WAAW,CACvBC,MAAM,CAAC,qDAAuD;AAAA,CAC9Dd,QAAQ,CAAEA,QAAQ,CAAC,QAAQ,CAAE,CAC9B,CAAC,cACFR,IAAA,CAACX,YAAY,EACXkC,KAAK,CAAC,QAAQ,CACdC,IAAI,CAAC,QAAQ,CACbC,eAAe,CAAC,2DAA6D;AAAA,CAC9E,CAAC,EACE,CAAC,cAGPvB,KAAA,QAAKc,SAAS,CAAC,0DAA0D,CAAAC,QAAA,eACvEjB,IAAA,WAAQ0B,OAAO,CAAEA,CAAA,GAAMd,WAAW,CAAC,CAAE,CAAAK,QAAA,CAClCb,KAAK,cAAGJ,IAAA,CAACT,MAAM,GAAE,CAAC,cAAGS,IAAA,CAACR,SAAS,GAAE,CAAC,CAC7B,CAAC,cACTQ,IAAA,QAAKgB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,cAC7BjB,IAAA,CAACP,wBAAwB,GAAE,CAAC,CACzB,CAAC,cACNO,IAAA,QAAAiB,QAAA,cACEjB,IAAA,CAACX,YAAY,EACXqC,OAAO,CAAEA,CAAA,GAAMnB,QAAQ,CAACT,MAAM,CAAC,CAAC,CAAE,CAClCyB,KAAK,CAAC,SAAS,CACfE,eAAe,CAAC,mFAAmF,CACpG,CAAC,CACC,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAtB,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}