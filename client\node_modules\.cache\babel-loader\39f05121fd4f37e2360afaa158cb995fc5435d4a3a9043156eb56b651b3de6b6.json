{"ast": null, "code": "'use client';\n\nexport { default } from './TableHead';\nexport { default as tableHeadClasses } from './tableHeadClasses';\nexport * from './tableHeadClasses';", "map": {"version": 3, "names": ["default", "tableHeadClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/TableHead/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './TableHead';\nexport { default as tableHeadClasses } from './tableHeadClasses';\nexport * from './tableHeadClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,oBAAoB;AAChE,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}