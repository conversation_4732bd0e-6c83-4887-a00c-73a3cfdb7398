{"ast": null, "code": "/**\n * Safe chained function.\n *\n * Will only create a new function if needed,\n * otherwise will pass back existing functions or null.\n */\nexport default function createChainedFunction() {\n  for (var _len = arguments.length, funcs = new Array(_len), _key = 0; _key < _len; _key++) {\n    funcs[_key] = arguments[_key];\n  }\n  return funcs.reduce((acc, func) => {\n    if (func == null) {\n      return acc;\n    }\n    return function chainedFunction() {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      acc.apply(this, args);\n      func.apply(this, args);\n    };\n  }, () => {});\n}", "map": {"version": 3, "names": ["createChainedFunction", "_len", "arguments", "length", "funcs", "Array", "_key", "reduce", "acc", "func", "chainedFunction", "_len2", "args", "_key2", "apply"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/createChainedFunction/createChainedFunction.js"], "sourcesContent": ["/**\n * Safe chained function.\n *\n * Will only create a new function if needed,\n * otherwise will pass back existing functions or null.\n */\nexport default function createChainedFunction(...funcs) {\n  return funcs.reduce((acc, func) => {\n    if (func == null) {\n      return acc;\n    }\n    return function chainedFunction(...args) {\n      acc.apply(this, args);\n      func.apply(this, args);\n    };\n  }, () => {});\n}"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASA,qBAAqBA,CAAA,EAAW;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAAPC,KAAK,OAAAC,KAAA,CAAAJ,IAAA,GAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAALF,KAAK,CAAAE,IAAA,IAAAJ,SAAA,CAAAI,IAAA;EAAA;EACpD,OAAOF,KAAK,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,IAAI,KAAK;IACjC,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChB,OAAOD,GAAG;IACZ;IACA,OAAO,SAASE,eAAeA,CAAA,EAAU;MAAA,SAAAC,KAAA,GAAAT,SAAA,CAAAC,MAAA,EAANS,IAAI,OAAAP,KAAA,CAAAM,KAAA,GAAAE,KAAA,MAAAA,KAAA,GAAAF,KAAA,EAAAE,KAAA;QAAJD,IAAI,CAAAC,KAAA,IAAAX,SAAA,CAAAW,KAAA;MAAA;MACrCL,GAAG,CAACM,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;MACrBH,IAAI,CAACK,KAAK,CAAC,IAAI,EAAEF,IAAI,CAAC;IACxB,CAAC;EACH,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}