{"ast": null, "code": "import { unsafeStringify } from './stringify.js';\nimport parse from './parse.js';\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  const bytes = [];\n  for (let i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n  return bytes;\n}\nexport const DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport const URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function v35(name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    var _namespace;\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n    if (typeof namespace === 'string') {\n      namespace = parse(namespace);\n    }\n    if (((_namespace = namespace) === null || _namespace === void 0 ? void 0 : _namespace.length) !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    } // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n\n    let bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n    if (buf) {\n      offset = offset || 0;\n      for (let i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n      return buf;\n    }\n    return unsafeStringify(bytes);\n  } // Function#name is not settable on some platforms (#270)\n\n  try {\n    generateUUID.name = name; // eslint-disable-next-line no-empty\n  } catch (err) {} // For CommonJS default export support\n\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}", "map": {"version": 3, "names": ["unsafeStringify", "parse", "stringToBytes", "str", "unescape", "encodeURIComponent", "bytes", "i", "length", "push", "charCodeAt", "DNS", "URL", "v35", "name", "version", "hashfunc", "generateUUID", "value", "namespace", "buf", "offset", "_namespace", "TypeError", "Uint8Array", "set", "err"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/uuid/dist/esm-browser/v35.js"], "sourcesContent": ["import { unsafeStringify } from './stringify.js';\nimport parse from './parse.js';\n\nfunction stringToBytes(str) {\n  str = unescape(encodeURIComponent(str)); // UTF8 escape\n\n  const bytes = [];\n\n  for (let i = 0; i < str.length; ++i) {\n    bytes.push(str.charCodeAt(i));\n  }\n\n  return bytes;\n}\n\nexport const DNS = '6ba7b810-9dad-11d1-80b4-00c04fd430c8';\nexport const URL = '6ba7b811-9dad-11d1-80b4-00c04fd430c8';\nexport default function v35(name, version, hashfunc) {\n  function generateUUID(value, namespace, buf, offset) {\n    var _namespace;\n\n    if (typeof value === 'string') {\n      value = stringToBytes(value);\n    }\n\n    if (typeof namespace === 'string') {\n      namespace = parse(namespace);\n    }\n\n    if (((_namespace = namespace) === null || _namespace === void 0 ? void 0 : _namespace.length) !== 16) {\n      throw TypeError('Namespace must be array-like (16 iterable integer values, 0-255)');\n    } // Compute hash of namespace and value, Per 4.3\n    // Future: Use spread syntax when supported on all platforms, e.g. `bytes =\n    // hashfunc([...namespace, ... value])`\n\n\n    let bytes = new Uint8Array(16 + value.length);\n    bytes.set(namespace);\n    bytes.set(value, namespace.length);\n    bytes = hashfunc(bytes);\n    bytes[6] = bytes[6] & 0x0f | version;\n    bytes[8] = bytes[8] & 0x3f | 0x80;\n\n    if (buf) {\n      offset = offset || 0;\n\n      for (let i = 0; i < 16; ++i) {\n        buf[offset + i] = bytes[i];\n      }\n\n      return buf;\n    }\n\n    return unsafeStringify(bytes);\n  } // Function#name is not settable on some platforms (#270)\n\n\n  try {\n    generateUUID.name = name; // eslint-disable-next-line no-empty\n  } catch (err) {} // For CommonJS default export support\n\n\n  generateUUID.DNS = DNS;\n  generateUUID.URL = URL;\n  return generateUUID;\n}"], "mappings": "AAAA,SAASA,eAAe,QAAQ,gBAAgB;AAChD,OAAOC,KAAK,MAAM,YAAY;AAE9B,SAASC,aAAaA,CAACC,GAAG,EAAE;EAC1BA,GAAG,GAAGC,QAAQ,CAACC,kBAAkB,CAACF,GAAG,CAAC,CAAC,CAAC,CAAC;;EAEzC,MAAMG,KAAK,GAAG,EAAE;EAEhB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,GAAG,CAACK,MAAM,EAAE,EAAED,CAAC,EAAE;IACnCD,KAAK,CAACG,IAAI,CAACN,GAAG,CAACO,UAAU,CAACH,CAAC,CAAC,CAAC;EAC/B;EAEA,OAAOD,KAAK;AACd;AAEA,OAAO,MAAMK,GAAG,GAAG,sCAAsC;AACzD,OAAO,MAAMC,GAAG,GAAG,sCAAsC;AACzD,eAAe,SAASC,GAAGA,CAACC,IAAI,EAAEC,OAAO,EAAEC,QAAQ,EAAE;EACnD,SAASC,YAAYA,CAACC,KAAK,EAAEC,SAAS,EAAEC,GAAG,EAAEC,MAAM,EAAE;IACnD,IAAIC,UAAU;IAEd,IAAI,OAAOJ,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAGhB,aAAa,CAACgB,KAAK,CAAC;IAC9B;IAEA,IAAI,OAAOC,SAAS,KAAK,QAAQ,EAAE;MACjCA,SAAS,GAAGlB,KAAK,CAACkB,SAAS,CAAC;IAC9B;IAEA,IAAI,CAAC,CAACG,UAAU,GAAGH,SAAS,MAAM,IAAI,IAAIG,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACd,MAAM,MAAM,EAAE,EAAE;MACpG,MAAMe,SAAS,CAAC,kEAAkE,CAAC;IACrF,CAAC,CAAC;IACF;IACA;;IAGA,IAAIjB,KAAK,GAAG,IAAIkB,UAAU,CAAC,EAAE,GAAGN,KAAK,CAACV,MAAM,CAAC;IAC7CF,KAAK,CAACmB,GAAG,CAACN,SAAS,CAAC;IACpBb,KAAK,CAACmB,GAAG,CAACP,KAAK,EAAEC,SAAS,CAACX,MAAM,CAAC;IAClCF,KAAK,GAAGU,QAAQ,CAACV,KAAK,CAAC;IACvBA,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGS,OAAO;IACpCT,KAAK,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC,GAAG,IAAI,GAAG,IAAI;IAEjC,IAAIc,GAAG,EAAE;MACPC,MAAM,GAAGA,MAAM,IAAI,CAAC;MAEpB,KAAK,IAAId,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAE,EAAEA,CAAC,EAAE;QAC3Ba,GAAG,CAACC,MAAM,GAAGd,CAAC,CAAC,GAAGD,KAAK,CAACC,CAAC,CAAC;MAC5B;MAEA,OAAOa,GAAG;IACZ;IAEA,OAAOpB,eAAe,CAACM,KAAK,CAAC;EAC/B,CAAC,CAAC;;EAGF,IAAI;IACFW,YAAY,CAACH,IAAI,GAAGA,IAAI,CAAC,CAAC;EAC5B,CAAC,CAAC,OAAOY,GAAG,EAAE,CAAC,CAAC,CAAC;;EAGjBT,YAAY,CAACN,GAAG,GAAGA,GAAG;EACtBM,YAAY,CAACL,GAAG,GAAGA,GAAG;EACtB,OAAOK,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}