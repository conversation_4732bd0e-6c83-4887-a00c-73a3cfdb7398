{"ast": null, "code": "export { default } from './useIsFocusVisible';\nexport * from './useIsFocusVisible';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/useIsFocusVisible/index.js"], "sourcesContent": ["export { default } from './useIsFocusVisible';\nexport * from './useIsFocusVisible';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,qBAAqB;AAC7C,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}