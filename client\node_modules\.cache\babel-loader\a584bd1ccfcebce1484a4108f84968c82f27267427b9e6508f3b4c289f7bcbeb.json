{"ast": null, "code": "export { FocusTrap as default } from '@mui/base/FocusTrap';", "map": {"version": 3, "names": ["FocusTrap", "default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/Unstable_TrapFocus/index.js"], "sourcesContent": ["export { FocusTrap as default } from '@mui/base/FocusTrap';"], "mappings": "AAAA,SAASA,SAAS,IAAIC,OAAO,QAAQ,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}