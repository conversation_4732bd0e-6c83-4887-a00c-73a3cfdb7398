[{"C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\redux\\store.js": "3", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\redux\\reducer.js": "4", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\index.js": "5", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\redux\\theme.js": "6", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\redux\\postSlice.js": "7", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\redux\\userSlice.js": "8", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Register.jsx": "9", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Login.jsx": "10", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\ResetPassword.jsx": "11", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Home.jsx": "12", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Admin.jsx": "13", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Notifications.jsx": "14", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Profile.jsx": "15", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Ai.jsx": "16", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\assets\\data.js": "17", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\assets\\index.js": "18", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\TopBar.jsx": "19", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\index.js": "20", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\TextInput.jsx": "21", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\CustomButton.jsx": "22", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\ProfileCard.jsx": "23", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\Loading.jsx": "24", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\FriendsCard.jsx": "25", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\PostCard.jsx": "26", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\EditProfile.jsx": "27", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\FollowerModel.jsx": "28", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\SkillProfilePills.jsx": "29", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\utils\\api.js": "30", "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\config\\imagekit.js": "31"}, {"size": 495, "mtime": 1726807244029, "results": "32", "hashOfConfig": "33"}, {"size": 1276, "mtime": 1726807243733, "results": "34", "hashOfConfig": "33"}, {"size": 220, "mtime": 1726807244042, "results": "35", "hashOfConfig": "33"}, {"size": 302, "mtime": 1726807244041, "results": "36", "hashOfConfig": "33"}, {"size": 359, "mtime": 1726807244039, "results": "37", "hashOfConfig": "33"}, {"size": 623, "mtime": 1726807244043, "results": "38", "hashOfConfig": "33"}, {"size": 431, "mtime": 1726807244041, "results": "39", "hashOfConfig": "33"}, {"size": 1033, "mtime": 1726807244044, "results": "40", "hashOfConfig": "33"}, {"size": 13193, "mtime": 1757573892894, "results": "41", "hashOfConfig": "33"}, {"size": 5996, "mtime": 1757574754484, "results": "42", "hashOfConfig": "33"}, {"size": 2119, "mtime": 1726807244038, "results": "43", "hashOfConfig": "33"}, {"size": 17182, "mtime": 1757580221343, "results": "44", "hashOfConfig": "33"}, {"size": 4688, "mtime": 1757573892849, "results": "45", "hashOfConfig": "33"}, {"size": 1264, "mtime": 1726807244035, "results": "46", "hashOfConfig": "33"}, {"size": 2845, "mtime": 1757573892864, "results": "47", "hashOfConfig": "33"}, {"size": 3563, "mtime": 1726807244031, "results": "48", "hashOfConfig": "33"}, {"size": 20549, "mtime": 1726807243951, "results": "49", "hashOfConfig": "33"}, {"size": 111, "mtime": 1726807243954, "results": "50", "hashOfConfig": "33"}, {"size": 3561, "mtime": 1735072630276, "results": "51", "hashOfConfig": "33"}, {"size": 441, "mtime": 1726807244025, "results": "52", "hashOfConfig": "33"}, {"size": 930, "mtime": 1726807244023, "results": "53", "hashOfConfig": "33"}, {"size": 381, "mtime": 1726807244016, "results": "54", "hashOfConfig": "33"}, {"size": 8015, "mtime": 1757573892864, "results": "55", "hashOfConfig": "33"}, {"size": 299, "mtime": 1726807244020, "results": "56", "hashOfConfig": "33"}, {"size": 1409, "mtime": 1726807244019, "results": "57", "hashOfConfig": "33"}, {"size": 11181, "mtime": 1757573893451, "results": "58", "hashOfConfig": "33"}, {"size": 9949, "mtime": 1757573892033, "results": "59", "hashOfConfig": "33"}, {"size": 1430, "mtime": 1726807244018, "results": "60", "hashOfConfig": "33"}, {"size": 867, "mtime": 1735072630259, "results": "61", "hashOfConfig": "33"}, {"size": 63, "mtime": 1757574754484, "results": "62", "hashOfConfig": "33"}, {"size": 902, "mtime": 1757580006245, "results": "63", "hashOfConfig": "33"}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1r89zq9", {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "88", "messages": "89", "suppressedMessages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "91", "messages": "92", "suppressedMessages": "93", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "94", "messages": "95", "suppressedMessages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "97", "messages": "98", "suppressedMessages": "99", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "100", "messages": "101", "suppressedMessages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "103", "messages": "104", "suppressedMessages": "105", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "106", "messages": "107", "suppressedMessages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "109", "messages": "110", "suppressedMessages": "111", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "112", "messages": "113", "suppressedMessages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "115", "messages": "116", "suppressedMessages": "117", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "118", "messages": "119", "suppressedMessages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "121", "messages": "122", "suppressedMessages": "123", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "124", "messages": "125", "suppressedMessages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\redux\\store.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\redux\\reducer.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\redux\\theme.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\redux\\postSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\redux\\userSlice.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Register.jsx", ["157", "158", "159", "160", "161", "162"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Login.jsx", ["163", "164", "165"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\ResetPassword.jsx", ["166", "167", "168"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Home.jsx", ["169", "170", "171", "172", "173", "174", "175", "176", "177", "178", "179", "180", "181", "182", "183", "184", "185"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Admin.jsx", ["186"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Notifications.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Profile.jsx", ["187", "188", "189"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\pages\\Ai.jsx", ["190", "191", "192", "193"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\assets\\data.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\assets\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\TopBar.jsx", ["194", "195"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\TextInput.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\CustomButton.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\ProfileCard.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\Loading.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\FriendsCard.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\PostCard.jsx", ["196", "197", "198", "199", "200"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\EditProfile.jsx", ["201"], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\FollowerModel.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\components\\SkillProfilePills.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\utils\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Documents\\GitHub\\cluster-delta\\client\\src\\config\\imagekit.js", [], [], {"ruleId": "202", "severity": 1, "message": "203", "line": 28, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 28, "endColumn": 24}, {"ruleId": "202", "severity": 1, "message": "206", "line": 28, "column": 26, "nodeType": "204", "messageId": "205", "endLine": 28, "endColumn": 44}, {"ruleId": "202", "severity": 1, "message": "207", "line": 29, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 29, "endColumn": 18}, {"ruleId": "208", "severity": 1, "message": "209", "line": 299, "column": 24, "nodeType": "210", "messageId": "211", "endLine": 299, "endColumn": 26}, {"ruleId": "208", "severity": 1, "message": "212", "line": 315, "column": 25, "nodeType": "210", "messageId": "211", "endLine": 315, "endColumn": 27}, {"ruleId": "213", "severity": 1, "message": "214", "line": 349, "column": 7, "nodeType": "215", "endLine": 353, "endColumn": 9}, {"ruleId": "202", "severity": 1, "message": "216", "line": 11, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 11, "endColumn": 17}, {"ruleId": "208", "severity": 1, "message": "212", "line": 135, "column": 25, "nodeType": "210", "messageId": "211", "endLine": 135, "endColumn": 27}, {"ruleId": "213", "severity": 1, "message": "214", "line": 168, "column": 7, "nodeType": "215", "endLine": 172, "endColumn": 9}, {"ruleId": "202", "severity": 1, "message": "217", "line": 6, "column": 18, "nodeType": "204", "messageId": "205", "endLine": 6, "endColumn": 27}, {"ruleId": "202", "severity": 1, "message": "218", "line": 7, "column": 24, "nodeType": "204", "messageId": "205", "endLine": 7, "endColumn": 39}, {"ruleId": "202", "severity": 1, "message": "219", "line": 18, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 18, "endColumn": 17}, {"ruleId": "202", "severity": 1, "message": "220", "line": 16, "column": 29, "nodeType": "204", "messageId": "205", "endLine": 16, "endColumn": 34}, {"ruleId": "202", "severity": 1, "message": "221", "line": 19, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 19, "endColumn": 23}, {"ruleId": "202", "severity": 1, "message": "222", "line": 22, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 22, "endColumn": 18}, {"ruleId": "202", "severity": 1, "message": "223", "line": 23, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 23, "endColumn": 18}, {"ruleId": "202", "severity": 1, "message": "224", "line": 25, "column": 2, "nodeType": "204", "messageId": "205", "endLine": 25, "endColumn": 16}, {"ruleId": "202", "severity": 1, "message": "225", "line": 26, "column": 2, "nodeType": "204", "messageId": "205", "endLine": 26, "endColumn": 29}, {"ruleId": "202", "severity": 1, "message": "226", "line": 31, "column": 8, "nodeType": "204", "messageId": "205", "endLine": 31, "endColumn": 24}, {"ruleId": "202", "severity": 1, "message": "227", "line": 38, "column": 24, "nodeType": "204", "messageId": "205", "endLine": 38, "endColumn": 40}, {"ruleId": "202", "severity": 1, "message": "228", "line": 39, "column": 27, "nodeType": "204", "messageId": "205", "endLine": 39, "endColumn": 46}, {"ruleId": "202", "severity": 1, "message": "229", "line": 41, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 41, "endColumn": 13}, {"ruleId": "202", "severity": 1, "message": "230", "line": 41, "column": 15, "nodeType": "204", "messageId": "205", "endLine": 41, "endColumn": 22}, {"ruleId": "202", "severity": 1, "message": "231", "line": 44, "column": 8, "nodeType": "204", "messageId": "205", "endLine": 44, "endColumn": 16}, {"ruleId": "202", "severity": 1, "message": "232", "line": 49, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 49, "endColumn": 17}, {"ruleId": "202", "severity": 1, "message": "233", "line": 49, "column": 19, "nodeType": "204", "messageId": "205", "endLine": 49, "endColumn": 30}, {"ruleId": "202", "severity": 1, "message": "234", "line": 292, "column": 8, "nodeType": "204", "messageId": "205", "endLine": 292, "endColumn": 21}, {"ruleId": "202", "severity": 1, "message": "235", "line": 299, "column": 8, "nodeType": "204", "messageId": "205", "endLine": 299, "endColumn": 19}, {"ruleId": "213", "severity": 1, "message": "214", "line": 330, "column": 9, "nodeType": "215", "endLine": 334, "endColumn": 11}, {"ruleId": "202", "severity": 1, "message": "236", "line": 14, "column": 8, "nodeType": "204", "messageId": "205", "endLine": 14, "endColumn": 13}, {"ruleId": "202", "severity": 1, "message": "237", "line": 4, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 4, "endColumn": 14}, {"ruleId": "202", "severity": 1, "message": "238", "line": 15, "column": 9, "nodeType": "204", "messageId": "205", "endLine": 15, "endColumn": 20}, {"ruleId": "239", "severity": 1, "message": "240", "line": 23, "column": 5, "nodeType": "241", "endLine": 23, "endColumn": 7, "suggestions": "242"}, {"ruleId": "202", "severity": 1, "message": "226", "line": 7, "column": 8, "nodeType": "204", "messageId": "205", "endLine": 7, "endColumn": 24}, {"ruleId": "202", "severity": 1, "message": "243", "line": 10, "column": 10, "nodeType": "204", "messageId": "205", "endLine": 10, "endColumn": 12}, {"ruleId": "202", "severity": 1, "message": "244", "line": 14, "column": 19, "nodeType": "204", "messageId": "205", "endLine": 14, "endColumn": 23}, {"ruleId": "202", "severity": 1, "message": "245", "line": 17, "column": 22, "nodeType": "204", "messageId": "205", "endLine": 17, "endColumn": 33}, {"ruleId": "202", "severity": 1, "message": "246", "line": 18, "column": 11, "nodeType": "204", "messageId": "205", "endLine": 18, "endColumn": 15}, {"ruleId": "202", "severity": 1, "message": "247", "line": 23, "column": 18, "nodeType": "204", "messageId": "205", "endLine": 23, "endColumn": 24}, {"ruleId": "202", "severity": 1, "message": "248", "line": 62, "column": 18, "nodeType": "204", "messageId": "205", "endLine": 62, "endColumn": 28}, {"ruleId": "202", "severity": 1, "message": "217", "line": 63, "column": 17, "nodeType": "204", "messageId": "205", "endLine": 63, "endColumn": 26}, {"ruleId": "202", "severity": 1, "message": "249", "line": 67, "column": 3, "nodeType": "204", "messageId": "205", "endLine": 67, "endColumn": 8}, {"ruleId": "213", "severity": 1, "message": "214", "line": 81, "column": 5, "nodeType": "215", "endLine": 85, "endColumn": 7}, {"ruleId": "213", "severity": 1, "message": "214", "line": 234, "column": 6, "nodeType": "215", "endLine": 238, "endColumn": 8}, {"ruleId": "239", "severity": 1, "message": "250", "line": 47, "column": 5, "nodeType": "241", "endLine": 47, "endColumn": 7, "suggestions": "251"}, "no-unused-vars", "'selectedAvatars' is assigned a value but never used.", "Identifier", "unusedVar", "'setSelectedAvatars' is assigned a value but never used.", "'hostedURL' is assigned a value but never used.", "eqeqeq", "Expected '!==' and instead saw '!='.", "BinaryExpression", "unexpected", "Expected '===' and instead saw '=='.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "'BgImage' is defined but never used.", "'setErrMsg' is assigned a value but never used.", "'setIsSubmitting' is assigned a value but never used.", "'onSubmit' is assigned a value but never used.", "'posts' is defined but never used.", "'BsFiletypeGif' is defined but never used.", "'SetPosts' is defined but never used.", "'IKUpload' is defined but never used.", "'imagekitConfig' is defined but never used.", "'getAuthenticationParameters' is defined but never used.", "'ControlPointIcon' is defined but never used.", "'setFriendRequest' is assigned a value but never used.", "'setSuggestedFriends' is assigned a value but never used.", "'file' is assigned a value but never used.", "'setFile' is assigned a value but never used.", "'dispatch' is assigned a value but never used.", "'postText' is assigned a value but never used.", "'setPostText' is assigned a value but never used.", "'gradientStyle' is assigned a value but never used.", "'handleClose' is assigned a value but never used.", "'style' is defined but never used.", "'Link' is defined but never used.", "'isFollowing' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'user._id'. Either include it or remove the dependency array.", "ArrayExpression", ["252"], "'v4' is defined but never used.", "'edit' is assigned a value but never used.", "'setIsMobile' is assigned a value but never used.", "'user' is assigned a value but never used.", "'errors' is assigned a value but never used.", "'setLoading' is assigned a value but never used.", "'reset' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'user'. Either include it or remove the dependency array.", ["253"], {"desc": "254", "fix": "255"}, {"desc": "256", "fix": "257"}, "Update the dependencies array to be: [user._id]", {"range": "258", "text": "259"}, "Update the dependencies array to be: [user]", {"range": "260", "text": "261"}, [863, 865], "[user._id]", [1470, 1472], "[user]"]