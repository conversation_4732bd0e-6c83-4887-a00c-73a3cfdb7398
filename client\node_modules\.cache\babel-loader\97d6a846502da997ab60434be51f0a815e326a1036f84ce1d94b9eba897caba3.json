{"ast": null, "code": "'use client';\n\nexport { default } from './MenuList';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/MenuList/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './MenuList';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}