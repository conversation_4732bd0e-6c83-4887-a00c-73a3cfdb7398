{"ast": null, "code": "import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nexport default function withTheme() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? \"MUI: withTheme is no longer exported from @mui/material/styles.\\nYou have to import it from @mui/styles.\\nSee https://mui.com/r/migration-v4/#mui-material-styles for more details.\" : _formatMuiErrorMessage(16));\n}", "map": {"version": 3, "names": ["_formatMuiErrorMessage", "withTheme", "Error", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/styles/withTheme.js"], "sourcesContent": ["import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nexport default function withTheme() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: withTheme is no longer exported from @mui/material/styles.\nYou have to import it from @mui/styles.\nSee https://mui.com/r/migration-v4/#mui-material-styles for more details.` : _formatMuiErrorMessage(16));\n}"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,kCAAkC;AACrE,eAAe,SAASC,SAASA,CAAA,EAAG;EAClC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,2LAEsBL,sBAAsB,CAAC,EAAE,CAAC,CAAC;AACxG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}