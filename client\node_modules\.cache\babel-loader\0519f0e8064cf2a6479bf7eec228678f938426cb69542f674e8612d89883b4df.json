{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _Linkify = require('./components/Linkify');\nvar _Linkify2 = _interopRequireDefault(_Linkify);\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    default: obj\n  };\n}\nexports.default = _Linkify2.default;", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_Linkify", "require", "_Linkify2", "_interopRequireDefault", "obj", "__esModule", "default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/react-linkify/dist/index.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _Linkify = require('./components/Linkify');\n\nvar _Linkify2 = _interopRequireDefault(_Linkify);\n\nfunction _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }\n\nexports.default = _Linkify2.default;"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,IAAIC,QAAQ,GAAGC,OAAO,CAAC,sBAAsB,CAAC;AAE9C,IAAIC,SAAS,GAAGC,sBAAsB,CAACH,QAAQ,CAAC;AAEhD,SAASG,sBAAsBA,CAACC,GAAG,EAAE;EAAE,OAAOA,GAAG,IAAIA,GAAG,CAACC,UAAU,GAAGD,GAAG,GAAG;IAAEE,OAAO,EAAEF;EAAI,CAAC;AAAE;AAE9FN,OAAO,CAACQ,OAAO,GAAGJ,SAAS,CAACI,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}