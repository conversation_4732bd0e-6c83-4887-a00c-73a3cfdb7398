{"ast": null, "code": "'use client';\n\nexport { default } from './AvatarGroup';\nexport { default as avatarGroupClasses } from './avatarGroupClasses';\nexport * from './avatarGroupClasses';", "map": {"version": 3, "names": ["default", "avatarGroupClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/AvatarGroup/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './AvatarGroup';\nexport { default as avatarGroupClasses } from './avatarGroupClasses';\nexport * from './avatarGroupClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,eAAe;AACvC,SAASA,OAAO,IAAIC,kBAAkB,QAAQ,sBAAsB;AACpE,cAAc,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}