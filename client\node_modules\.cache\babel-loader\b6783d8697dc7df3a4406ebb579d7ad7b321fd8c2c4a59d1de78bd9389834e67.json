{"ast": null, "code": "import NoProfile from\"./userprofile.png\";import BgImage from\"./img.jpeg\";export{NoProfile,BgImage};", "map": {"version": 3, "names": ["NoProfile", "BgImage"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/assets/index.js"], "sourcesContent": ["import NoProfile from \"./userprofile.png\";\r\nimport BgImage from \"./img.jpeg\";\r\nexport { NoProfile, BgImage };\r\n"], "mappings": "AAAA,MAAO,CAAAA,SAAS,KAAM,mBAAmB,CACzC,MAAO,CAAAC,OAAO,KAAM,YAAY,CAChC,OAASD,SAAS,CAAEC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}