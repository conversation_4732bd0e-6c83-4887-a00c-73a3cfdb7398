{"ast": null, "code": "'use client';\n\n// do not remove the following import (https://github.com/microsoft/TypeScript/issues/29808#issuecomment-**********)\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// @ts-ignore\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { unstable_createCssVarsProvider as createCssVarsProvider } from '@mui/system';\nimport styleFunctionSx from '@mui/system/styleFunctionSx';\nimport experimental_extendTheme from './experimental_extendTheme';\nimport createTypography from './createTypography';\nimport excludeVariablesFromRoot from './excludeVariablesFromRoot';\nimport THEME_ID from './identifier';\nconst defaultTheme = experimental_extendTheme();\nconst {\n  CssVarsProvider,\n  useColorScheme,\n  getInitColorSchemeScript\n} = createCssVarsProvider({\n  themeId: THEME_ID,\n  theme: defaultTheme,\n  attribute: 'data-mui-color-scheme',\n  modeStorageKey: 'mui-mode',\n  colorSchemeStorageKey: 'mui-color-scheme',\n  defaultColorScheme: {\n    light: 'light',\n    dark: 'dark'\n  },\n  resolveTheme: theme => {\n    const newTheme = _extends({}, theme, {\n      typography: createTypography(theme.palette, theme.typography)\n    });\n    newTheme.unstable_sx = function sx(props) {\n      return styleFunctionSx({\n        sx: props,\n        theme: this\n      });\n    };\n    return newTheme;\n  },\n  excludeVariablesFromRoot\n});\nexport { useColorScheme, getInitColorSchemeScript, CssVarsProvider as Experimental_CssVarsProvider };", "map": {"version": 3, "names": ["_extends", "unstable_createCssVarsProvider", "createCssVarsProvider", "styleFunctionSx", "experimental_extendTheme", "createTypography", "excludeVariablesFromRoot", "THEME_ID", "defaultTheme", "CssVarsProvider", "useColorScheme", "getInitColorSchemeScript", "themeId", "theme", "attribute", "modeStorageKey", "colorSchemeStorageKey", "defaultColorScheme", "light", "dark", "resolveTheme", "newTheme", "typography", "palette", "unstable_sx", "sx", "props", "Experimental_CssVarsProvider"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/styles/CssVarsProvider.js"], "sourcesContent": ["'use client';\n\n// do not remove the following import (https://github.com/microsoft/TypeScript/issues/29808#issuecomment-**********)\n/* eslint-disable @typescript-eslint/no-unused-vars */\n// @ts-ignore\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { unstable_createCssVarsProvider as createCssVarsProvider } from '@mui/system';\nimport styleFunctionSx from '@mui/system/styleFunctionSx';\nimport experimental_extendTheme from './experimental_extendTheme';\nimport createTypography from './createTypography';\nimport excludeVariablesFromRoot from './excludeVariablesFromRoot';\nimport THEME_ID from './identifier';\nconst defaultTheme = experimental_extendTheme();\nconst {\n  CssVarsProvider,\n  useColorScheme,\n  getInitColorSchemeScript\n} = createCssVarsProvider({\n  themeId: THEME_ID,\n  theme: defaultTheme,\n  attribute: 'data-mui-color-scheme',\n  modeStorageKey: 'mui-mode',\n  colorSchemeStorageKey: 'mui-color-scheme',\n  defaultColorScheme: {\n    light: 'light',\n    dark: 'dark'\n  },\n  resolveTheme: theme => {\n    const newTheme = _extends({}, theme, {\n      typography: createTypography(theme.palette, theme.typography)\n    });\n    newTheme.unstable_sx = function sx(props) {\n      return styleFunctionSx({\n        sx: props,\n        theme: this\n      });\n    };\n    return newTheme;\n  },\n  excludeVariablesFromRoot\n});\nexport { useColorScheme, getInitColorSchemeScript, CssVarsProvider as Experimental_CssVarsProvider };"], "mappings": "AAAA,YAAY;;AAEZ;AACA;AACA;AACA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,8BAA8B,IAAIC,qBAAqB,QAAQ,aAAa;AACrF,OAAOC,eAAe,MAAM,6BAA6B;AACzD,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,QAAQ,MAAM,cAAc;AACnC,MAAMC,YAAY,GAAGJ,wBAAwB,CAAC,CAAC;AAC/C,MAAM;EACJK,eAAe;EACfC,cAAc;EACdC;AACF,CAAC,GAAGT,qBAAqB,CAAC;EACxBU,OAAO,EAAEL,QAAQ;EACjBM,KAAK,EAAEL,YAAY;EACnBM,SAAS,EAAE,uBAAuB;EAClCC,cAAc,EAAE,UAAU;EAC1BC,qBAAqB,EAAE,kBAAkB;EACzCC,kBAAkB,EAAE;IAClBC,KAAK,EAAE,OAAO;IACdC,IAAI,EAAE;EACR,CAAC;EACDC,YAAY,EAAEP,KAAK,IAAI;IACrB,MAAMQ,QAAQ,GAAGrB,QAAQ,CAAC,CAAC,CAAC,EAAEa,KAAK,EAAE;MACnCS,UAAU,EAAEjB,gBAAgB,CAACQ,KAAK,CAACU,OAAO,EAAEV,KAAK,CAACS,UAAU;IAC9D,CAAC,CAAC;IACFD,QAAQ,CAACG,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;MACxC,OAAOvB,eAAe,CAAC;QACrBsB,EAAE,EAAEC,KAAK;QACTb,KAAK,EAAE;MACT,CAAC,CAAC;IACJ,CAAC;IACD,OAAOQ,QAAQ;EACjB,CAAC;EACDf;AACF,CAAC,CAAC;AACF,SAASI,cAAc,EAAEC,wBAAwB,EAAEF,eAAe,IAAIkB,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}