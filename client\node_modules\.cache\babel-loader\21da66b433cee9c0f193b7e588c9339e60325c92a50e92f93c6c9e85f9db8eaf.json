{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"anchor\", \"classes\", \"className\", \"width\", \"style\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport capitalize from '../utils/capitalize';\nimport { isHorizontal } from '../Drawer/Drawer';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst SwipeAreaRoot = styled('div', {\n  shouldForwardProp: rootShouldForwardProp\n})(_ref => {\n  let {\n    theme,\n    ownerState\n  } = _ref;\n  return _extends({\n    position: 'fixed',\n    top: 0,\n    left: 0,\n    bottom: 0,\n    zIndex: theme.zIndex.drawer - 1\n  }, ownerState.anchor === 'left' && {\n    right: 'auto'\n  }, ownerState.anchor === 'right' && {\n    left: 'auto',\n    right: 0\n  }, ownerState.anchor === 'top' && {\n    bottom: 'auto',\n    right: 0\n  }, ownerState.anchor === 'bottom' && {\n    top: 'auto',\n    bottom: 0,\n    right: 0\n  });\n});\n\n/**\n * @ignore - internal component.\n */\nconst SwipeArea = /*#__PURE__*/React.forwardRef(function SwipeArea(props, ref) {\n  const {\n      anchor,\n      classes = {},\n      className,\n      width,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  return /*#__PURE__*/_jsx(SwipeAreaRoot, _extends({\n    className: clsx('PrivateSwipeArea-root', classes.root, classes[\"anchor\".concat(capitalize(anchor))], className),\n    ref: ref,\n    style: _extends({\n      [isHorizontal(anchor) ? 'width' : 'height']: width\n    }, style),\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeArea.propTypes = {\n  /**\n   * Side on which to attach the discovery area.\n   */\n  anchor: PropTypes.oneOf(['left', 'top', 'right', 'bottom']).isRequired,\n  /**\n   * @ignore\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` where the\n   * drawer can be swiped open from.\n   */\n  width: PropTypes.number.isRequired\n} : void 0;\nexport default SwipeArea;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "styled", "rootShouldForwardProp", "capitalize", "isHorizontal", "jsx", "_jsx", "SwipeAreaRoot", "shouldForwardProp", "_ref", "theme", "ownerState", "position", "top", "left", "bottom", "zIndex", "drawer", "anchor", "right", "SwipeArea", "forwardRef", "props", "ref", "classes", "className", "width", "style", "other", "root", "concat", "process", "env", "NODE_ENV", "propTypes", "oneOf", "isRequired", "object", "string", "number"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/SwipeableDrawer/SwipeArea.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"anchor\", \"classes\", \"className\", \"width\", \"style\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport styled, { rootShouldForwardProp } from '../styles/styled';\nimport capitalize from '../utils/capitalize';\nimport { isHorizontal } from '../Drawer/Drawer';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst SwipeAreaRoot = styled('div', {\n  shouldForwardProp: rootShouldForwardProp\n})(({\n  theme,\n  ownerState\n}) => _extends({\n  position: 'fixed',\n  top: 0,\n  left: 0,\n  bottom: 0,\n  zIndex: theme.zIndex.drawer - 1\n}, ownerState.anchor === 'left' && {\n  right: 'auto'\n}, ownerState.anchor === 'right' && {\n  left: 'auto',\n  right: 0\n}, ownerState.anchor === 'top' && {\n  bottom: 'auto',\n  right: 0\n}, ownerState.anchor === 'bottom' && {\n  top: 'auto',\n  bottom: 0,\n  right: 0\n}));\n\n/**\n * @ignore - internal component.\n */\nconst SwipeArea = /*#__PURE__*/React.forwardRef(function SwipeArea(props, ref) {\n  const {\n      anchor,\n      classes = {},\n      className,\n      width,\n      style\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = props;\n  return /*#__PURE__*/_jsx(SwipeAreaRoot, _extends({\n    className: clsx('PrivateSwipeArea-root', classes.root, classes[`anchor${capitalize(anchor)}`], className),\n    ref: ref,\n    style: _extends({\n      [isHorizontal(anchor) ? 'width' : 'height']: width\n    }, style),\n    ownerState: ownerState\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? SwipeArea.propTypes = {\n  /**\n   * Side on which to attach the discovery area.\n   */\n  anchor: PropTypes.oneOf(['left', 'top', 'right', 'bottom']).isRequired,\n  /**\n   * @ignore\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * @ignore\n   */\n  style: PropTypes.object,\n  /**\n   * The width of the left most (or right most) area in `px` where the\n   * drawer can be swiped open from.\n   */\n  width: PropTypes.number.isRequired\n} : void 0;\nexport default SwipeArea;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,OAAO,EAAE,OAAO,CAAC;AACtE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,MAAM,IAAIC,qBAAqB,QAAQ,kBAAkB;AAChE,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,aAAa,GAAGN,MAAM,CAAC,KAAK,EAAE;EAClCO,iBAAiB,EAAEN;AACrB,CAAC,CAAC,CAACO,IAAA;EAAA,IAAC;IACFC,KAAK;IACLC;EACF,CAAC,GAAAF,IAAA;EAAA,OAAKb,QAAQ,CAAC;IACbgB,QAAQ,EAAE,OAAO;IACjBC,GAAG,EAAE,CAAC;IACNC,IAAI,EAAE,CAAC;IACPC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAEN,KAAK,CAACM,MAAM,CAACC,MAAM,GAAG;EAChC,CAAC,EAAEN,UAAU,CAACO,MAAM,KAAK,MAAM,IAAI;IACjCC,KAAK,EAAE;EACT,CAAC,EAAER,UAAU,CAACO,MAAM,KAAK,OAAO,IAAI;IAClCJ,IAAI,EAAE,MAAM;IACZK,KAAK,EAAE;EACT,CAAC,EAAER,UAAU,CAACO,MAAM,KAAK,KAAK,IAAI;IAChCH,MAAM,EAAE,MAAM;IACdI,KAAK,EAAE;EACT,CAAC,EAAER,UAAU,CAACO,MAAM,KAAK,QAAQ,IAAI;IACnCL,GAAG,EAAE,MAAM;IACXE,MAAM,EAAE,CAAC;IACTI,KAAK,EAAE;EACT,CAAC,CAAC;AAAA,EAAC;;AAEH;AACA;AACA;AACA,MAAMC,SAAS,GAAG,aAAatB,KAAK,CAACuB,UAAU,CAAC,SAASD,SAASA,CAACE,KAAK,EAAEC,GAAG,EAAE;EAC7E,MAAM;MACFL,MAAM;MACNM,OAAO,GAAG,CAAC,CAAC;MACZC,SAAS;MACTC,KAAK;MACLC;IACF,CAAC,GAAGL,KAAK;IACTM,KAAK,GAAGjC,6BAA6B,CAAC2B,KAAK,EAAEzB,SAAS,CAAC;EACzD,MAAMc,UAAU,GAAGW,KAAK;EACxB,OAAO,aAAahB,IAAI,CAACC,aAAa,EAAEX,QAAQ,CAAC;IAC/C6B,SAAS,EAAEzB,IAAI,CAAC,uBAAuB,EAAEwB,OAAO,CAACK,IAAI,EAAEL,OAAO,UAAAM,MAAA,CAAU3B,UAAU,CAACe,MAAM,CAAC,EAAG,EAAEO,SAAS,CAAC;IACzGF,GAAG,EAAEA,GAAG;IACRI,KAAK,EAAE/B,QAAQ,CAAC;MACd,CAACQ,YAAY,CAACc,MAAM,CAAC,GAAG,OAAO,GAAG,QAAQ,GAAGQ;IAC/C,CAAC,EAAEC,KAAK,CAAC;IACThB,UAAU,EAAEA;EACd,CAAC,EAAEiB,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFG,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGb,SAAS,CAACc,SAAS,GAAG;EAC5D;AACF;AACA;EACEhB,MAAM,EAAEnB,SAAS,CAACoC,KAAK,CAAC,CAAC,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,QAAQ,CAAC,CAAC,CAACC,UAAU;EACtE;AACF;AACA;EACEZ,OAAO,EAAEzB,SAAS,CAACsC,MAAM;EACzB;AACF;AACA;EACEZ,SAAS,EAAE1B,SAAS,CAACuC,MAAM;EAC3B;AACF;AACA;EACEX,KAAK,EAAE5B,SAAS,CAACsC,MAAM;EACvB;AACF;AACA;AACA;EACEX,KAAK,EAAE3B,SAAS,CAACwC,MAAM,CAACH;AAC1B,CAAC,GAAG,KAAK,CAAC;AACV,eAAehB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}