{"ast": null, "code": "import{jsx as _jsx}from\"react/jsx-runtime\";function SkillProfilePills(_ref){let{skills}=_ref;return/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"div\",{className:\"flex flex-wrap gap-3 items-center\",children:skills.map(skill=>{return/*#__PURE__*/_jsx(\"div\",{style:{cursor:'pointer',background:\"#333\",color:\"#fff\",borderRadius:'20px',paddingLeft:'15px',paddingRight:'15px',paddingTop:'7.5px',paddingBottom:'7.5px'},children:skill},skill);})})});}export default SkillProfilePills;", "map": {"version": 3, "names": ["SkillProfilePills", "_ref", "skills", "_jsx", "children", "className", "map", "skill", "style", "cursor", "background", "color", "borderRadius", "paddingLeft", "paddingRight", "paddingTop", "paddingBottom"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/components/SkillProfilePills.jsx"], "sourcesContent": ["function SkillProfilePills({skills}){\r\n    return(\r\n        <div>\r\n            <div className=\"flex flex-wrap gap-3 items-center\">\r\n                {skills.map(skill=>{\r\n                    return(\r\n                        <div key={skill} style={{\r\n                            cursor: 'pointer',\r\n                            background: \"#333\",\r\n                            color: \"#fff\",\r\n                            borderRadius: '20px',\r\n                            paddingLeft: '15px',\r\n                            paddingRight: '15px',\r\n                            paddingTop: '7.5px',\r\n                            paddingBottom: '7.5px',\r\n                        }}>\r\n                            {skill}\r\n                        </div>\r\n                    )\r\n                })}\r\n            </div>\r\n        </div>\r\n    )\r\n}\r\nexport default SkillProfilePills;"], "mappings": "2CAAA,QAAS,CAAAA,iBAAiBA,CAAAC,IAAA,CAAU,IAAT,CAACC,MAAM,CAAC,CAAAD,IAAA,CAC/B,mBACIE,IAAA,QAAAC,QAAA,cACID,IAAA,QAAKE,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAC7CF,MAAM,CAACI,GAAG,CAACC,KAAK,EAAE,CACf,mBACIJ,IAAA,QAAiBK,KAAK,CAAE,CACpBC,MAAM,CAAE,SAAS,CACjBC,UAAU,CAAE,MAAM,CAClBC,KAAK,CAAE,MAAM,CACbC,YAAY,CAAE,MAAM,CACpBC,WAAW,CAAE,MAAM,CACnBC,YAAY,CAAE,MAAM,CACpBC,UAAU,CAAE,OAAO,CACnBC,aAAa,CAAE,OACnB,CAAE,CAAAZ,QAAA,CACGG,KAAK,EAVAA,KAWL,CAAC,CAEd,CAAC,CAAC,CACD,CAAC,CACL,CAAC,CAEd,CACA,cAAe,CAAAP,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}