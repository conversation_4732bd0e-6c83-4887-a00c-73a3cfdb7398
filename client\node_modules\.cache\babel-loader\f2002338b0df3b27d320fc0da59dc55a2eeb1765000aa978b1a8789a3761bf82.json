{"ast": null, "code": "import { globalStateClasses } from '@mui/utils/generateUtilityClass';\nconst GLOBAL_CLASS_PREFIX = 'base';\nfunction buildStateClass(state) {\n  return \"\".concat(GLOBAL_CLASS_PREFIX, \"--\").concat(state);\n}\nfunction buildSlotClass(componentName, slot) {\n  return \"\".concat(GLOBAL_CLASS_PREFIX, \"-\").concat(componentName, \"-\").concat(slot);\n}\nexport function generateUtilityClass(componentName, slot) {\n  const globalStateClass = globalStateClasses[slot];\n  return globalStateClass ? buildStateClass(globalStateClass) : buildSlotClass(componentName, slot);\n}\nexport function isGlobalState(slot) {\n  return globalStateClasses[slot] !== undefined;\n}", "map": {"version": 3, "names": ["globalStateClasses", "GLOBAL_CLASS_PREFIX", "buildStateClass", "state", "concat", "buildSlotClass", "componentName", "slot", "generateUtilityClass", "globalStateClass", "isGlobalState", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/generateUtilityClass/index.js"], "sourcesContent": ["import { globalStateClasses } from '@mui/utils/generateUtilityClass';\nconst GLOBAL_CLASS_PREFIX = 'base';\nfunction buildStateClass(state) {\n  return `${GLOBAL_CLASS_PREFIX}--${state}`;\n}\nfunction buildSlotClass(componentName, slot) {\n  return `${GLOBAL_CLASS_PREFIX}-${componentName}-${slot}`;\n}\nexport function generateUtilityClass(componentName, slot) {\n  const globalStateClass = globalStateClasses[slot];\n  return globalStateClass ? buildStateClass(globalStateClass) : buildSlotClass(componentName, slot);\n}\nexport function isGlobalState(slot) {\n  return globalStateClasses[slot] !== undefined;\n}"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,iCAAiC;AACpE,MAAMC,mBAAmB,GAAG,MAAM;AAClC,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,UAAAC,MAAA,CAAUH,mBAAmB,QAAAG,MAAA,CAAKD,KAAK;AACzC;AACA,SAASE,cAAcA,CAACC,aAAa,EAAEC,IAAI,EAAE;EAC3C,UAAAH,MAAA,CAAUH,mBAAmB,OAAAG,MAAA,CAAIE,aAAa,OAAAF,MAAA,CAAIG,IAAI;AACxD;AACA,OAAO,SAASC,oBAAoBA,CAACF,aAAa,EAAEC,IAAI,EAAE;EACxD,MAAME,gBAAgB,GAAGT,kBAAkB,CAACO,IAAI,CAAC;EACjD,OAAOE,gBAAgB,GAAGP,eAAe,CAACO,gBAAgB,CAAC,GAAGJ,cAAc,CAACC,aAAa,EAAEC,IAAI,CAAC;AACnG;AACA,OAAO,SAASG,aAAaA,CAACH,IAAI,EAAE;EAClC,OAAOP,kBAAkB,CAACO,IAAI,CAAC,KAAKI,SAAS;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}