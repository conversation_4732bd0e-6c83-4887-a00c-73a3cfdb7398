{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nconst _excluded = [\"breakpoints\", \"mixins\", \"spacing\", \"palette\", \"transitions\", \"typography\", \"shape\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport styleFunctionSx, { unstable_defaultSxConfig as defaultSxConfig } from '@mui/system/styleFunctionSx';\nimport systemCreateTheme from '@mui/system/createTheme';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport createMixins from './createMixins';\nimport createPalette from './createPalette';\nimport createTypography from './createTypography';\nimport shadows from './shadows';\nimport createTransitions from './createTransitions';\nimport zIndex from './zIndex';\nfunction createTheme() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  const {\n      mixins: mixinsInput = {},\n      palette: paletteInput = {},\n      transitions: transitionsInput = {},\n      typography: typographyInput = {}\n    } = options,\n    other = _objectWithoutPropertiesLoose(options, _excluded);\n  if (options.vars) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? \"MUI: `vars` is a private field used for CSS variables support.\\nPlease use another name.\" : _formatMuiErrorMessage(18));\n  }\n  const palette = createPalette(paletteInput);\n  const systemTheme = systemCreateTheme(options);\n  let muiTheme = deepmerge(systemTheme, {\n    mixins: createMixins(systemTheme.breakpoints, mixinsInput),\n    palette,\n    // Don't use [...shadows] until you've verified its transpiled code is not invoking the iterator protocol.\n    shadows: shadows.slice(),\n    typography: createTypography(palette, typographyInput),\n    transitions: createTransitions(transitionsInput),\n    zIndex: _extends({}, zIndex)\n  });\n  muiTheme = deepmerge(muiTheme, other);\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO v6: Refactor to use globalStateClassesMapping from @mui/utils once `readOnly` state class is used in Rating component.\n    const stateClasses = ['active', 'checked', 'completed', 'disabled', 'error', 'expanded', 'focused', 'focusVisible', 'required', 'selected'];\n    const traverse = (node, component) => {\n      let key;\n\n      // eslint-disable-next-line guard-for-in, no-restricted-syntax\n      for (key in node) {\n        const child = node[key];\n        if (stateClasses.indexOf(key) !== -1 && Object.keys(child).length > 0) {\n          if (process.env.NODE_ENV !== 'production') {\n            const stateClass = generateUtilityClass('', key);\n            console.error([\"MUI: The `\".concat(component, \"` component increases \") + \"the CSS specificity of the `\".concat(key, \"` internal state.\"), 'You can not override it like this: ', JSON.stringify(node, null, 2), '', \"Instead, you need to use the '&.\".concat(stateClass, \"' syntax:\"), JSON.stringify({\n              root: {\n                [\"&.\".concat(stateClass)]: child\n              }\n            }, null, 2), '', 'https://mui.com/r/state-classes-guide'].join('\\n'));\n          }\n          // Remove the style to prevent global conflicts.\n          node[key] = {};\n        }\n      }\n    };\n    Object.keys(muiTheme.components).forEach(component => {\n      const styleOverrides = muiTheme.components[component].styleOverrides;\n      if (styleOverrides && component.indexOf('Mui') === 0) {\n        traverse(styleOverrides, component);\n      }\n    });\n  }\n  muiTheme.unstable_sxConfig = _extends({}, defaultSxConfig, other == null ? void 0 : other.unstable_sxConfig);\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nlet warnedOnce = false;\nexport function createMuiTheme() {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnce) {\n      warnedOnce = true;\n      console.error(['MUI: the createMuiTheme function was renamed to createTheme.', '', \"You should use `import { createTheme } from '@mui/material/styles'`\"].join('\\n'));\n    }\n  }\n  return createTheme(...arguments);\n}\nexport default createTheme;", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_formatMuiErrorMessage", "_excluded", "deepmerge", "styleFunctionSx", "unstable_defaultSxConfig", "defaultSxConfig", "systemCreateTheme", "generateUtilityClass", "createMixins", "createPalette", "createTypography", "shadows", "createTransitions", "zIndex", "createTheme", "options", "arguments", "length", "undefined", "mixins", "mixinsInput", "palette", "paletteInput", "transitions", "transitionsInput", "typography", "typographyInput", "other", "vars", "Error", "process", "env", "NODE_ENV", "systemTheme", "muiTheme", "breakpoints", "slice", "_len", "args", "Array", "_key", "reduce", "acc", "argument", "stateClasses", "traverse", "node", "component", "key", "child", "indexOf", "Object", "keys", "stateClass", "console", "error", "concat", "JSON", "stringify", "root", "join", "components", "for<PERSON>ach", "styleOverrides", "unstable_sxConfig", "unstable_sx", "sx", "props", "theme", "warnedOnce", "createMuiTheme"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/styles/createTheme.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nconst _excluded = [\"breakpoints\", \"mixins\", \"spacing\", \"palette\", \"transitions\", \"typography\", \"shape\"];\nimport deepmerge from '@mui/utils/deepmerge';\nimport styleFunctionSx, { unstable_defaultSxConfig as defaultSxConfig } from '@mui/system/styleFunctionSx';\nimport systemCreateTheme from '@mui/system/createTheme';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nimport createMixins from './createMixins';\nimport createPalette from './createPalette';\nimport createTypography from './createTypography';\nimport shadows from './shadows';\nimport createTransitions from './createTransitions';\nimport zIndex from './zIndex';\nfunction createTheme(options = {}, ...args) {\n  const {\n      mixins: mixinsInput = {},\n      palette: paletteInput = {},\n      transitions: transitionsInput = {},\n      typography: typographyInput = {}\n    } = options,\n    other = _objectWithoutPropertiesLoose(options, _excluded);\n  if (options.vars) {\n    throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: \\`vars\\` is a private field used for CSS variables support.\nPlease use another name.` : _formatMuiErrorMessage(18));\n  }\n  const palette = createPalette(paletteInput);\n  const systemTheme = systemCreateTheme(options);\n  let muiTheme = deepmerge(systemTheme, {\n    mixins: createMixins(systemTheme.breakpoints, mixinsInput),\n    palette,\n    // Don't use [...shadows] until you've verified its transpiled code is not invoking the iterator protocol.\n    shadows: shadows.slice(),\n    typography: createTypography(palette, typographyInput),\n    transitions: createTransitions(transitionsInput),\n    zIndex: _extends({}, zIndex)\n  });\n  muiTheme = deepmerge(muiTheme, other);\n  muiTheme = args.reduce((acc, argument) => deepmerge(acc, argument), muiTheme);\n  if (process.env.NODE_ENV !== 'production') {\n    // TODO v6: Refactor to use globalStateClassesMapping from @mui/utils once `readOnly` state class is used in Rating component.\n    const stateClasses = ['active', 'checked', 'completed', 'disabled', 'error', 'expanded', 'focused', 'focusVisible', 'required', 'selected'];\n    const traverse = (node, component) => {\n      let key;\n\n      // eslint-disable-next-line guard-for-in, no-restricted-syntax\n      for (key in node) {\n        const child = node[key];\n        if (stateClasses.indexOf(key) !== -1 && Object.keys(child).length > 0) {\n          if (process.env.NODE_ENV !== 'production') {\n            const stateClass = generateUtilityClass('', key);\n            console.error([`MUI: The \\`${component}\\` component increases ` + `the CSS specificity of the \\`${key}\\` internal state.`, 'You can not override it like this: ', JSON.stringify(node, null, 2), '', `Instead, you need to use the '&.${stateClass}' syntax:`, JSON.stringify({\n              root: {\n                [`&.${stateClass}`]: child\n              }\n            }, null, 2), '', 'https://mui.com/r/state-classes-guide'].join('\\n'));\n          }\n          // Remove the style to prevent global conflicts.\n          node[key] = {};\n        }\n      }\n    };\n    Object.keys(muiTheme.components).forEach(component => {\n      const styleOverrides = muiTheme.components[component].styleOverrides;\n      if (styleOverrides && component.indexOf('Mui') === 0) {\n        traverse(styleOverrides, component);\n      }\n    });\n  }\n  muiTheme.unstable_sxConfig = _extends({}, defaultSxConfig, other == null ? void 0 : other.unstable_sxConfig);\n  muiTheme.unstable_sx = function sx(props) {\n    return styleFunctionSx({\n      sx: props,\n      theme: this\n    });\n  };\n  return muiTheme;\n}\nlet warnedOnce = false;\nexport function createMuiTheme(...args) {\n  if (process.env.NODE_ENV !== 'production') {\n    if (!warnedOnce) {\n      warnedOnce = true;\n      console.error(['MUI: the createMuiTheme function was renamed to createTheme.', '', \"You should use `import { createTheme } from '@mui/material/styles'`\"].join('\\n'));\n    }\n  }\n  return createTheme(...args);\n}\nexport default createTheme;"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,sBAAsB,MAAM,kCAAkC;AACrE,MAAMC,SAAS,GAAG,CAAC,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,EAAE,YAAY,EAAE,OAAO,CAAC;AACvG,OAAOC,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,eAAe,IAAIC,wBAAwB,IAAIC,eAAe,QAAQ,6BAA6B;AAC1G,OAAOC,iBAAiB,MAAM,yBAAyB;AACvD,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAOC,YAAY,MAAM,gBAAgB;AACzC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,gBAAgB,MAAM,oBAAoB;AACjD,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAOC,iBAAiB,MAAM,qBAAqB;AACnD,OAAOC,MAAM,MAAM,UAAU;AAC7B,SAASC,WAAWA,CAAA,EAAwB;EAAA,IAAvBC,OAAO,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAC/B,MAAM;MACFG,MAAM,EAAEC,WAAW,GAAG,CAAC,CAAC;MACxBC,OAAO,EAAEC,YAAY,GAAG,CAAC,CAAC;MAC1BC,WAAW,EAAEC,gBAAgB,GAAG,CAAC,CAAC;MAClCC,UAAU,EAAEC,eAAe,GAAG,CAAC;IACjC,CAAC,GAAGX,OAAO;IACXY,KAAK,GAAG5B,6BAA6B,CAACgB,OAAO,EAAEd,SAAS,CAAC;EAC3D,IAAIc,OAAO,CAACa,IAAI,EAAE;IAChB,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,gGAC7BhC,sBAAsB,CAAC,EAAE,CAAC,CAAC;EACrD;EACA,MAAMqB,OAAO,GAAGZ,aAAa,CAACa,YAAY,CAAC;EAC3C,MAAMW,WAAW,GAAG3B,iBAAiB,CAACS,OAAO,CAAC;EAC9C,IAAImB,QAAQ,GAAGhC,SAAS,CAAC+B,WAAW,EAAE;IACpCd,MAAM,EAAEX,YAAY,CAACyB,WAAW,CAACE,WAAW,EAAEf,WAAW,CAAC;IAC1DC,OAAO;IACP;IACAV,OAAO,EAAEA,OAAO,CAACyB,KAAK,CAAC,CAAC;IACxBX,UAAU,EAAEf,gBAAgB,CAACW,OAAO,EAAEK,eAAe,CAAC;IACtDH,WAAW,EAAEX,iBAAiB,CAACY,gBAAgB,CAAC;IAChDX,MAAM,EAAEf,QAAQ,CAAC,CAAC,CAAC,EAAEe,MAAM;EAC7B,CAAC,CAAC;EACFqB,QAAQ,GAAGhC,SAAS,CAACgC,QAAQ,EAAEP,KAAK,CAAC;EAAC,SAAAU,IAAA,GAAArB,SAAA,CAAAC,MAAA,EAvBFqB,IAAI,OAAAC,KAAA,CAAAF,IAAA,OAAAA,IAAA,WAAAG,IAAA,MAAAA,IAAA,GAAAH,IAAA,EAAAG,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAxB,SAAA,CAAAwB,IAAA;EAAA;EAwBxCN,QAAQ,GAAGI,IAAI,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,QAAQ,KAAKzC,SAAS,CAACwC,GAAG,EAAEC,QAAQ,CAAC,EAAET,QAAQ,CAAC;EAC7E,IAAIJ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,MAAMY,YAAY,GAAG,CAAC,QAAQ,EAAE,SAAS,EAAE,WAAW,EAAE,UAAU,EAAE,OAAO,EAAE,UAAU,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU,EAAE,UAAU,CAAC;IAC3I,MAAMC,QAAQ,GAAGA,CAACC,IAAI,EAAEC,SAAS,KAAK;MACpC,IAAIC,GAAG;;MAEP;MACA,KAAKA,GAAG,IAAIF,IAAI,EAAE;QAChB,MAAMG,KAAK,GAAGH,IAAI,CAACE,GAAG,CAAC;QACvB,IAAIJ,YAAY,CAACM,OAAO,CAACF,GAAG,CAAC,KAAK,CAAC,CAAC,IAAIG,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC,CAAChC,MAAM,GAAG,CAAC,EAAE;UACrE,IAAIa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;YACzC,MAAMqB,UAAU,GAAG9C,oBAAoB,CAAC,EAAE,EAAEyC,GAAG,CAAC;YAChDM,OAAO,CAACC,KAAK,CAAC,CAAC,aAAAC,MAAA,CAAcT,SAAS,6DAAAS,MAAA,CAA4DR,GAAG,sBAAoB,EAAE,qCAAqC,EAAES,IAAI,CAACC,SAAS,CAACZ,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,qCAAAU,MAAA,CAAqCH,UAAU,gBAAaI,IAAI,CAACC,SAAS,CAAC;cAC5QC,IAAI,EAAE;gBACJ,MAAAH,MAAA,CAAMH,UAAU,IAAKJ;cACvB;YACF,CAAC,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,uCAAuC,CAAC,CAACW,IAAI,CAAC,IAAI,CAAC,CAAC;UACvE;UACA;UACAd,IAAI,CAACE,GAAG,CAAC,GAAG,CAAC,CAAC;QAChB;MACF;IACF,CAAC;IACDG,MAAM,CAACC,IAAI,CAAClB,QAAQ,CAAC2B,UAAU,CAAC,CAACC,OAAO,CAACf,SAAS,IAAI;MACpD,MAAMgB,cAAc,GAAG7B,QAAQ,CAAC2B,UAAU,CAACd,SAAS,CAAC,CAACgB,cAAc;MACpE,IAAIA,cAAc,IAAIhB,SAAS,CAACG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,EAAE;QACpDL,QAAQ,CAACkB,cAAc,EAAEhB,SAAS,CAAC;MACrC;IACF,CAAC,CAAC;EACJ;EACAb,QAAQ,CAAC8B,iBAAiB,GAAGlE,QAAQ,CAAC,CAAC,CAAC,EAAEO,eAAe,EAAEsB,KAAK,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACqC,iBAAiB,CAAC;EAC5G9B,QAAQ,CAAC+B,WAAW,GAAG,SAASC,EAAEA,CAACC,KAAK,EAAE;IACxC,OAAOhE,eAAe,CAAC;MACrB+D,EAAE,EAAEC,KAAK;MACTC,KAAK,EAAE;IACT,CAAC,CAAC;EACJ,CAAC;EACD,OAAOlC,QAAQ;AACjB;AACA,IAAImC,UAAU,GAAG,KAAK;AACtB,OAAO,SAASC,cAAcA,CAAA,EAAU;EACtC,IAAIxC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC,IAAI,CAACqC,UAAU,EAAE;MACfA,UAAU,GAAG,IAAI;MACjBf,OAAO,CAACC,KAAK,CAAC,CAAC,8DAA8D,EAAE,EAAE,EAAE,qEAAqE,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,CAAC;IACvK;EACF;EACA,OAAO9C,WAAW,CAAC,GAAAE,SAAO,CAAC;AAC7B;AACA,eAAeF,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}