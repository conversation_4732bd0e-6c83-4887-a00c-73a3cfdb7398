{"ast": null, "code": "// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n  return getRandomValues(rnds8);\n}", "map": {"version": 3, "names": ["getRandomValues", "rnds8", "Uint8Array", "rng", "crypto", "bind", "Error"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/uuid/dist/esm-browser/rng.js"], "sourcesContent": ["// Unique ID creation requires a high quality random # generator. In the browser we therefore\n// require the crypto API and do not support built-in fallback to lower quality random number\n// generators (like Math.random()).\nlet getRandomValues;\nconst rnds8 = new Uint8Array(16);\nexport default function rng() {\n  // lazy load so that environments that need to polyfill have a chance to do so\n  if (!getRandomValues) {\n    // getRandomValues needs to be invoked in a context where \"this\" is a Crypto implementation.\n    getRandomValues = typeof crypto !== 'undefined' && crypto.getRandomValues && crypto.getRandomValues.bind(crypto);\n\n    if (!getRandomValues) {\n      throw new Error('crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported');\n    }\n  }\n\n  return getRandomValues(rnds8);\n}"], "mappings": "AAAA;AACA;AACA;AACA,IAAIA,eAAe;AACnB,MAAMC,KAAK,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;AAChC,eAAe,SAASC,GAAGA,CAAA,EAAG;EAC5B;EACA,IAAI,CAACH,eAAe,EAAE;IACpB;IACAA,eAAe,GAAG,OAAOI,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACJ,eAAe,IAAII,MAAM,CAACJ,eAAe,CAACK,IAAI,CAACD,MAAM,CAAC;IAEhH,IAAI,CAACJ,eAAe,EAAE;MACpB,MAAM,IAAIM,KAAK,CAAC,0GAA0G,CAAC;IAC7H;EACF;EAEA,OAAON,eAAe,CAACC,KAAK,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}