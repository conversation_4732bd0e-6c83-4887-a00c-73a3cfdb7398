{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { isHostComponent } from './isHostComponent';\n\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node or undefined, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nexport function appendOwnerState(elementType, otherProps, ownerState) {\n  if (elementType === undefined || isHostComponent(elementType)) {\n    return otherProps;\n  }\n  return _extends({}, otherProps, {\n    ownerState: _extends({}, otherProps.ownerState, ownerState)\n  });\n}", "map": {"version": 3, "names": ["_extends", "isHostComponent", "appendOwnerState", "elementType", "otherProps", "ownerState", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/utils/appendOwnerState.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { isHostComponent } from './isHostComponent';\n\n/**\n * Type of the ownerState based on the type of an element it applies to.\n * This resolves to the provided OwnerState for React components and `undefined` for host components.\n * Falls back to `OwnerState | undefined` when the exact type can't be determined in development time.\n */\n\n/**\n * Appends the ownerState object to the props, merging with the existing one if necessary.\n *\n * @param elementType Type of the element that owns the `existingProps`. If the element is a DOM node or undefined, `ownerState` is not applied.\n * @param otherProps Props of the element.\n * @param ownerState\n */\nexport function appendOwnerState(elementType, otherProps, ownerState) {\n  if (elementType === undefined || isHostComponent(elementType)) {\n    return otherProps;\n  }\n  return _extends({}, otherProps, {\n    ownerState: _extends({}, otherProps.ownerState, ownerState)\n  });\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,eAAe,QAAQ,mBAAmB;;AAEnD;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,gBAAgBA,CAACC,WAAW,EAAEC,UAAU,EAAEC,UAAU,EAAE;EACpE,IAAIF,WAAW,KAAKG,SAAS,IAAIL,eAAe,CAACE,WAAW,CAAC,EAAE;IAC7D,OAAOC,UAAU;EACnB;EACA,OAAOJ,QAAQ,CAAC,CAAC,CAAC,EAAEI,UAAU,EAAE;IAC9BC,UAAU,EAAEL,QAAQ,CAAC,CAAC,CAAC,EAAEI,UAAU,CAACC,UAAU,EAAEA,UAAU;EAC5D,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}