{"ast": null, "code": "'use client';\n\nexport { default } from './DialogTitle';\nexport { default as dialogTitleClasses } from './dialogTitleClasses';\nexport * from './dialogTitleClasses';", "map": {"version": 3, "names": ["default", "dialogTitleClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/DialogTitle/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './DialogTitle';\nexport { default as dialogTitleClasses } from './dialogTitleClasses';\nexport * from './dialogTitleClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,eAAe;AACvC,SAASA,OAAO,IAAIC,kBAAkB,QAAQ,sBAAsB;AACpE,cAAc,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}