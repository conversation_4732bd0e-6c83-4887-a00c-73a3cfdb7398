{"ast": null, "code": "export * from './ClickAwayListener';", "map": {"version": 3, "names": [], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/ClickAwayListener/index.js"], "sourcesContent": ["export * from './ClickAwayListener';"], "mappings": "AAAA,cAAc,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}