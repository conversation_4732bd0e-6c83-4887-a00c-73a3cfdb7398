{"ast": null, "code": "export { default } from './requirePropFactory';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/requirePropFactory/index.js"], "sourcesContent": ["export { default } from './requirePropFactory';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}