{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documents\\\\GitHub\\\\cluster-delta\\\\client\\\\src\\\\pages\\\\Home.jsx\",\n  _s = $RefreshSig$();\nimport { BACKEND_URL } from '../utils/api';\nimport React, { useState, useEffect } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { useSelector } from 'react-redux';\nimport axios from 'axios';\nimport { CustomButton, EditProfile, FriendsCard, Loading, PostCard, ProfileCard, TextInput, TopBar } from '../components';\nimport { suggest, requests, posts } from '../assets/data';\nimport { Link } from 'react-router-dom';\nimport { NoProfile } from '../assets';\nimport { BsFiletypeGif, BsPersonFillAdd } from 'react-icons/bs';\nimport { BiImages, BiSolidVideo } from 'react-icons/bi';\nimport { useForm } from 'react-hook-form';\nimport { SetPosts } from '../redux/postSlice';\nimport { IKUpload } from 'imagekitio-react';\nimport { imagekitConfig, getAuthenticationParameters } from '../config/imagekit';\nimport { v4 } from 'uuid';\nimport LocalFireDepartmentIcon from '@mui/icons-material/LocalFireDepartment';\nimport CampaignIcon from '@mui/icons-material/Campaign';\nimport ControlPointIcon from '@mui/icons-material/ControlPoint';\nimport AccountCircleIcon from '@mui/icons-material/AccountCircle';\nimport AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';\nimport welcome from '../assets/welcome.png';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nimport { Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst Home = () => {\n  _s();\n  var _user$profileUrl;\n  const {\n    user,\n    edit\n  } = useSelector(state => state.user);\n  const [friendRequest, setFriendRequest] = useState(requests);\n  const [suggestedFriends, setSuggestedFriends] = useState(suggest);\n  const [errMsg, setErrMsg] = useState('');\n  const [file, setFile] = useState(null);\n  const [posting, setPosting] = useState(false);\n  const [loading, setLoading] = useState(false);\n  const dispatch = useDispatch();\n  const [description, setDescription] = useState('');\n  const [showImage, setShowImage] = useState(true); //WELCOME IMAGE SHOW\n\n  const [image, setImage] = useState('');\n  const [postText, setPostText] = useState('');\n  const [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\n  const uid = user === null || user === void 0 ? void 0 : user._id;\n  useEffect(() => {\n    if (localStorage.getItem('wel') === '1') {\n      setShowImage(false);\n    } else {\n      setShowImage(true);\n      localStorage.setItem('wel', '1');\n    }\n  }, []);\n  useEffect(() => {\n    const handleResize = () => {\n      setIsMobile(window.innerWidth <= 768);\n    };\n    window.addEventListener('resize', handleResize);\n    return () => {\n      window.removeEventListener('resize', handleResize);\n    };\n  }, []);\n  const {\n    register,\n    handleSubmit,\n    formState: {\n      errors\n    }\n  } = useForm();\n  const handleFileChange = e => {\n    setImage(e.target.files[0]);\n    const selectedImage = e.target.files[0];\n    setImage(selectedImage);\n  };\n\n  // const handlePostSubmit = async (data) => {\n  //   try {\n  //     setErrMsg(\"\");\n  //     setPosting(true);\n\n  //     const formData = new FormData();\n  //     formData.append(\"description\", data.description);\n  //     formData.append(\"userId\", user._id);\n  //     console.log(user._id);\n  //     console.log(data.description);\n  //     if (file) {\n  //       formData.append(\"image\", file);\n  //     }\n\n  //     // Include user information in the request body\n  //     formData.append(\"userId\", user._id);\n\n  //     const response = await fetch(\"https://cluster-delta.onrender.com/posts/create-post\", {\n  //       method: \"POST\",\n  //       headers: {\n  //         \"Content-Type\": \"multipart/form-data\",\n  //       },\n  //       body: formData,\n  //     });\n\n  //     if (response.status === 201) {\n  //       const responseData = await response.json();\n  //       console.log(\"Post created successfully:\", responseData.data);\n  //     } else {\n  //       const errorData = await response.json();\n  //       console.error(\"Failed to create post:\", errorData.message);\n  //       setErrMsg({ message: errorData.message, status: \"failed\" });\n  //     }\n  //   } catch (error) {\n  //     console.error(\"An error occurred during post creation:\", error.message);\n  //     setErrMsg({\n  //       message: \"Error occurred during post creation\",\n  //       status: \"failed\",\n  //     });\n  //   } finally {\n  //     setPosting(false);\n  //   }\n  // };\n\n  const [fetchedPosts, setFetchedPosts] = useState([]);\n  useEffect(() => {\n    const fetchPosts = async () => {\n      try {\n        setLoading(true);\n\n        // Make a request to fetch posts\n        const response = await axios.post(\n        // \"https://cluster-delta.onrender.com/posts/\"\n        `${BACKEND_URL}/posts/`);\n        if (response.status === 200) {\n          setFetchedPosts(response.data.data);\n          console.log(response.data.data);\n          console.log(response.data);\n        } else {\n          console.error('Failed to fetch posts:', response.data.message);\n        }\n      } catch (error) {\n        console.error('An error occurred during post fetching:', error.message);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchPosts();\n  }, []);\n  const handlePostSubmit = async data => {\n    try {\n      setErrMsg('');\n      setPosting(true);\n\n      // If an image is selected, upload it to ImageKit first\n      let imageUrl = null;\n      if (image) {\n        try {\n          // Create FormData for the upload\n          const formData = new FormData();\n          formData.append('file', image);\n          formData.append('fileName', `post_${v4()}_${image.name}`);\n          formData.append('folder', '/posts');\n\n          // Upload to ImageKit via server endpoint\n          const uploadResponse = await fetch(`${BACKEND_URL}/imagekit/upload-image`, {\n            method: 'POST',\n            body: formData\n          });\n          if (!uploadResponse.ok) {\n            throw new Error('Upload failed');\n          }\n          const uploadResult = await uploadResponse.json();\n          imageUrl = uploadResult.secure_url;\n        } catch (uploadError) {\n          console.error('Error uploading image to ImageKit:', uploadError);\n          setErrMsg({\n            message: 'Image upload error occurred!',\n            status: 'failed'\n          });\n          return;\n        }\n      }\n\n      // Create post data including image URL\n      const postData = {\n        userId: user._id,\n        description: data.description,\n        image: imageUrl\n      };\n      console.log(postData);\n      console.log(imageUrl);\n\n      // Post data to your backend\n      const response = await axios.post(`${BACKEND_URL}/posts/create-post`,\n      // \"https://cluster-delta.onrender.com/posts/create-post\",\n      postData);\n      if (response.status === 201) {\n        setDescription('');\n        setErrMsg({\n          status: 'success',\n          message: 'Post created successfully!'\n        });\n        window.location.reload();\n\n        // Optionally reload the page or update state as needed\n      } else {\n        const errorData = response.data;\n        console.error('Failed to create post:', errorData.message);\n        setErrMsg({\n          message: errorData.message,\n          status: 'failed'\n        });\n      }\n    } catch (error) {\n      console.error('An error occurred during post creation:', error.message);\n      setErrMsg({\n        message: 'Error occurred during post creation',\n        status: 'failed'\n      });\n    } finally {\n      setPosting(false);\n      setImage(null); // Reset image state\n      setDescription('');\n    }\n  };\n\n  // Handle image input change\n\n  // const handlePostSubmit = async (data) => {\n  //   try {\n  //     setErrMsg(\"\");\n  //     setPosting(true);\n\n  //     const user = JSON.parse(localStorage.getItem(\"user\"));\n\n  //     const postData = {\n  //       userId: user._id,\n  //       description: data.description,\n  //     };\n\n  //     const response = await axios.post('https://cluster-delta.onrender.com/posts/create-post', postData);\n\n  //     if (response.status === 201) {\n  //       setDescription(\"\");\n  //       setPostText(\"\");\n  //       setErrMsg({\n  //         status: \"success\",\n  //         message: \"Post created successfully!\",\n  //       });\n  //       const responseData = response.data;\n  //       console.log(\"Post created successfully:\", responseData.data);\n  //       window.location.reload();\n\n  //     } else {\n  //       const errorData = response.data;\n  //       console.error(\"Failed to create post:\", errorData.message);\n  //       setErrMsg({ message: errorData.message, status: \"failed\" });\n  //     }\n  //   } catch (error) {\n  //     console.error(\"An error occurred during post creation:\", error.message);\n  //     setErrMsg({\n  //       message: \"Error occurred during post creation\",\n  //       status: \"failed\",\n  //     });\n  //   } finally {\n  //     setPosting(false);\n  //     setDescription(\"\");\n  //   }\n  // };\n\n  //GRADIENT FOR BOTTOM AI\n  const gradientStyle = {\n    fontSize: '25px',\n    background: 'linear-gradient(to right, violet, blue)',\n    WebkitBackgroundClip: 'text',\n    WebkitTextFillColor: 'transparent'\n  };\n  const handleClose = () => {\n    // Set the key in local storage to indicate that the image has been shown\n    localStorage.setItem('hasShownImage', 'true');\n    setShowImage(false);\n  };\n  return /*#__PURE__*/_jsxDEV(_Fragment, {\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"w-full px-0 lg:px-10 pb-20 2xl:px-40 bg-bgColor lg:rounded-lg h-screen overflow-hidden\",\n      children: [/*#__PURE__*/_jsxDEV(TopBar, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"mt-3 mr-3 ml-3\",\n        children: showImage && isMobile && /*#__PURE__*/_jsxDEV(\"img\", {\n          src: welcome,\n          style: {\n            borderRadius: '10px'\n          },\n          alt: \"\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 7\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 5\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"w-full flex gap-2 lg:gap-4 pt-5 pb-10 h-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden w-1/3 lg:w-1/4 h-full md:flex flex-col gap-6 overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(ProfileCard, {\n            user: user\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 319,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(FriendsCard, {\n            friends: user === null || user === void 0 ? void 0 : user.friends\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 320,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 318,\n          columnNumber: 6\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 h-full px-4 flex flex-col gap-6 overflow-y-auto rounded-lg\",\n          children: [/*#__PURE__*/_jsxDEV(\"form\", {\n            onSubmit: handleSubmit(handlePostSubmit),\n            className: \"bg-primary px-4 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full flex items-center gap-2 py-4 border-b border-[#********]\",\n              children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                src: (_user$profileUrl = user === null || user === void 0 ? void 0 : user.profileUrl) !== null && _user$profileUrl !== void 0 ? _user$profileUrl : NoProfile,\n                alt: \"User Image\",\n                className: \"w-14 h-14 rounded-full object-cover\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 330,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(TextInput, {\n                styles: \"w-full rounded-full py-5\",\n                placeholder: \"What's on your mind....\",\n                name: \"description\",\n                onChange: e => setDescription(e.target.value),\n                value: description,\n                register: register('description', {\n                  required: 'Write something about post'\n                }),\n                error: errors.description ? errors.description.message : ''\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 329,\n              columnNumber: 8\n            }, this), (errMsg === null || errMsg === void 0 ? void 0 : errMsg.message) && /*#__PURE__*/_jsxDEV(\"span\", {\n              role: \"alert\",\n              className: `text-sm ${(errMsg === null || errMsg === void 0 ? void 0 : errMsg.status) === 'failed' ? 'text-[#f64949fe]' : 'text-[#2ba150fe]'} mt-0.5`,\n              children: errMsg === null || errMsg === void 0 ? void 0 : errMsg.message\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 348,\n              columnNumber: 9\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between py-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"label\", {\n                htmlFor: \"imgUpload\",\n                className: \"flex items-center gap-1 text-base text-ascent-2 hover:text-ascent-1 cursor-pointer\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  onChange: e => handleFileChange(e),\n                  className: \"hidden\",\n                  id: \"imgUpload\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 365,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(BiImages, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 371,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Image\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 372,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"label\", {\n                className: \"flex items-center gap-1 text-base text-ascent-2 hover:text-ascent-1 cursor-pointer\",\n                htmlFor: \"videoUpload\",\n                children: [/*#__PURE__*/_jsxDEV(\"input\", {\n                  type: \"file\",\n                  \"data-max-size\": \"5120\",\n                  onChange: handleFileChange,\n                  className: \"hidden\",\n                  id: \"videoUpload\",\n                  accept: \".mp4, .wav\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 379,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(BiSolidVideo, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 387,\n                  columnNumber: 10\n                }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                  children: \"Video\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 388,\n                  columnNumber: 10\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 375,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                children: posting ? /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 393,\n                  columnNumber: 11\n                }, this) : /*#__PURE__*/_jsxDEV(CustomButton, {\n                  type: \"submit\",\n                  title: \"Post\",\n                  containerStyles: \"bg-[#0444a4] text-white py-1 px-6 rounded-full font-semibold text-sm\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 395,\n                  columnNumber: 11\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 391,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 360,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 325,\n            columnNumber: 7\n          }, this), loading ? /*#__PURE__*/_jsxDEV(Loading, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 406,\n            columnNumber: 8\n          }, this) : (fetchedPosts === null || fetchedPosts === void 0 ? void 0 : fetchedPosts.length) > 0 ? fetchedPosts.slice().reverse().map(post => /*#__PURE__*/_jsxDEV(PostCard, {\n            post: post,\n            user: user,\n            deletePost: () => {},\n            likePost: () => {}\n          }, post === null || post === void 0 ? void 0 : post._id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 10\n          }, this)) : /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex w-full h-full items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-lg text-ascent-2\",\n              children: \"No Post Available\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 422,\n              columnNumber: 9\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 324,\n          columnNumber: 6\n        }, this), isMobile && /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            height: '60px'\n          },\n          className: \"fixed bottom-0 left-0 w-full bg-primary p-4 flex justify-around items-center text-blue\",\n          children: [/*#__PURE__*/_jsxDEV(Link, {\n            to: \"/\",\n            className: \"text-xl flex flex-col items-center\",\n            style: {\n              fontSize: '15px',\n              color: 'grey'\n            },\n            children: [/*#__PURE__*/_jsxDEV(LocalFireDepartmentIcon, {\n              style: {\n                fontSize: '25px',\n                color: 'grey'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 438,\n              columnNumber: 9\n            }, this), \"Feed\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 433,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/ai\",\n            className: \"text-xl flex flex-col items-center\",\n            style: {\n              fontSize: '15px',\n              color: '#f5c000'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AutoAwesomeIcon, {\n              style: {\n                fontSize: '25px',\n                color: '#f5c000'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 449,\n              columnNumber: 9\n            }, this), \"Clu.ai\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 444,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: `/profile/${uid}`,\n            className: \"text-xl flex flex-col items-center\",\n            style: {\n              fontSize: '15px',\n              color: 'grey'\n            },\n            children: [/*#__PURE__*/_jsxDEV(AccountCircleIcon, {\n              style: {\n                fontSize: '25px',\n                color: 'grey'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 460,\n              columnNumber: 9\n            }, this), \"Profile\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 455,\n            columnNumber: 8\n          }, this), /*#__PURE__*/_jsxDEV(Link, {\n            to: \"/notifications\",\n            className: \"text-xl flex flex-col items-center\",\n            style: {\n              fontSize: '15px',\n              color: 'grey'\n            },\n            children: [/*#__PURE__*/_jsxDEV(CampaignIcon, {\n              style: {\n                fontSize: '25px',\n                color: 'grey'\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 471,\n              columnNumber: 9\n            }, this), \"Updates\"]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 8\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 428,\n          columnNumber: 7\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"hidden w-1/4 h-full lg:flex flex-col gap-8 overflow-y-auto\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-primary shadow-sm rounded-lg px-6 py-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-xl text-ascent-1 pb-2 border-b border-[#********]\",\n              children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                children: \" Friend Request\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 481,\n                columnNumber: 9\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                children: friendRequest === null || friendRequest === void 0 ? void 0 : friendRequest.length\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 482,\n                columnNumber: 9\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full flex flex-col gap-4 pt-4\",\n              children: friendRequest === null || friendRequest === void 0 ? void 0 : friendRequest.map(({\n                _id,\n                requestFrom: from\n              }) => {\n                var _from$profileUrl, _from$profession;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: '/profile/' + from._id,\n                    className: \"w-full flex gap-4 items-center cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: (_from$profileUrl = from === null || from === void 0 ? void 0 : from.profileUrl) !== null && _from$profileUrl !== void 0 ? _from$profileUrl : NoProfile,\n                      alt: from === null || from === void 0 ? void 0 : from.firstName,\n                      className: \"w-10 h-10 object-cover rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 492,\n                      columnNumber: 12\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1\",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-base font-medium text-ascent-1\",\n                        children: [from === null || from === void 0 ? void 0 : from.firstName, \" \", from === null || from === void 0 ? void 0 : from.lastName]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 498,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-ascent-2\",\n                        children: (_from$profession = from === null || from === void 0 ? void 0 : from.profession) !== null && _from$profession !== void 0 ? _from$profession : 'No Profession'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 501,\n                        columnNumber: 13\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 497,\n                      columnNumber: 12\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 488,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex gap-1\",\n                    children: [/*#__PURE__*/_jsxDEV(CustomButton, {\n                      title: \"Accept\",\n                      containerStyles: \"bg-[#0444a4] text-xs text-white px-1.5 py-1 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 508,\n                      columnNumber: 12\n                    }, this), /*#__PURE__*/_jsxDEV(CustomButton, {\n                      title: \"Deny\",\n                      containerStyles: \"border border-[#666] text-xs text-ascent-1 px-1.5 py-1 rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 512,\n                      columnNumber: 12\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 507,\n                    columnNumber: 11\n                  }, this)]\n                }, _id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 487,\n                  columnNumber: 10\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 485,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 7\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-full bg-primary shadow-sm rounded-lg px-5 py-5\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between text-lg text-ascent-1 border-b border-[#********]\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: \"Friend Suggestion\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 525,\n                columnNumber: 9\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 524,\n              columnNumber: 8\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-full flex flex-col gap-4 pt-4\",\n              children: suggestedFriends === null || suggestedFriends === void 0 ? void 0 : suggestedFriends.map(friend => {\n                var _friend$profileUrl, _friend$profession;\n                return /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-center justify-between\",\n                  children: [/*#__PURE__*/_jsxDEV(Link, {\n                    to: '/profile/' + (friend === null || friend === void 0 ? void 0 : friend._id),\n                    className: \"w-full flex gap-4 items-center cursor-pointer\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      src: (_friend$profileUrl = friend === null || friend === void 0 ? void 0 : friend.profileUrl) !== null && _friend$profileUrl !== void 0 ? _friend$profileUrl : NoProfile,\n                      alt: friend === null || friend === void 0 ? void 0 : friend.firstName,\n                      className: \"w-10 h-10 object-cover rounded-full\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 538,\n                      columnNumber: 12\n                    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"flex-1 \",\n                      children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                        className: \"text-base font-medium text-ascent-1\",\n                        children: [friend === null || friend === void 0 ? void 0 : friend.firstName, \" \", friend === null || friend === void 0 ? void 0 : friend.lastName]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 544,\n                        columnNumber: 13\n                      }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                        className: \"text-sm text-ascent-2\",\n                        children: (_friend$profession = friend === null || friend === void 0 ? void 0 : friend.profession) !== null && _friend$profession !== void 0 ? _friend$profession : 'No Profession'\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 547,\n                        columnNumber: 13\n                      }, this)]\n                    }, void 0, true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 543,\n                      columnNumber: 12\n                    }, this)]\n                  }, friend === null || friend === void 0 ? void 0 : friend._id, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 533,\n                    columnNumber: 11\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex gap-1\",\n                    children: /*#__PURE__*/_jsxDEV(\"button\", {\n                      className: \"bg-[#0444a430] text-sm text-white p-1 rounded\",\n                      onClick: () => {},\n                      children: /*#__PURE__*/_jsxDEV(BsPersonFillAdd, {\n                        size: 20,\n                        className: \"text-[#0f52b6]\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 558,\n                        columnNumber: 13\n                      }, this)\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 554,\n                      columnNumber: 12\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 553,\n                    columnNumber: 11\n                  }, this)]\n                }, friend._id, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 529,\n                  columnNumber: 10\n                }, this);\n              })\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 527,\n              columnNumber: 8\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 523,\n            columnNumber: 7\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 477,\n          columnNumber: 6\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 316,\n        columnNumber: 5\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 307,\n      columnNumber: 4\n    }, this), edit && /*#__PURE__*/_jsxDEV(EditProfile, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 569,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true);\n};\n_s(Home, \"CzbgIgjjpmFntetKmNQpTOXtFtw=\", false, function () {\n  return [useSelector, useDispatch, useForm];\n});\n_c = Home;\nexport default Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");", "map": {"version": 3, "names": ["BACKEND_URL", "React", "useState", "useEffect", "useDispatch", "useSelector", "axios", "CustomButton", "EditProfile", "FriendsCard", "Loading", "PostCard", "ProfileCard", "TextInput", "TopBar", "suggest", "requests", "posts", "Link", "NoProfile", "BsFiletypeGif", "BsPersonFillAdd", "BiImages", "BiSolidVideo", "useForm", "SetPosts", "IKUpload", "imagekitConfig", "getAuthenticationParameters", "v4", "LocalFireDepartmentIcon", "CampaignIcon", "ControlPointIcon", "AccountCircleIcon", "AutoAwesomeIcon", "welcome", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "Home", "_s", "_user$profileUrl", "user", "edit", "state", "friendRequest", "setFriendRequest", "suggestedFriends", "setSuggestedFriends", "errMsg", "setErrMsg", "file", "setFile", "posting", "setPosting", "loading", "setLoading", "dispatch", "description", "setDescription", "showImage", "setShowImage", "image", "setImage", "postText", "setPostText", "isMobile", "setIsMobile", "window", "innerWidth", "uid", "_id", "localStorage", "getItem", "setItem", "handleResize", "addEventListener", "removeEventListener", "register", "handleSubmit", "formState", "errors", "handleFileChange", "e", "target", "files", "selectedImage", "fetchedPosts", "setFetchedPosts", "fetchPosts", "response", "post", "status", "data", "console", "log", "error", "message", "handlePostSubmit", "imageUrl", "formData", "FormData", "append", "name", "uploadResponse", "fetch", "method", "body", "ok", "Error", "uploadResult", "json", "secure_url", "uploadError", "postData", "userId", "location", "reload", "errorData", "gradientStyle", "fontSize", "background", "WebkitBackgroundClip", "WebkitTextFillColor", "handleClose", "children", "className", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "src", "style", "borderRadius", "alt", "friends", "onSubmit", "profileUrl", "styles", "placeholder", "onChange", "value", "required", "role", "htmlFor", "type", "id", "accept", "title", "containerStyles", "length", "slice", "reverse", "map", "deletePost", "likePost", "height", "to", "color", "requestFrom", "from", "_from$profileUrl", "_from$profession", "firstName", "lastName", "profession", "friend", "_friend$profileUrl", "_friend$profession", "onClick", "size", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/pages/Home.jsx"], "sourcesContent": ["import { BACKEND_URL } from '../utils/api';\r\nimport React, { useState, useEffect } from 'react';\r\nimport { useDispatch } from 'react-redux';\r\nimport { useSelector } from 'react-redux';\r\nimport axios from 'axios';\r\nimport {\r\n\tCustomButton,\r\n\tEditProfile,\r\n\tFriendsCard,\r\n\tLoading,\r\n\tPostCard,\r\n\tProfileCard,\r\n\tTextInput,\r\n\tTopBar,\r\n} from '../components';\r\nimport { suggest, requests, posts } from '../assets/data';\r\nimport { Link } from 'react-router-dom';\r\nimport { NoProfile } from '../assets';\r\nimport { BsFiletypeGif, BsPersonFillAdd } from 'react-icons/bs';\r\nimport { BiImages, BiSolidVideo } from 'react-icons/bi';\r\nimport { useForm } from 'react-hook-form';\r\nimport { SetPosts } from '../redux/postSlice';\r\nimport { IKUpload } from 'imagekitio-react';\r\nimport {\r\n\timagekitConfig,\r\n\tgetAuthenticationParameters,\r\n} from '../config/imagekit';\r\nimport { v4 } from 'uuid';\r\nimport LocalFireDepartmentIcon from '@mui/icons-material/LocalFireDepartment';\r\nimport CampaignIcon from '@mui/icons-material/Campaign';\r\nimport ControlPointIcon from '@mui/icons-material/ControlPoint';\r\nimport AccountCircleIcon from '@mui/icons-material/AccountCircle';\r\nimport AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';\r\nimport welcome from '../assets/welcome.png';\r\n\r\nconst Home = () => {\r\n\tconst { user, edit } = useSelector((state) => state.user);\r\n\tconst [friendRequest, setFriendRequest] = useState(requests);\r\n\tconst [suggestedFriends, setSuggestedFriends] = useState(suggest);\r\n\tconst [errMsg, setErrMsg] = useState('');\r\n\tconst [file, setFile] = useState(null);\r\n\tconst [posting, setPosting] = useState(false);\r\n\tconst [loading, setLoading] = useState(false);\r\n\tconst dispatch = useDispatch();\r\n\tconst [description, setDescription] = useState('');\r\n\tconst [showImage, setShowImage] = useState(true); //WELCOME IMAGE SHOW\r\n\r\n\tconst [image, setImage] = useState('');\r\n\tconst [postText, setPostText] = useState('');\r\n\r\n\tconst [isMobile, setIsMobile] = useState(window.innerWidth <= 768);\r\n\r\n\tconst uid = user?._id;\r\n\r\n\tuseEffect(() => {\r\n\t\tif (localStorage.getItem('wel') === '1') {\r\n\t\t\tsetShowImage(false);\r\n\t\t} else {\r\n\t\t\tsetShowImage(true);\r\n\t\t\tlocalStorage.setItem('wel', '1');\r\n\t\t}\r\n\t}, []);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst handleResize = () => {\r\n\t\t\tsetIsMobile(window.innerWidth <= 768);\r\n\t\t};\r\n\r\n\t\twindow.addEventListener('resize', handleResize);\r\n\r\n\t\treturn () => {\r\n\t\t\twindow.removeEventListener('resize', handleResize);\r\n\t\t};\r\n\t}, []);\r\n\r\n\tconst {\r\n\t\tregister,\r\n\t\thandleSubmit,\r\n\t\tformState: { errors },\r\n\t} = useForm();\r\n\r\n\tconst handleFileChange = (e) => {\r\n\t\tsetImage(e.target.files[0]);\r\n\t\tconst selectedImage = e.target.files[0];\r\n\t\tsetImage(selectedImage);\r\n\t};\r\n\r\n\t// const handlePostSubmit = async (data) => {\r\n\t//   try {\r\n\t//     setErrMsg(\"\");\r\n\t//     setPosting(true);\r\n\r\n\t//     const formData = new FormData();\r\n\t//     formData.append(\"description\", data.description);\r\n\t//     formData.append(\"userId\", user._id);\r\n\t//     console.log(user._id);\r\n\t//     console.log(data.description);\r\n\t//     if (file) {\r\n\t//       formData.append(\"image\", file);\r\n\t//     }\r\n\r\n\t//     // Include user information in the request body\r\n\t//     formData.append(\"userId\", user._id);\r\n\r\n\t//     const response = await fetch(\"https://cluster-delta.onrender.com/posts/create-post\", {\r\n\t//       method: \"POST\",\r\n\t//       headers: {\r\n\t//         \"Content-Type\": \"multipart/form-data\",\r\n\t//       },\r\n\t//       body: formData,\r\n\t//     });\r\n\r\n\t//     if (response.status === 201) {\r\n\t//       const responseData = await response.json();\r\n\t//       console.log(\"Post created successfully:\", responseData.data);\r\n\t//     } else {\r\n\t//       const errorData = await response.json();\r\n\t//       console.error(\"Failed to create post:\", errorData.message);\r\n\t//       setErrMsg({ message: errorData.message, status: \"failed\" });\r\n\t//     }\r\n\t//   } catch (error) {\r\n\t//     console.error(\"An error occurred during post creation:\", error.message);\r\n\t//     setErrMsg({\r\n\t//       message: \"Error occurred during post creation\",\r\n\t//       status: \"failed\",\r\n\t//     });\r\n\t//   } finally {\r\n\t//     setPosting(false);\r\n\t//   }\r\n\t// };\r\n\r\n\tconst [fetchedPosts, setFetchedPosts] = useState([]);\r\n\r\n\tuseEffect(() => {\r\n\t\tconst fetchPosts = async () => {\r\n\t\t\ttry {\r\n\t\t\t\tsetLoading(true);\r\n\r\n\t\t\t\t// Make a request to fetch posts\r\n\t\t\t\tconst response = await axios.post(\r\n\t\t\t\t\t// \"https://cluster-delta.onrender.com/posts/\"\r\n\t\t\t\t\t`${BACKEND_URL}/posts/`\r\n\t\t\t\t);\r\n\r\n\t\t\t\tif (response.status === 200) {\r\n\t\t\t\t\tsetFetchedPosts(response.data.data);\r\n\t\t\t\t\tconsole.log(response.data.data);\r\n\t\t\t\t\tconsole.log(response.data);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tconsole.error('Failed to fetch posts:', response.data.message);\r\n\t\t\t\t}\r\n\t\t\t} catch (error) {\r\n\t\t\t\tconsole.error('An error occurred during post fetching:', error.message);\r\n\t\t\t} finally {\r\n\t\t\t\tsetLoading(false);\r\n\t\t\t}\r\n\t\t};\r\n\r\n\t\tfetchPosts();\r\n\t}, []);\r\n\r\n\tconst handlePostSubmit = async (data) => {\r\n\t\ttry {\r\n\t\t\tsetErrMsg('');\r\n\t\t\tsetPosting(true);\r\n\r\n\t\t\t// If an image is selected, upload it to ImageKit first\r\n\t\t\tlet imageUrl = null;\r\n\r\n\t\t\tif (image) {\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// Create FormData for the upload\r\n\t\t\t\t\tconst formData = new FormData();\r\n\t\t\t\t\tformData.append('file', image);\r\n\t\t\t\t\tformData.append('fileName', `post_${v4()}_${image.name}`);\r\n\t\t\t\t\tformData.append('folder', '/posts');\r\n\r\n\t\t\t\t\t// Upload to ImageKit via server endpoint\r\n\t\t\t\t\tconst uploadResponse = await fetch(\r\n\t\t\t\t\t\t`${BACKEND_URL}/imagekit/upload-image`,\r\n\t\t\t\t\t\t{\r\n\t\t\t\t\t\t\tmethod: 'POST',\r\n\t\t\t\t\t\t\tbody: formData,\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t);\r\n\r\n\t\t\t\t\tif (!uploadResponse.ok) {\r\n\t\t\t\t\t\tthrow new Error('Upload failed');\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\tconst uploadResult = await uploadResponse.json();\r\n\t\t\t\t\timageUrl = uploadResult.secure_url;\r\n\t\t\t\t} catch (uploadError) {\r\n\t\t\t\t\tconsole.error('Error uploading image to ImageKit:', uploadError);\r\n\t\t\t\t\tsetErrMsg({\r\n\t\t\t\t\t\tmessage: 'Image upload error occurred!',\r\n\t\t\t\t\t\tstatus: 'failed',\r\n\t\t\t\t\t});\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\r\n\t\t\t// Create post data including image URL\r\n\t\t\tconst postData = {\r\n\t\t\t\tuserId: user._id,\r\n\t\t\t\tdescription: data.description,\r\n\t\t\t\timage: imageUrl,\r\n\t\t\t};\r\n\t\t\tconsole.log(postData);\r\n\r\n\t\t\tconsole.log(imageUrl);\r\n\r\n\t\t\t// Post data to your backend\r\n\t\t\tconst response = await axios.post(\r\n\t\t\t\t`${BACKEND_URL}/posts/create-post`,\r\n\t\t\t\t// \"https://cluster-delta.onrender.com/posts/create-post\",\r\n\t\t\t\tpostData\r\n\t\t\t);\r\n\r\n\t\t\tif (response.status === 201) {\r\n\t\t\t\tsetDescription('');\r\n\t\t\t\tsetErrMsg({\r\n\t\t\t\t\tstatus: 'success',\r\n\t\t\t\t\tmessage: 'Post created successfully!',\r\n\t\t\t\t});\r\n\t\t\t\twindow.location.reload();\r\n\r\n\t\t\t\t// Optionally reload the page or update state as needed\r\n\t\t\t} else {\r\n\t\t\t\tconst errorData = response.data;\r\n\t\t\t\tconsole.error('Failed to create post:', errorData.message);\r\n\t\t\t\tsetErrMsg({ message: errorData.message, status: 'failed' });\r\n\t\t\t}\r\n\t\t} catch (error) {\r\n\t\t\tconsole.error('An error occurred during post creation:', error.message);\r\n\t\t\tsetErrMsg({\r\n\t\t\t\tmessage: 'Error occurred during post creation',\r\n\t\t\t\tstatus: 'failed',\r\n\t\t\t});\r\n\t\t} finally {\r\n\t\t\tsetPosting(false);\r\n\t\t\tsetImage(null); // Reset image state\r\n\t\t\tsetDescription('');\r\n\t\t}\r\n\t};\r\n\r\n\t// Handle image input change\r\n\r\n\t// const handlePostSubmit = async (data) => {\r\n\t//   try {\r\n\t//     setErrMsg(\"\");\r\n\t//     setPosting(true);\r\n\r\n\t//     const user = JSON.parse(localStorage.getItem(\"user\"));\r\n\r\n\t//     const postData = {\r\n\t//       userId: user._id,\r\n\t//       description: data.description,\r\n\t//     };\r\n\r\n\t//     const response = await axios.post('https://cluster-delta.onrender.com/posts/create-post', postData);\r\n\r\n\t//     if (response.status === 201) {\r\n\t//       setDescription(\"\");\r\n\t//       setPostText(\"\");\r\n\t//       setErrMsg({\r\n\t//         status: \"success\",\r\n\t//         message: \"Post created successfully!\",\r\n\t//       });\r\n\t//       const responseData = response.data;\r\n\t//       console.log(\"Post created successfully:\", responseData.data);\r\n\t//       window.location.reload();\r\n\r\n\t//     } else {\r\n\t//       const errorData = response.data;\r\n\t//       console.error(\"Failed to create post:\", errorData.message);\r\n\t//       setErrMsg({ message: errorData.message, status: \"failed\" });\r\n\t//     }\r\n\t//   } catch (error) {\r\n\t//     console.error(\"An error occurred during post creation:\", error.message);\r\n\t//     setErrMsg({\r\n\t//       message: \"Error occurred during post creation\",\r\n\t//       status: \"failed\",\r\n\t//     });\r\n\t//   } finally {\r\n\t//     setPosting(false);\r\n\t//     setDescription(\"\");\r\n\t//   }\r\n\t// };\r\n\r\n\t//GRADIENT FOR BOTTOM AI\r\n\tconst gradientStyle = {\r\n\t\tfontSize: '25px',\r\n\t\tbackground: 'linear-gradient(to right, violet, blue)',\r\n\t\tWebkitBackgroundClip: 'text',\r\n\t\tWebkitTextFillColor: 'transparent',\r\n\t};\r\n\r\n\tconst handleClose = () => {\r\n\t\t// Set the key in local storage to indicate that the image has been shown\r\n\t\tlocalStorage.setItem('hasShownImage', 'true');\r\n\t\tsetShowImage(false);\r\n\t};\r\n\r\n\treturn (\r\n\t\t<>\r\n\t\t\t<div className='w-full px-0 lg:px-10 pb-20 2xl:px-40 bg-bgColor lg:rounded-lg h-screen overflow-hidden'>\r\n\t\t\t\t<TopBar />\r\n\r\n\t\t\t\t<div className='mt-3 mr-3 ml-3'>\r\n\t\t\t\t\t{showImage && isMobile && (\r\n\t\t\t\t\t\t<img src={welcome} style={{ borderRadius: '10px' }} alt='' />\r\n\t\t\t\t\t)}\r\n\t\t\t\t</div>\r\n\r\n\t\t\t\t<div className='w-full flex gap-2 lg:gap-4 pt-5 pb-10 h-full'>\r\n\t\t\t\t\t{/* LEFT */}\r\n\t\t\t\t\t<div className='hidden w-1/3 lg:w-1/4 h-full md:flex flex-col gap-6 overflow-y-auto'>\r\n\t\t\t\t\t\t<ProfileCard user={user} />\r\n\t\t\t\t\t\t<FriendsCard friends={user?.friends} />\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t{/* CENTER */}\r\n\t\t\t\t\t<div className='flex-1 h-full px-4 flex flex-col gap-6 overflow-y-auto rounded-lg'>\r\n\t\t\t\t\t\t<form\r\n\t\t\t\t\t\t\tonSubmit={handleSubmit(handlePostSubmit)}\r\n\t\t\t\t\t\t\tclassName='bg-primary px-4 rounded-lg'\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<div className='w-full flex items-center gap-2 py-4 border-b border-[#********]'>\r\n\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\tsrc={user?.profileUrl ?? NoProfile}\r\n\t\t\t\t\t\t\t\t\talt='User Image'\r\n\t\t\t\t\t\t\t\t\tclassName='w-14 h-14 rounded-full object-cover'\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t<TextInput\r\n\t\t\t\t\t\t\t\t\tstyles='w-full rounded-full py-5'\r\n\t\t\t\t\t\t\t\t\tplaceholder=\"What's on your mind....\"\r\n\t\t\t\t\t\t\t\t\tname='description'\r\n\t\t\t\t\t\t\t\t\tonChange={(e) => setDescription(e.target.value)}\r\n\t\t\t\t\t\t\t\t\tvalue={description}\r\n\t\t\t\t\t\t\t\t\tregister={register('description', {\r\n\t\t\t\t\t\t\t\t\t\trequired: 'Write something about post',\r\n\t\t\t\t\t\t\t\t\t})}\r\n\t\t\t\t\t\t\t\t\terror={errors.description ? errors.description.message : ''}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t{errMsg?.message && (\r\n\t\t\t\t\t\t\t\t<span\r\n\t\t\t\t\t\t\t\t\trole='alert'\r\n\t\t\t\t\t\t\t\t\tclassName={`text-sm ${\r\n\t\t\t\t\t\t\t\t\t\terrMsg?.status === 'failed'\r\n\t\t\t\t\t\t\t\t\t\t\t? 'text-[#f64949fe]'\r\n\t\t\t\t\t\t\t\t\t\t\t: 'text-[#2ba150fe]'\r\n\t\t\t\t\t\t\t\t\t} mt-0.5`}\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t{errMsg?.message}\r\n\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t)}\r\n\r\n\t\t\t\t\t\t\t<div className='flex items-center justify-between py-4'>\r\n\t\t\t\t\t\t\t\t<label\r\n\t\t\t\t\t\t\t\t\thtmlFor='imgUpload'\r\n\t\t\t\t\t\t\t\t\tclassName='flex items-center gap-1 text-base text-ascent-2 hover:text-ascent-1 cursor-pointer'\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype='file'\r\n\t\t\t\t\t\t\t\t\t\tonChange={(e) => handleFileChange(e)}\r\n\t\t\t\t\t\t\t\t\t\tclassName='hidden'\r\n\t\t\t\t\t\t\t\t\t\tid='imgUpload'\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t<BiImages />\r\n\t\t\t\t\t\t\t\t\t<span>Image</span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\r\n\t\t\t\t\t\t\t\t<label\r\n\t\t\t\t\t\t\t\t\tclassName='flex items-center gap-1 text-base text-ascent-2 hover:text-ascent-1 cursor-pointer'\r\n\t\t\t\t\t\t\t\t\thtmlFor='videoUpload'\r\n\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t<input\r\n\t\t\t\t\t\t\t\t\t\ttype='file'\r\n\t\t\t\t\t\t\t\t\t\tdata-max-size='5120'\r\n\t\t\t\t\t\t\t\t\t\tonChange={handleFileChange}\r\n\t\t\t\t\t\t\t\t\t\tclassName='hidden'\r\n\t\t\t\t\t\t\t\t\t\tid='videoUpload'\r\n\t\t\t\t\t\t\t\t\t\taccept='.mp4, .wav'\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t<BiSolidVideo />\r\n\t\t\t\t\t\t\t\t\t<span>Video</span>\r\n\t\t\t\t\t\t\t\t</label>\r\n\r\n\t\t\t\t\t\t\t\t<div>\r\n\t\t\t\t\t\t\t\t\t{posting ? (\r\n\t\t\t\t\t\t\t\t\t\t<Loading />\r\n\t\t\t\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t\t\t\t<CustomButton\r\n\t\t\t\t\t\t\t\t\t\t\ttype='submit'\r\n\t\t\t\t\t\t\t\t\t\t\ttitle='Post'\r\n\t\t\t\t\t\t\t\t\t\t\tcontainerStyles='bg-[#0444a4] text-white py-1 px-6 rounded-full font-semibold text-sm'\r\n\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t)}\r\n\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</form>\r\n\r\n\t\t\t\t\t\t{loading ? (\r\n\t\t\t\t\t\t\t<Loading />\r\n\t\t\t\t\t\t) : fetchedPosts?.length > 0 ? (\r\n\t\t\t\t\t\t\tfetchedPosts\r\n\t\t\t\t\t\t\t\t.slice()\r\n\t\t\t\t\t\t\t\t.reverse()\r\n\t\t\t\t\t\t\t\t.map((post) => (\r\n\t\t\t\t\t\t\t\t\t<PostCard\r\n\t\t\t\t\t\t\t\t\t\tkey={post?._id}\r\n\t\t\t\t\t\t\t\t\t\tpost={post}\r\n\t\t\t\t\t\t\t\t\t\tuser={user}\r\n\t\t\t\t\t\t\t\t\t\tdeletePost={() => {}}\r\n\t\t\t\t\t\t\t\t\t\tlikePost={() => {}}\r\n\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t))\r\n\t\t\t\t\t\t) : (\r\n\t\t\t\t\t\t\t<div className='flex w-full h-full items-center justify-center'>\r\n\t\t\t\t\t\t\t\t<p className='text-lg text-ascent-2'>No Post Available</p>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t)}\r\n\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t{isMobile && (\r\n\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\tstyle={{ height: '60px' }}\r\n\t\t\t\t\t\t\tclassName='fixed bottom-0 left-0 w-full bg-primary p-4 flex justify-around items-center text-blue'\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t{/* Update your bottom navigation items with icons */}\r\n\t\t\t\t\t\t\t<Link\r\n\t\t\t\t\t\t\t\tto='/'\r\n\t\t\t\t\t\t\t\tclassName='text-xl flex flex-col items-center'\r\n\t\t\t\t\t\t\t\tstyle={{ fontSize: '15px', color: 'grey' }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<LocalFireDepartmentIcon\r\n\t\t\t\t\t\t\t\t\tstyle={{ fontSize: '25px', color: 'grey' }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\tFeed\r\n\t\t\t\t\t\t\t</Link>\r\n\r\n\t\t\t\t\t\t\t<Link\r\n\t\t\t\t\t\t\t\tto='/ai'\r\n\t\t\t\t\t\t\t\tclassName='text-xl flex flex-col items-center'\r\n\t\t\t\t\t\t\t\tstyle={{ fontSize: '15px', color: '#f5c000' }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<AutoAwesomeIcon\r\n\t\t\t\t\t\t\t\t\tstyle={{ fontSize: '25px', color: '#f5c000' }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\tClu.ai\r\n\t\t\t\t\t\t\t</Link>\r\n\r\n\t\t\t\t\t\t\t<Link\r\n\t\t\t\t\t\t\t\tto={`/profile/${uid}`}\r\n\t\t\t\t\t\t\t\tclassName='text-xl flex flex-col items-center'\r\n\t\t\t\t\t\t\t\tstyle={{ fontSize: '15px', color: 'grey' }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<AccountCircleIcon\r\n\t\t\t\t\t\t\t\t\tstyle={{ fontSize: '25px', color: 'grey' }}\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\tProfile\r\n\t\t\t\t\t\t\t</Link>\r\n\r\n\t\t\t\t\t\t\t<Link\r\n\t\t\t\t\t\t\t\tto='/notifications'\r\n\t\t\t\t\t\t\t\tclassName='text-xl flex flex-col items-center'\r\n\t\t\t\t\t\t\t\tstyle={{ fontSize: '15px', color: 'grey' }}\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<CampaignIcon style={{ fontSize: '25px', color: 'grey' }} />\r\n\t\t\t\t\t\t\t\tUpdates\r\n\t\t\t\t\t\t\t</Link>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t)}\r\n\t\t\t\t\t{/* RIGHT */}\r\n\t\t\t\t\t<div className='hidden w-1/4 h-full lg:flex flex-col gap-8 overflow-y-auto'>\r\n\t\t\t\t\t\t{/* FRIEND REQUEST */}\r\n\t\t\t\t\t\t<div className='w-full bg-primary shadow-sm rounded-lg px-6 py-5'>\r\n\t\t\t\t\t\t\t<div className='flex items-center justify-between text-xl text-ascent-1 pb-2 border-b border-[#********]'>\r\n\t\t\t\t\t\t\t\t<span> Friend Request</span>\r\n\t\t\t\t\t\t\t\t<span>{friendRequest?.length}</span>\r\n\t\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t\t<div className='w-full flex flex-col gap-4 pt-4'>\r\n\t\t\t\t\t\t\t\t{friendRequest?.map(({ _id, requestFrom: from }) => (\r\n\t\t\t\t\t\t\t\t\t<div key={_id} className='flex items-center justify-between'>\r\n\t\t\t\t\t\t\t\t\t\t<Link\r\n\t\t\t\t\t\t\t\t\t\t\tto={'/profile/' + from._id}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName='w-full flex gap-4 items-center cursor-pointer'\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\tsrc={from?.profileUrl ?? NoProfile}\r\n\t\t\t\t\t\t\t\t\t\t\t\talt={from?.firstName}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName='w-10 h-10 object-cover rounded-full'\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<div className='flex-1'>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<p className='text-base font-medium text-ascent-1'>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{from?.firstName} {from?.lastName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span className='text-sm text-ascent-2'>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{from?.profession ?? 'No Profession'}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</Link>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<div className='flex gap-1'>\r\n\t\t\t\t\t\t\t\t\t\t\t<CustomButton\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle='Accept'\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontainerStyles='bg-[#0444a4] text-xs text-white px-1.5 py-1 rounded-full'\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<CustomButton\r\n\t\t\t\t\t\t\t\t\t\t\t\ttitle='Deny'\r\n\t\t\t\t\t\t\t\t\t\t\t\tcontainerStyles='border border-[#666] text-xs text-ascent-1 px-1.5 py-1 rounded-full'\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\r\n\t\t\t\t\t\t{/* SUGGESTED FRIENDS */}\r\n\t\t\t\t\t\t<div className='w-full bg-primary shadow-sm rounded-lg px-5 py-5'>\r\n\t\t\t\t\t\t\t<div className='flex items-center justify-between text-lg text-ascent-1 border-b border-[#********]'>\r\n\t\t\t\t\t\t\t\t<span>Friend Suggestion</span>\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t<div className='w-full flex flex-col gap-4 pt-4'>\r\n\t\t\t\t\t\t\t\t{suggestedFriends?.map((friend) => (\r\n\t\t\t\t\t\t\t\t\t<div\r\n\t\t\t\t\t\t\t\t\t\tclassName='flex items-center justify-between'\r\n\t\t\t\t\t\t\t\t\t\tkey={friend._id}\r\n\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t<Link\r\n\t\t\t\t\t\t\t\t\t\t\tto={'/profile/' + friend?._id}\r\n\t\t\t\t\t\t\t\t\t\t\tkey={friend?._id}\r\n\t\t\t\t\t\t\t\t\t\t\tclassName='w-full flex gap-4 items-center cursor-pointer'\r\n\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t<img\r\n\t\t\t\t\t\t\t\t\t\t\t\tsrc={friend?.profileUrl ?? NoProfile}\r\n\t\t\t\t\t\t\t\t\t\t\t\talt={friend?.firstName}\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName='w-10 h-10 object-cover rounded-full'\r\n\t\t\t\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t\t\t\t\t<div className='flex-1 '>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<p className='text-base font-medium text-ascent-1'>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{friend?.firstName} {friend?.lastName}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</p>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<span className='text-sm text-ascent-2'>\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t{friend?.profession ?? 'No Profession'}\r\n\t\t\t\t\t\t\t\t\t\t\t\t</span>\r\n\t\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t\t</Link>\r\n\r\n\t\t\t\t\t\t\t\t\t\t<div className='flex gap-1'>\r\n\t\t\t\t\t\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t\t\t\t\t\t\tclassName='bg-[#0444a430] text-sm text-white p-1 rounded'\r\n\t\t\t\t\t\t\t\t\t\t\t\tonClick={() => {}}\r\n\t\t\t\t\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t\t\t\t\t<BsPersonFillAdd size={20} className='text-[#0f52b6]' />\r\n\t\t\t\t\t\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t\t\t))}\r\n\t\t\t\t\t\t\t</div>\r\n\t\t\t\t\t\t</div>\r\n\t\t\t\t\t</div>\r\n\t\t\t\t</div>\r\n\t\t\t</div>\r\n\r\n\t\t\t{edit && <EditProfile />}\r\n\t\t</>\r\n\t);\r\n};\r\n\r\nexport default Home;\r\n"], "mappings": ";;AAAA,SAASA,WAAW,QAAQ,cAAc;AAC1C,OAAOC,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,aAAa;AACzC,SAASC,WAAW,QAAQ,aAAa;AACzC,OAAOC,KAAK,MAAM,OAAO;AACzB,SACCC,YAAY,EACZC,WAAW,EACXC,WAAW,EACXC,OAAO,EACPC,QAAQ,EACRC,WAAW,EACXC,SAAS,EACTC,MAAM,QACA,eAAe;AACtB,SAASC,OAAO,EAAEC,QAAQ,EAAEC,KAAK,QAAQ,gBAAgB;AACzD,SAASC,IAAI,QAAQ,kBAAkB;AACvC,SAASC,SAAS,QAAQ,WAAW;AACrC,SAASC,aAAa,EAAEC,eAAe,QAAQ,gBAAgB;AAC/D,SAASC,QAAQ,EAAEC,YAAY,QAAQ,gBAAgB;AACvD,SAASC,OAAO,QAAQ,iBAAiB;AACzC,SAASC,QAAQ,QAAQ,oBAAoB;AAC7C,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SACCC,cAAc,EACdC,2BAA2B,QACrB,oBAAoB;AAC3B,SAASC,EAAE,QAAQ,MAAM;AACzB,OAAOC,uBAAuB,MAAM,yCAAyC;AAC7E,OAAOC,YAAY,MAAM,8BAA8B;AACvD,OAAOC,gBAAgB,MAAM,kCAAkC;AAC/D,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,OAAO,MAAM,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAAA,SAAAC,QAAA,IAAAC,SAAA;AAE5C,MAAMC,IAAI,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAAA,IAAAC,gBAAA;EAClB,MAAM;IAAEC,IAAI;IAAEC;EAAK,CAAC,GAAGvC,WAAW,CAAEwC,KAAK,IAAKA,KAAK,CAACF,IAAI,CAAC;EACzD,MAAM,CAACG,aAAa,EAAEC,gBAAgB,CAAC,GAAG7C,QAAQ,CAACc,QAAQ,CAAC;EAC5D,MAAM,CAACgC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG/C,QAAQ,CAACa,OAAO,CAAC;EACjE,MAAM,CAACmC,MAAM,EAAEC,SAAS,CAAC,GAAGjD,QAAQ,CAAC,EAAE,CAAC;EACxC,MAAM,CAACkD,IAAI,EAAEC,OAAO,CAAC,GAAGnD,QAAQ,CAAC,IAAI,CAAC;EACtC,MAAM,CAACoD,OAAO,EAAEC,UAAU,CAAC,GAAGrD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAM,CAACsD,OAAO,EAAEC,UAAU,CAAC,GAAGvD,QAAQ,CAAC,KAAK,CAAC;EAC7C,MAAMwD,QAAQ,GAAGtD,WAAW,CAAC,CAAC;EAC9B,MAAM,CAACuD,WAAW,EAAEC,cAAc,CAAC,GAAG1D,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAAC2D,SAAS,EAAEC,YAAY,CAAC,GAAG5D,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;;EAElD,MAAM,CAAC6D,KAAK,EAAEC,QAAQ,CAAC,GAAG9D,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAM,CAAC+D,QAAQ,EAAEC,WAAW,CAAC,GAAGhE,QAAQ,CAAC,EAAE,CAAC;EAE5C,MAAM,CAACiE,QAAQ,EAAEC,WAAW,CAAC,GAAGlE,QAAQ,CAACmE,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;EAElE,MAAMC,GAAG,GAAG5B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,GAAG;EAErBrE,SAAS,CAAC,MAAM;IACf,IAAIsE,YAAY,CAACC,OAAO,CAAC,KAAK,CAAC,KAAK,GAAG,EAAE;MACxCZ,YAAY,CAAC,KAAK,CAAC;IACpB,CAAC,MAAM;MACNA,YAAY,CAAC,IAAI,CAAC;MAClBW,YAAY,CAACE,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC;IACjC;EACD,CAAC,EAAE,EAAE,CAAC;EAENxE,SAAS,CAAC,MAAM;IACf,MAAMyE,YAAY,GAAGA,CAAA,KAAM;MAC1BR,WAAW,CAACC,MAAM,CAACC,UAAU,IAAI,GAAG,CAAC;IACtC,CAAC;IAEDD,MAAM,CAACQ,gBAAgB,CAAC,QAAQ,EAAED,YAAY,CAAC;IAE/C,OAAO,MAAM;MACZP,MAAM,CAACS,mBAAmB,CAAC,QAAQ,EAAEF,YAAY,CAAC;IACnD,CAAC;EACF,CAAC,EAAE,EAAE,CAAC;EAEN,MAAM;IACLG,QAAQ;IACRC,YAAY;IACZC,SAAS,EAAE;MAAEC;IAAO;EACrB,CAAC,GAAG1D,OAAO,CAAC,CAAC;EAEb,MAAM2D,gBAAgB,GAAIC,CAAC,IAAK;IAC/BpB,QAAQ,CAACoB,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC,CAAC;IAC3B,MAAMC,aAAa,GAAGH,CAAC,CAACC,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;IACvCtB,QAAQ,CAACuB,aAAa,CAAC;EACxB,CAAC;;EAED;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGvF,QAAQ,CAAC,EAAE,CAAC;EAEpDC,SAAS,CAAC,MAAM;IACf,MAAMuF,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC9B,IAAI;QACHjC,UAAU,CAAC,IAAI,CAAC;;QAEhB;QACA,MAAMkC,QAAQ,GAAG,MAAMrF,KAAK,CAACsF,IAAI;QAChC;QACC,GAAE5F,WAAY,SAChB,CAAC;QAED,IAAI2F,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;UAC5BJ,eAAe,CAACE,QAAQ,CAACG,IAAI,CAACA,IAAI,CAAC;UACnCC,OAAO,CAACC,GAAG,CAACL,QAAQ,CAACG,IAAI,CAACA,IAAI,CAAC;UAC/BC,OAAO,CAACC,GAAG,CAACL,QAAQ,CAACG,IAAI,CAAC;QAC3B,CAAC,MAAM;UACNC,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEN,QAAQ,CAACG,IAAI,CAACI,OAAO,CAAC;QAC/D;MACD,CAAC,CAAC,OAAOD,KAAK,EAAE;QACfF,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAACC,OAAO,CAAC;MACxE,CAAC,SAAS;QACTzC,UAAU,CAAC,KAAK,CAAC;MAClB;IACD,CAAC;IAEDiC,UAAU,CAAC,CAAC;EACb,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMS,gBAAgB,GAAG,MAAOL,IAAI,IAAK;IACxC,IAAI;MACH3C,SAAS,CAAC,EAAE,CAAC;MACbI,UAAU,CAAC,IAAI,CAAC;;MAEhB;MACA,IAAI6C,QAAQ,GAAG,IAAI;MAEnB,IAAIrC,KAAK,EAAE;QACV,IAAI;UACH;UACA,MAAMsC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;UAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAExC,KAAK,CAAC;UAC9BsC,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAG,QAAO1E,EAAE,CAAC,CAAE,IAAGkC,KAAK,CAACyC,IAAK,EAAC,CAAC;UACzDH,QAAQ,CAACE,MAAM,CAAC,QAAQ,EAAE,QAAQ,CAAC;;UAEnC;UACA,MAAME,cAAc,GAAG,MAAMC,KAAK,CAChC,GAAE1G,WAAY,wBAAuB,EACtC;YACC2G,MAAM,EAAE,MAAM;YACdC,IAAI,EAAEP;UACP,CACD,CAAC;UAED,IAAI,CAACI,cAAc,CAACI,EAAE,EAAE;YACvB,MAAM,IAAIC,KAAK,CAAC,eAAe,CAAC;UACjC;UAEA,MAAMC,YAAY,GAAG,MAAMN,cAAc,CAACO,IAAI,CAAC,CAAC;UAChDZ,QAAQ,GAAGW,YAAY,CAACE,UAAU;QACnC,CAAC,CAAC,OAAOC,WAAW,EAAE;UACrBnB,OAAO,CAACE,KAAK,CAAC,oCAAoC,EAAEiB,WAAW,CAAC;UAChE/D,SAAS,CAAC;YACT+C,OAAO,EAAE,8BAA8B;YACvCL,MAAM,EAAE;UACT,CAAC,CAAC;UACF;QACD;MACD;;MAEA;MACA,MAAMsB,QAAQ,GAAG;QAChBC,MAAM,EAAEzE,IAAI,CAAC6B,GAAG;QAChBb,WAAW,EAAEmC,IAAI,CAACnC,WAAW;QAC7BI,KAAK,EAAEqC;MACR,CAAC;MACDL,OAAO,CAACC,GAAG,CAACmB,QAAQ,CAAC;MAErBpB,OAAO,CAACC,GAAG,CAACI,QAAQ,CAAC;;MAErB;MACA,MAAMT,QAAQ,GAAG,MAAMrF,KAAK,CAACsF,IAAI,CAC/B,GAAE5F,WAAY,oBAAmB;MAClC;MACAmH,QACD,CAAC;MAED,IAAIxB,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;QAC5BjC,cAAc,CAAC,EAAE,CAAC;QAClBT,SAAS,CAAC;UACT0C,MAAM,EAAE,SAAS;UACjBK,OAAO,EAAE;QACV,CAAC,CAAC;QACF7B,MAAM,CAACgD,QAAQ,CAACC,MAAM,CAAC,CAAC;;QAExB;MACD,CAAC,MAAM;QACN,MAAMC,SAAS,GAAG5B,QAAQ,CAACG,IAAI;QAC/BC,OAAO,CAACE,KAAK,CAAC,wBAAwB,EAAEsB,SAAS,CAACrB,OAAO,CAAC;QAC1D/C,SAAS,CAAC;UAAE+C,OAAO,EAAEqB,SAAS,CAACrB,OAAO;UAAEL,MAAM,EAAE;QAAS,CAAC,CAAC;MAC5D;IACD,CAAC,CAAC,OAAOI,KAAK,EAAE;MACfF,OAAO,CAACE,KAAK,CAAC,yCAAyC,EAAEA,KAAK,CAACC,OAAO,CAAC;MACvE/C,SAAS,CAAC;QACT+C,OAAO,EAAE,qCAAqC;QAC9CL,MAAM,EAAE;MACT,CAAC,CAAC;IACH,CAAC,SAAS;MACTtC,UAAU,CAAC,KAAK,CAAC;MACjBS,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;MAChBJ,cAAc,CAAC,EAAE,CAAC;IACnB;EACD,CAAC;;EAED;;EAEA;EACA;EACA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;;EAEA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;;EAEA;EACA,MAAM4D,aAAa,GAAG;IACrBC,QAAQ,EAAE,MAAM;IAChBC,UAAU,EAAE,yCAAyC;IACrDC,oBAAoB,EAAE,MAAM;IAC5BC,mBAAmB,EAAE;EACtB,CAAC;EAED,MAAMC,WAAW,GAAGA,CAAA,KAAM;IACzB;IACApD,YAAY,CAACE,OAAO,CAAC,eAAe,EAAE,MAAM,CAAC;IAC7Cb,YAAY,CAAC,KAAK,CAAC;EACpB,CAAC;EAED,oBACCzB,OAAA,CAAAE,SAAA;IAAAuF,QAAA,gBACCzF,OAAA;MAAK0F,SAAS,EAAC,wFAAwF;MAAAD,QAAA,gBACtGzF,OAAA,CAACvB,MAAM;QAAAkH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEV9F,OAAA;QAAK0F,SAAS,EAAC,gBAAgB;QAAAD,QAAA,EAC7BjE,SAAS,IAAIM,QAAQ,iBACrB9B,OAAA;UAAK+F,GAAG,EAAEjG,OAAQ;UAACkG,KAAK,EAAE;YAAEC,YAAY,EAAE;UAAO,CAAE;UAACC,GAAG,EAAC;QAAE;UAAAP,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAC5D;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACG,CAAC,eAEN9F,OAAA;QAAK0F,SAAS,EAAC,8CAA8C;QAAAD,QAAA,gBAE5DzF,OAAA;UAAK0F,SAAS,EAAC,qEAAqE;UAAAD,QAAA,gBACnFzF,OAAA,CAACzB,WAAW;YAAC+B,IAAI,EAAEA;UAAK;YAAAqF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAC3B9F,OAAA,CAAC5B,WAAW;YAAC+H,OAAO,EAAE7F,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6F;UAAQ;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnC,CAAC,eAGN9F,OAAA;UAAK0F,SAAS,EAAC,mEAAmE;UAAAD,QAAA,gBACjFzF,OAAA;YACCoG,QAAQ,EAAEzD,YAAY,CAACmB,gBAAgB,CAAE;YACzC4B,SAAS,EAAC,4BAA4B;YAAAD,QAAA,gBAEtCzF,OAAA;cAAK0F,SAAS,EAAC,iEAAiE;cAAAD,QAAA,gBAC/EzF,OAAA;gBACC+F,GAAG,GAAA1F,gBAAA,GAAEC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE+F,UAAU,cAAAhG,gBAAA,cAAAA,gBAAA,GAAIvB,SAAU;gBACnCoH,GAAG,EAAC,YAAY;gBAChBR,SAAS,EAAC;cAAqC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC/C,CAAC,eACF9F,OAAA,CAACxB,SAAS;gBACT8H,MAAM,EAAC,0BAA0B;gBACjCC,WAAW,EAAC,yBAAyB;gBACrCpC,IAAI,EAAC,aAAa;gBAClBqC,QAAQ,EAAGzD,CAAC,IAAKxB,cAAc,CAACwB,CAAC,CAACC,MAAM,CAACyD,KAAK,CAAE;gBAChDA,KAAK,EAAEnF,WAAY;gBACnBoB,QAAQ,EAAEA,QAAQ,CAAC,aAAa,EAAE;kBACjCgE,QAAQ,EAAE;gBACX,CAAC,CAAE;gBACH9C,KAAK,EAAEf,MAAM,CAACvB,WAAW,GAAGuB,MAAM,CAACvB,WAAW,CAACuC,OAAO,GAAG;cAAG;gBAAA8B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC5D,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC,EACL,CAAAjF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgD,OAAO,kBACf7D,OAAA;cACC2G,IAAI,EAAC,OAAO;cACZjB,SAAS,EAAG,WACX,CAAA7E,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE2C,MAAM,MAAK,QAAQ,GACxB,kBAAkB,GAClB,kBACH,SAAS;cAAAiC,QAAA,EAET5E,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEgD;YAAO;cAAA8B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACX,CACN,eAED9F,OAAA;cAAK0F,SAAS,EAAC,wCAAwC;cAAAD,QAAA,gBACtDzF,OAAA;gBACC4G,OAAO,EAAC,WAAW;gBACnBlB,SAAS,EAAC,oFAAoF;gBAAAD,QAAA,gBAE9FzF,OAAA;kBACC6G,IAAI,EAAC,MAAM;kBACXL,QAAQ,EAAGzD,CAAC,IAAKD,gBAAgB,CAACC,CAAC,CAAE;kBACrC2C,SAAS,EAAC,QAAQ;kBAClBoB,EAAE,EAAC;gBAAW;kBAAAnB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACd,CAAC,eACF9F,OAAA,CAACf,QAAQ;kBAAA0G,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACZ9F,OAAA;kBAAAyF,QAAA,EAAM;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAER9F,OAAA;gBACC0F,SAAS,EAAC,oFAAoF;gBAC9FkB,OAAO,EAAC,aAAa;gBAAAnB,QAAA,gBAErBzF,OAAA;kBACC6G,IAAI,EAAC,MAAM;kBACX,iBAAc,MAAM;kBACpBL,QAAQ,EAAE1D,gBAAiB;kBAC3B4C,SAAS,EAAC,QAAQ;kBAClBoB,EAAE,EAAC,aAAa;kBAChBC,MAAM,EAAC;gBAAY;kBAAApB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACnB,CAAC,eACF9F,OAAA,CAACd,YAAY;kBAAAyG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eAChB9F,OAAA;kBAAAyF,QAAA,EAAM;gBAAK;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACZ,CAAC,eAER9F,OAAA;gBAAAyF,QAAA,EACExE,OAAO,gBACPjB,OAAA,CAAC3B,OAAO;kBAAAsH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,gBAEX9F,OAAA,CAAC9B,YAAY;kBACZ2I,IAAI,EAAC,QAAQ;kBACbG,KAAK,EAAC,MAAM;kBACZC,eAAe,EAAC;gBAAsE;kBAAAtB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACtF;cACD;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACD,CAAC,EAEN3E,OAAO,gBACPnB,OAAA,CAAC3B,OAAO;YAAAsH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,GACR,CAAA3C,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAE+D,MAAM,IAAG,CAAC,GAC3B/D,YAAY,CACVgE,KAAK,CAAC,CAAC,CACPC,OAAO,CAAC,CAAC,CACTC,GAAG,CAAE9D,IAAI,iBACTvD,OAAA,CAAC1B,QAAQ;YAERiF,IAAI,EAAEA,IAAK;YACXjD,IAAI,EAAEA,IAAK;YACXgH,UAAU,EAAEA,CAAA,KAAM,CAAC,CAAE;YACrBC,QAAQ,EAAEA,CAAA,KAAM,CAAC;UAAE,GAJdhE,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEpB,GAAG;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAKd,CACD,CAAC,gBAEH9F,OAAA;YAAK0F,SAAS,EAAC,gDAAgD;YAAAD,QAAA,eAC9DzF,OAAA;cAAG0F,SAAS,EAAC,uBAAuB;cAAAD,QAAA,EAAC;YAAiB;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACtD,CACL;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACG,CAAC,EAELhE,QAAQ,iBACR9B,OAAA;UACCgG,KAAK,EAAE;YAAEwB,MAAM,EAAE;UAAO,CAAE;UAC1B9B,SAAS,EAAC,wFAAwF;UAAAD,QAAA,gBAGlGzF,OAAA,CAACnB,IAAI;YACJ4I,EAAE,EAAC,GAAG;YACN/B,SAAS,EAAC,oCAAoC;YAC9CM,KAAK,EAAE;cAAEZ,QAAQ,EAAE,MAAM;cAAEsC,KAAK,EAAE;YAAO,CAAE;YAAAjC,QAAA,gBAE3CzF,OAAA,CAACP,uBAAuB;cACvBuG,KAAK,EAAE;gBAAEZ,QAAQ,EAAE,MAAM;gBAAEsC,KAAK,EAAE;cAAO;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,QAEH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEP9F,OAAA,CAACnB,IAAI;YACJ4I,EAAE,EAAC,KAAK;YACR/B,SAAS,EAAC,oCAAoC;YAC9CM,KAAK,EAAE;cAAEZ,QAAQ,EAAE,MAAM;cAAEsC,KAAK,EAAE;YAAU,CAAE;YAAAjC,QAAA,gBAE9CzF,OAAA,CAACH,eAAe;cACfmG,KAAK,EAAE;gBAAEZ,QAAQ,EAAE,MAAM;gBAAEsC,KAAK,EAAE;cAAU;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9C,CAAC,UAEH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEP9F,OAAA,CAACnB,IAAI;YACJ4I,EAAE,EAAG,YAAWvF,GAAI,EAAE;YACtBwD,SAAS,EAAC,oCAAoC;YAC9CM,KAAK,EAAE;cAAEZ,QAAQ,EAAE,MAAM;cAAEsC,KAAK,EAAE;YAAO,CAAE;YAAAjC,QAAA,gBAE3CzF,OAAA,CAACJ,iBAAiB;cACjBoG,KAAK,EAAE;gBAAEZ,QAAQ,EAAE,MAAM;gBAAEsC,KAAK,EAAE;cAAO;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,WAEH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAEP9F,OAAA,CAACnB,IAAI;YACJ4I,EAAE,EAAC,gBAAgB;YACnB/B,SAAS,EAAC,oCAAoC;YAC9CM,KAAK,EAAE;cAAEZ,QAAQ,EAAE,MAAM;cAAEsC,KAAK,EAAE;YAAO,CAAE;YAAAjC,QAAA,gBAE3CzF,OAAA,CAACN,YAAY;cAACsG,KAAK,EAAE;gBAAEZ,QAAQ,EAAE,MAAM;gBAAEsC,KAAK,EAAE;cAAO;YAAE;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,WAE7D;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CACL,eAED9F,OAAA;UAAK0F,SAAS,EAAC,4DAA4D;UAAAD,QAAA,gBAE1EzF,OAAA;YAAK0F,SAAS,EAAC,kDAAkD;YAAAD,QAAA,gBAChEzF,OAAA;cAAK0F,SAAS,EAAC,0FAA0F;cAAAD,QAAA,gBACxGzF,OAAA;gBAAAyF,QAAA,EAAM;cAAe;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC,eAC5B9F,OAAA;gBAAAyF,QAAA,EAAOhF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAEyG;cAAM;gBAAAvB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChC,CAAC,eAEN9F,OAAA;cAAK0F,SAAS,EAAC,iCAAiC;cAAAD,QAAA,EAC9ChF,aAAa,aAAbA,aAAa,uBAAbA,aAAa,CAAE4G,GAAG,CAAC,CAAC;gBAAElF,GAAG;gBAAEwF,WAAW,EAAEC;cAAK,CAAC;gBAAA,IAAAC,gBAAA,EAAAC,gBAAA;gBAAA,oBAC9C9H,OAAA;kBAAe0F,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,gBAC3DzF,OAAA,CAACnB,IAAI;oBACJ4I,EAAE,EAAE,WAAW,GAAGG,IAAI,CAACzF,GAAI;oBAC3BuD,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAEzDzF,OAAA;sBACC+F,GAAG,GAAA8B,gBAAA,GAAED,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEvB,UAAU,cAAAwB,gBAAA,cAAAA,gBAAA,GAAI/I,SAAU;sBACnCoH,GAAG,EAAE0B,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,SAAU;sBACrBrC,SAAS,EAAC;oBAAqC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACF9F,OAAA;sBAAK0F,SAAS,EAAC,QAAQ;sBAAAD,QAAA,gBACtBzF,OAAA;wBAAG0F,SAAS,EAAC,qCAAqC;wBAAAD,QAAA,GAChDmC,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEG,SAAS,EAAC,GAAC,EAACH,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEI,QAAQ;sBAAA;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC,eACJ9F,OAAA;wBAAM0F,SAAS,EAAC,uBAAuB;wBAAAD,QAAA,GAAAqC,gBAAA,GACrCF,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAEK,UAAU,cAAAH,gBAAA,cAAAA,gBAAA,GAAI;sBAAe;wBAAAnC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC/B,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACD,CAAC,eAEP9F,OAAA;oBAAK0F,SAAS,EAAC,YAAY;oBAAAD,QAAA,gBAC1BzF,OAAA,CAAC9B,YAAY;sBACZ8I,KAAK,EAAC,QAAQ;sBACdC,eAAe,EAAC;oBAA0D;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC1E,CAAC,eACF9F,OAAA,CAAC9B,YAAY;sBACZ8I,KAAK,EAAC,MAAM;sBACZC,eAAe,EAAC;oBAAqE;sBAAAtB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACrF,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACE,CAAC;gBAAA,GA7BG3D,GAAG;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8BR,CAAC;cAAA,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC,eAGN9F,OAAA;YAAK0F,SAAS,EAAC,kDAAkD;YAAAD,QAAA,gBAChEzF,OAAA;cAAK0F,SAAS,EAAC,qFAAqF;cAAAD,QAAA,eACnGzF,OAAA;gBAAAyF,QAAA,EAAM;cAAiB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACN9F,OAAA;cAAK0F,SAAS,EAAC,iCAAiC;cAAAD,QAAA,EAC9C9E,gBAAgB,aAAhBA,gBAAgB,uBAAhBA,gBAAgB,CAAE0G,GAAG,CAAEa,MAAM;gBAAA,IAAAC,kBAAA,EAAAC,kBAAA;gBAAA,oBAC7BpI,OAAA;kBACC0F,SAAS,EAAC,mCAAmC;kBAAAD,QAAA,gBAG7CzF,OAAA,CAACnB,IAAI;oBACJ4I,EAAE,EAAE,WAAW,IAAGS,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE/F,GAAG,CAAC;oBAE9BuD,SAAS,EAAC,+CAA+C;oBAAAD,QAAA,gBAEzDzF,OAAA;sBACC+F,GAAG,GAAAoC,kBAAA,GAAED,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE7B,UAAU,cAAA8B,kBAAA,cAAAA,kBAAA,GAAIrJ,SAAU;sBACrCoH,GAAG,EAAEgC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEH,SAAU;sBACvBrC,SAAS,EAAC;oBAAqC;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAC/C,CAAC,eACF9F,OAAA;sBAAK0F,SAAS,EAAC,SAAS;sBAAAD,QAAA,gBACvBzF,OAAA;wBAAG0F,SAAS,EAAC,qCAAqC;wBAAAD,QAAA,GAChDyC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEH,SAAS,EAAC,GAAC,EAACG,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEF,QAAQ;sBAAA;wBAAArC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACnC,CAAC,eACJ9F,OAAA;wBAAM0F,SAAS,EAAC,uBAAuB;wBAAAD,QAAA,GAAA2C,kBAAA,GACrCF,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAED,UAAU,cAAAG,kBAAA,cAAAA,kBAAA,GAAI;sBAAe;wBAAAzC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACjC,CAAC;oBAAA;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACH,CAAC;kBAAA,GAfDoC,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAE/F,GAAG;oBAAAwD,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAgBX,CAAC,eAEP9F,OAAA;oBAAK0F,SAAS,EAAC,YAAY;oBAAAD,QAAA,eAC1BzF,OAAA;sBACC0F,SAAS,EAAC,+CAA+C;sBACzD2C,OAAO,EAAEA,CAAA,KAAM,CAAC,CAAE;sBAAA5C,QAAA,eAElBzF,OAAA,CAAChB,eAAe;wBAACsJ,IAAI,EAAE,EAAG;wBAAC5C,SAAS,EAAC;sBAAgB;wBAAAC,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAE;oBAAC;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACjD;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACL,CAAC;gBAAA,GA7BDoC,MAAM,CAAC/F,GAAG;kBAAAwD,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OA8BX,CAAC;cAAA,CACN;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACF,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAELvF,IAAI,iBAAIP,OAAA,CAAC7B,WAAW;MAAAwH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC;EAAA,eACvB,CAAC;AAEL,CAAC;AAAC1F,EAAA,CAxhBID,IAAI;EAAA,QACcnC,WAAW,EAOjBD,WAAW,EAoCxBoB,OAAO;AAAA;AAAAoJ,EAAA,GA5CNpI,IAAI;AA0hBV,eAAeA,IAAI;AAAC,IAAAoI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}