{"ast": null, "code": "export { unstable_ClassNameGenerator } from '@mui/utils';", "map": {"version": 3, "names": ["unstable_ClassNameGenerator"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/ClassNameGenerator/index.js"], "sourcesContent": ["export { unstable_ClassNameGenerator } from '@mui/utils';"], "mappings": "AAAA,SAASA,2BAA2B,QAAQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}