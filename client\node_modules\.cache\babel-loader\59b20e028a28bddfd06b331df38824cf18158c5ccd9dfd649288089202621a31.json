{"ast": null, "code": "'use client';\n\nexport { useModal as unstable_useModal } from './useModal';\nexport * from './useModal.types';\nexport * from './ModalManager';", "map": {"version": 3, "names": ["useModal", "unstable_useModal"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/unstable_useModal/index.js"], "sourcesContent": ["'use client';\n\nexport { useModal as unstable_useModal } from './useModal';\nexport * from './useModal.types';\nexport * from './ModalManager';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,IAAIC,iBAAiB,QAAQ,YAAY;AAC1D,cAAc,kBAAkB;AAChC,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}