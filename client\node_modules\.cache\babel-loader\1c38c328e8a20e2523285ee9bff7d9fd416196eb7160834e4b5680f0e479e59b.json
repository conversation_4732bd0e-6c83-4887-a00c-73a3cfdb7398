{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - do not document.\n *\n * Use this function determine the host element correctly on the server (in a SSR context, e.g. Next.js)\n */\nexport function useRootElementName(parameters) {\n  const {\n    rootElementName: rootElementNameProp = '',\n    componentName\n  } = parameters;\n  const [rootElementName, setRootElementName] = React.useState(rootElementNameProp.toUpperCase());\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (rootElementNameProp && rootElementName !== rootElementNameProp.toUpperCase()) {\n        console.error(\"useRootElementName: the `rootElementName` prop of \".concat(componentName ? \"the \".concat(componentName, \" component\") : 'a component', \" expected the '\").concat(rootElementNameProp, \"' element, but a '\").concat(rootElementName.toLowerCase(), \"' was rendered instead\"), 'This may cause hydration issues in an SSR context, e.g. in a Next.js app');\n      }\n    }, [rootElementNameProp, rootElementName, componentName]);\n  }\n  const updateRootElementName = React.useCallback(instance => {\n    var _instance$tagName;\n    setRootElementName((_instance$tagName = instance == null ? void 0 : instance.tagName) != null ? _instance$tagName : '');\n  }, []);\n  return [rootElementName, updateRootElementName];\n}", "map": {"version": 3, "names": ["React", "useRootElementName", "parameters", "rootElementName", "rootElementNameProp", "componentName", "setRootElementName", "useState", "toUpperCase", "process", "env", "NODE_ENV", "useEffect", "console", "error", "concat", "toLowerCase", "updateRootElementName", "useCallback", "instance", "_instance$tagName", "tagName"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/utils/useRootElementName.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\n/**\n * @ignore - do not document.\n *\n * Use this function determine the host element correctly on the server (in a SSR context, e.g. Next.js)\n */\nexport function useRootElementName(parameters) {\n  const {\n    rootElementName: rootElementNameProp = '',\n    componentName\n  } = parameters;\n  const [rootElementName, setRootElementName] = React.useState(rootElementNameProp.toUpperCase());\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    React.useEffect(() => {\n      if (rootElementNameProp && rootElementName !== rootElementNameProp.toUpperCase()) {\n        console.error(`useRootElementName: the \\`rootElementName\\` prop of ${componentName ? `the ${componentName} component` : 'a component'} expected the '${rootElementNameProp}' element, but a '${rootElementName.toLowerCase()}' was rendered instead`, 'This may cause hydration issues in an SSR context, e.g. in a Next.js app');\n      }\n    }, [rootElementNameProp, rootElementName, componentName]);\n  }\n  const updateRootElementName = React.useCallback(instance => {\n    var _instance$tagName;\n    setRootElementName((_instance$tagName = instance == null ? void 0 : instance.tagName) != null ? _instance$tagName : '');\n  }, []);\n  return [rootElementName, updateRootElementName];\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA,OAAO,SAASC,kBAAkBA,CAACC,UAAU,EAAE;EAC7C,MAAM;IACJC,eAAe,EAAEC,mBAAmB,GAAG,EAAE;IACzCC;EACF,CAAC,GAAGH,UAAU;EACd,MAAM,CAACC,eAAe,EAAEG,kBAAkB,CAAC,GAAGN,KAAK,CAACO,QAAQ,CAACH,mBAAmB,CAACI,WAAW,CAAC,CAAC,CAAC;EAC/F,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACAX,KAAK,CAACY,SAAS,CAAC,MAAM;MACpB,IAAIR,mBAAmB,IAAID,eAAe,KAAKC,mBAAmB,CAACI,WAAW,CAAC,CAAC,EAAE;QAChFK,OAAO,CAACC,KAAK,sDAAAC,MAAA,CAAwDV,aAAa,UAAAU,MAAA,CAAUV,aAAa,kBAAe,aAAa,qBAAAU,MAAA,CAAkBX,mBAAmB,wBAAAW,MAAA,CAAqBZ,eAAe,CAACa,WAAW,CAAC,CAAC,6BAA0B,0EAA0E,CAAC;MACnU;IACF,CAAC,EAAE,CAACZ,mBAAmB,EAAED,eAAe,EAAEE,aAAa,CAAC,CAAC;EAC3D;EACA,MAAMY,qBAAqB,GAAGjB,KAAK,CAACkB,WAAW,CAACC,QAAQ,IAAI;IAC1D,IAAIC,iBAAiB;IACrBd,kBAAkB,CAAC,CAACc,iBAAiB,GAAGD,QAAQ,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACE,OAAO,KAAK,IAAI,GAAGD,iBAAiB,GAAG,EAAE,CAAC;EACzH,CAAC,EAAE,EAAE,CAAC;EACN,OAAO,CAACjB,eAAe,EAAEc,qBAAqB,CAAC;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}