{"ast": null, "code": "export { default } from './deepmerge';\nexport * from './deepmerge';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/deepmerge/index.js"], "sourcesContent": ["export { default } from './deepmerge';\nexport * from './deepmerge';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,aAAa;AACrC,cAAc,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}