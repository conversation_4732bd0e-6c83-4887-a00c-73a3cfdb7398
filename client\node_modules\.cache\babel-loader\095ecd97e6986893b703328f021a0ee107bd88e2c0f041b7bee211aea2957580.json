{"ast": null, "code": "export { default } from './useLazyRef';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/useLazyRef/index.js"], "sourcesContent": ["export { default } from './useLazyRef';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}