{"ast": null, "code": "export { default } from './HTMLElementType';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/utils/HTMLElementType/index.js"], "sourcesContent": ["export { default } from './HTMLElementType';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}