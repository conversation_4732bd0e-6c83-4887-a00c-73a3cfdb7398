{"ast": null, "code": "export { TextareaAutosize as default } from '@mui/base/TextareaAutosize';", "map": {"version": 3, "names": ["TextareaAutosize", "default"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/TextareaAutosize/index.js"], "sourcesContent": ["export { TextareaAutosize as default } from '@mui/base/TextareaAutosize';"], "mappings": "AAAA,SAASA,gBAAgB,IAAIC,OAAO,QAAQ,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}