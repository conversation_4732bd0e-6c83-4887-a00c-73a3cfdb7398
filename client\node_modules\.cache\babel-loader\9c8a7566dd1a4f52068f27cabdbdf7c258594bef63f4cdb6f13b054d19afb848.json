{"ast": null, "code": "'use client';\n\nexport { Snackbar } from './Snackbar';\nexport * from './Snackbar.types';\nexport * from './snackbarClasses';", "map": {"version": 3, "names": ["Snackbar"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/Snackbar/index.js"], "sourcesContent": ["'use client';\n\nexport { Snackbar } from './Snackbar';\nexport * from './Snackbar.types';\nexport * from './snackbarClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,QAAQ,QAAQ,YAAY;AACrC,cAAc,kBAAkB;AAChC,cAAc,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}