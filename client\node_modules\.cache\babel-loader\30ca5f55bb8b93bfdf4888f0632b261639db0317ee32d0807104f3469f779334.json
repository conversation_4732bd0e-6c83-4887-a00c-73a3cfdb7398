{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport { createGrid as createGrid2 } from '@mui/system/Unstable_Grid';\nimport { styled, useThemeProps } from '../styles';\nconst Grid2 = createGrid2({\n  createStyledComponent: styled('div', {\n    name: 'MuiGrid2',\n    slot: 'Root',\n    overridesResolver: (props, styles) => styles.root\n  }),\n  componentName: 'MuiGrid2',\n  useThemeProps: inProps => useThemeProps({\n    props: inProps,\n    name: 'MuiGrid2'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Grid2.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Grid2;", "map": {"version": 3, "names": ["PropTypes", "createGrid", "createGrid2", "styled", "useThemeProps", "Grid2", "createStyledComponent", "name", "slot", "overridesResolver", "props", "styles", "root", "componentName", "inProps", "process", "env", "NODE_ENV", "propTypes", "children", "node", "sx", "oneOfType", "arrayOf", "func", "object", "bool"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/Unstable_Grid2/Grid2.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport { createGrid as createGrid2 } from '@mui/system/Unstable_Grid';\nimport { styled, useThemeProps } from '../styles';\nconst Grid2 = createGrid2({\n  createStyledComponent: styled('div', {\n    name: 'MuiGrid2',\n    slot: 'Root',\n    overridesResolver: (props, styles) => styles.root\n  }),\n  componentName: 'MuiGrid2',\n  useThemeProps: inProps => useThemeProps({\n    props: inProps,\n    name: 'MuiGrid2'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Grid2.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │ To update them, edit the TypeScript types and run `pnpm proptypes`. │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * @ignore\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Grid2;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,YAAY;AAClC,SAASC,UAAU,IAAIC,WAAW,QAAQ,2BAA2B;AACrE,SAASC,MAAM,EAAEC,aAAa,QAAQ,WAAW;AACjD,MAAMC,KAAK,GAAGH,WAAW,CAAC;EACxBI,qBAAqB,EAAEH,MAAM,CAAC,KAAK,EAAE;IACnCI,IAAI,EAAE,UAAU;IAChBC,IAAI,EAAE,MAAM;IACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAKA,MAAM,CAACC;EAC/C,CAAC,CAAC;EACFC,aAAa,EAAE,UAAU;EACzBT,aAAa,EAAEU,OAAO,IAAIV,aAAa,CAAC;IACtCM,KAAK,EAAEI,OAAO;IACdP,IAAI,EAAE;EACR,CAAC;AACH,CAAC,CAAC;AACFQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGZ,KAAK,CAACa,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAEnB,SAAS,CAACoB,IAAI;EACxB;AACF;AACA;EACEC,EAAE,EAAErB,SAAS,CAACsB,SAAS,CAAC,CAACtB,SAAS,CAACuB,OAAO,CAACvB,SAAS,CAACsB,SAAS,CAAC,CAACtB,SAAS,CAACwB,IAAI,EAAExB,SAAS,CAACyB,MAAM,EAAEzB,SAAS,CAAC0B,IAAI,CAAC,CAAC,CAAC,EAAE1B,SAAS,CAACwB,IAAI,EAAExB,SAAS,CAACyB,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAepB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}