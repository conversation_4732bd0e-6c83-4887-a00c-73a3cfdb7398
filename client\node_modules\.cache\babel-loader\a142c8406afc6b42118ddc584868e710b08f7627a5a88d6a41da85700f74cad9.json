{"ast": null, "code": "'use client';\n\nimport useControlled from '@mui/utils/useControlled';\nexport default useControlled;", "map": {"version": 3, "names": ["useControlled"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/utils/useControlled.js"], "sourcesContent": ["'use client';\n\nimport useControlled from '@mui/utils/useControlled';\nexport default useControlled;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,aAAa,MAAM,0BAA0B;AACpD,eAAeA,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}