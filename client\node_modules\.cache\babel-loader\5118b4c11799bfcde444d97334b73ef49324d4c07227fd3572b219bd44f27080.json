{"ast": null, "code": "'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nvar _react = require('react');\nvar React = _interopRequireWildcard(_react);\nfunction _interopRequireWildcard(obj) {\n  if (obj && obj.__esModule) {\n    return obj;\n  } else {\n    var newObj = {};\n    if (obj != null) {\n      for (var key in obj) {\n        if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key];\n      }\n    }\n    newObj.default = obj;\n    return newObj;\n  }\n}\nexports.default = function (decoratedHref, decoratedText, key) {\n  return React.createElement('a', {\n    href: decoratedHref,\n    key: key\n  }, decoratedText);\n};", "map": {"version": 3, "names": ["Object", "defineProperty", "exports", "value", "_react", "require", "React", "_interopRequireWildcard", "obj", "__esModule", "newObj", "key", "prototype", "hasOwnProperty", "call", "default", "decorated<PERSON><PERSON>f", "decoratedText", "createElement", "href"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/react-linkify/dist/decorators/defaultComponentDecorator.js"], "sourcesContent": ["'use strict';\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n\nvar _react = require('react');\n\nvar React = _interopRequireWildcard(_react);\n\nfunction _interopRequireWildcard(obj) { if (obj && obj.__esModule) { return obj; } else { var newObj = {}; if (obj != null) { for (var key in obj) { if (Object.prototype.hasOwnProperty.call(obj, key)) newObj[key] = obj[key]; } } newObj.default = obj; return newObj; } }\n\nexports.default = function (decoratedHref, decoratedText, key) {\n  return React.createElement(\n    'a',\n    { href: decoratedHref, key: key },\n    decoratedText\n  );\n};"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,cAAc,CAACC,OAAO,EAAE,YAAY,EAAE;EAC3CC,KAAK,EAAE;AACT,CAAC,CAAC;AAEF,IAAIC,MAAM,GAAGC,OAAO,CAAC,OAAO,CAAC;AAE7B,IAAIC,KAAK,GAAGC,uBAAuB,CAACH,MAAM,CAAC;AAE3C,SAASG,uBAAuBA,CAACC,GAAG,EAAE;EAAE,IAAIA,GAAG,IAAIA,GAAG,CAACC,UAAU,EAAE;IAAE,OAAOD,GAAG;EAAE,CAAC,MAAM;IAAE,IAAIE,MAAM,GAAG,CAAC,CAAC;IAAE,IAAIF,GAAG,IAAI,IAAI,EAAE;MAAE,KAAK,IAAIG,GAAG,IAAIH,GAAG,EAAE;QAAE,IAAIR,MAAM,CAACY,SAAS,CAACC,cAAc,CAACC,IAAI,CAACN,GAAG,EAAEG,GAAG,CAAC,EAAED,MAAM,CAACC,GAAG,CAAC,GAAGH,GAAG,CAACG,GAAG,CAAC;MAAE;IAAE;IAAED,MAAM,CAACK,OAAO,GAAGP,GAAG;IAAE,OAAOE,MAAM;EAAE;AAAE;AAE5QR,OAAO,CAACa,OAAO,GAAG,UAAUC,aAAa,EAAEC,aAAa,EAAEN,GAAG,EAAE;EAC7D,OAAOL,KAAK,CAACY,aAAa,CACxB,GAAG,EACH;IAAEC,IAAI,EAAEH,aAAa;IAAEL,GAAG,EAAEA;EAAI,CAAC,EACjCM,aACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}