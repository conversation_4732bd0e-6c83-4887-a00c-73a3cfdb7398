{"ast": null, "code": "// ImageKit configuration for client-side\nexport const imagekitConfig={publicKey:process.env.REACT_APP_IMAGEKIT_PUBLIC_KEY||'public_XA3Jkld7ls8x1CsE5y+gjWj5I4k=',urlEndpoint:process.env.REACT_APP_IMAGEKIT_URL_ENDPOINT||'https://ik.imagekit.io/georgebobby/cluster-files/',authenticationEndpoint:process.env.REACT_APP_BACKEND_URL?\"\".concat(process.env.REACT_APP_BACKEND_URL,\"/imagekit/auth\"):'http://localhost:8800/imagekit/auth'};// Utility function to get authentication parameters from server\nexport const getAuthenticationParameters=async()=>{try{const response=await fetch(imagekitConfig.authenticationEndpoint);if(!response.ok){throw new Error('Failed to get authentication parameters');}return await response.json();}catch(error){console.error('Error getting authentication parameters:',error);throw error;}};", "map": {"version": 3, "names": ["imagekitConfig", "public<PERSON>ey", "process", "env", "REACT_APP_IMAGEKIT_PUBLIC_KEY", "urlEndpoint", "REACT_APP_IMAGEKIT_URL_ENDPOINT", "authenticationEndpoint", "REACT_APP_BACKEND_URL", "concat", "getAuthenticationParameters", "response", "fetch", "ok", "Error", "json", "error", "console"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/src/config/imagekit.js"], "sourcesContent": ["// ImageKit configuration for client-side\nexport const imagekitConfig = {\n  publicKey: process.env.REACT_APP_IMAGEKIT_PUBLIC_KEY || 'public_XA3Jkld7ls8x1CsE5y+gjWj5I4k=',\n  urlEndpoint: process.env.REACT_APP_IMAGEKIT_URL_ENDPOINT || 'https://ik.imagekit.io/georgebobby/cluster-files/',\n  authenticationEndpoint: process.env.REACT_APP_BACKEND_URL ? \n    `${process.env.REACT_APP_BACKEND_URL}/imagekit/auth` : \n    'http://localhost:8800/imagekit/auth'\n};\n\n// Utility function to get authentication parameters from server\nexport const getAuthenticationParameters = async () => {\n  try {\n    const response = await fetch(imagekitConfig.authenticationEndpoint);\n    if (!response.ok) {\n      throw new Error('Failed to get authentication parameters');\n    }\n    return await response.json();\n  } catch (error) {\n    console.error('Error getting authentication parameters:', error);\n    throw error;\n  }\n};\n"], "mappings": "AAAA;AACA,MAAO,MAAM,CAAAA,cAAc,CAAG,CAC5BC,SAAS,CAAEC,OAAO,CAACC,GAAG,CAACC,6BAA6B,EAAI,qCAAqC,CAC7FC,WAAW,CAAEH,OAAO,CAACC,GAAG,CAACG,+BAA+B,EAAI,mDAAmD,CAC/GC,sBAAsB,CAAEL,OAAO,CAACC,GAAG,CAACK,qBAAqB,IAAAC,MAAA,CACpDP,OAAO,CAACC,GAAG,CAACK,qBAAqB,mBACpC,qCACJ,CAAC,CAED;AACA,MAAO,MAAM,CAAAE,2BAA2B,CAAG,KAAAA,CAAA,GAAY,CACrD,GAAI,CACF,KAAM,CAAAC,QAAQ,CAAG,KAAM,CAAAC,KAAK,CAACZ,cAAc,CAACO,sBAAsB,CAAC,CACnE,GAAI,CAACI,QAAQ,CAACE,EAAE,CAAE,CAChB,KAAM,IAAI,CAAAC,KAAK,CAAC,yCAAyC,CAAC,CAC5D,CACA,MAAO,MAAM,CAAAH,QAAQ,CAACI,IAAI,CAAC,CAAC,CAC9B,CAAE,MAAOC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0CAA0C,CAAEA,KAAK,CAAC,CAChE,KAAM,CAAAA,KAAK,CACb,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}