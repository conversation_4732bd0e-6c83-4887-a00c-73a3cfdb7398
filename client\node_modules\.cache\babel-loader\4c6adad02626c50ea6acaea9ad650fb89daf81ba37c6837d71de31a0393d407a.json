{"ast": null, "code": "'use client';\n\nexport { default } from './InputBase';\nexport { default as inputBaseClasses } from './inputBaseClasses';\nexport * from './inputBaseClasses';", "map": {"version": 3, "names": ["default", "inputBaseClasses"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/InputBase/index.js"], "sourcesContent": ["'use client';\n\nexport { default } from './InputBase';\nexport { default as inputBaseClasses } from './inputBaseClasses';\nexport * from './inputBaseClasses';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,OAAO,QAAQ,aAAa;AACrC,SAASA,OAAO,IAAIC,gBAAgB,QAAQ,oBAAoB;AAChE,cAAc,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}