{"ast": null, "code": "'use client';\n\nexport { Popup as Unstable_Popup } from './Popup';\nexport * from './Popup.types';\nexport * from './popupClasses';\nexport * from './PopupContext';", "map": {"version": 3, "names": ["Popup", "Unstable_Popup"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/base/Unstable_Popup/index.js"], "sourcesContent": ["'use client';\n\nexport { Popup as Unstable_Popup } from './Popup';\nexport * from './Popup.types';\nexport * from './popupClasses';\nexport * from './PopupContext';"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,KAAK,IAAIC,cAAc,QAAQ,SAAS;AACjD,cAAc,eAAe;AAC7B,cAAc,gBAAgB;AAC9B,cAAc,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}