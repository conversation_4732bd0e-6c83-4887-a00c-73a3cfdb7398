{"ast": null, "code": "'use client';\n\nimport PropTypes from 'prop-types';\nimport { createContainer } from '@mui/system';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nconst Container = createContainer({\n  createStyledComponent: styled('div', {\n    name: '<PERSON>i<PERSON>ontaine<PERSON>',\n    slot: 'Root',\n    overridesResolver: (props, styles) => {\n      const {\n        ownerState\n      } = props;\n      return [styles.root, styles[\"maxWidth\".concat(capitalize(String(ownerState.maxWidth)))], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n    }\n  }),\n  useThemeProps: inProps => useThemeProps({\n    props: inProps,\n    name: '<PERSON>i<PERSON>ontainer'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * Set the max-width to match the min-width of the current breakpoint.\n   * This is useful if you'd prefer to design for a fixed set of sizes\n   * instead of trying to accommodate a fully fluid viewport.\n   * It's fluid by default.\n   * @default false\n   */\n  fixed: PropTypes.bool,\n  /**\n   * Determine the max-width of the container.\n   * The container width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'lg'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Container;", "map": {"version": 3, "names": ["PropTypes", "createContainer", "capitalize", "styled", "useThemeProps", "Container", "createStyledComponent", "name", "slot", "overridesResolver", "props", "styles", "ownerState", "root", "concat", "String", "max<PERSON><PERSON><PERSON>", "fixed", "disableGutters", "inProps", "process", "env", "NODE_ENV", "propTypes", "children", "node", "classes", "object", "component", "elementType", "bool", "oneOfType", "oneOf", "string", "sx", "arrayOf", "func"], "sources": ["C:/Users/<USER>/OneDrive/Documents/GitHub/cluster-delta/client/node_modules/@mui/material/Container/Container.js"], "sourcesContent": ["'use client';\n\nimport PropTypes from 'prop-types';\nimport { createContainer } from '@mui/system';\nimport capitalize from '../utils/capitalize';\nimport styled from '../styles/styled';\nimport useThemeProps from '../styles/useThemeProps';\nconst Container = createContainer({\n  createStyledComponent: styled('div', {\n    name: '<PERSON>i<PERSON>ontainer',\n    slot: 'Root',\n    overridesResolver: (props, styles) => {\n      const {\n        ownerState\n      } = props;\n      return [styles.root, styles[`maxWidth${capitalize(String(ownerState.maxWidth))}`], ownerState.fixed && styles.fixed, ownerState.disableGutters && styles.disableGutters];\n    }\n  }),\n  useThemeProps: inProps => useThemeProps({\n    props: inProps,\n    name: 'MuiContainer'\n  })\n});\nprocess.env.NODE_ENV !== \"production\" ? Container.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * @ignore\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * If `true`, the left and right padding is removed.\n   * @default false\n   */\n  disableGutters: PropTypes.bool,\n  /**\n   * Set the max-width to match the min-width of the current breakpoint.\n   * This is useful if you'd prefer to design for a fixed set of sizes\n   * instead of trying to accommodate a fully fluid viewport.\n   * It's fluid by default.\n   * @default false\n   */\n  fixed: PropTypes.bool,\n  /**\n   * Determine the max-width of the container.\n   * The container width grows with the size of the screen.\n   * Set to `false` to disable `maxWidth`.\n   * @default 'lg'\n   */\n  maxWidth: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['xs', 'sm', 'md', 'lg', 'xl', false]), PropTypes.string]),\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object])\n} : void 0;\nexport default Container;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,SAAS,MAAM,YAAY;AAClC,SAASC,eAAe,QAAQ,aAAa;AAC7C,OAAOC,UAAU,MAAM,qBAAqB;AAC5C,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,aAAa,MAAM,yBAAyB;AACnD,MAAMC,SAAS,GAAGJ,eAAe,CAAC;EAChCK,qBAAqB,EAAEH,MAAM,CAAC,KAAK,EAAE;IACnCI,IAAI,EAAE,cAAc;IACpBC,IAAI,EAAE,MAAM;IACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;MACpC,MAAM;QACJC;MACF,CAAC,GAAGF,KAAK;MACT,OAAO,CAACC,MAAM,CAACE,IAAI,EAAEF,MAAM,YAAAG,MAAA,CAAYZ,UAAU,CAACa,MAAM,CAACH,UAAU,CAACI,QAAQ,CAAC,CAAC,EAAG,EAAEJ,UAAU,CAACK,KAAK,IAAIN,MAAM,CAACM,KAAK,EAAEL,UAAU,CAACM,cAAc,IAAIP,MAAM,CAACO,cAAc,CAAC;IAC1K;EACF,CAAC,CAAC;EACFd,aAAa,EAAEe,OAAO,IAAIf,aAAa,CAAC;IACtCM,KAAK,EAAES,OAAO;IACdZ,IAAI,EAAE;EACR,CAAC;AACH,CAAC,CAAC;AACFa,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGjB,SAAS,CAACkB,SAAS,CAAC,yBAAyB;EACnF;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAExB,SAAS,CAACyB,IAAI;EACxB;AACF;AACA;EACEC,OAAO,EAAE1B,SAAS,CAAC2B,MAAM;EACzB;AACF;AACA;AACA;EACEC,SAAS,EAAE5B,SAAS,CAAC6B,WAAW;EAChC;AACF;AACA;AACA;EACEX,cAAc,EAAElB,SAAS,CAAC8B,IAAI;EAC9B;AACF;AACA;AACA;AACA;AACA;AACA;EACEb,KAAK,EAAEjB,SAAS,CAAC8B,IAAI;EACrB;AACF;AACA;AACA;AACA;AACA;EACEd,QAAQ,EAAEhB,SAAS,CAAC,sCAAsC+B,SAAS,CAAC,CAAC/B,SAAS,CAACgC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,CAAC,EAAEhC,SAAS,CAACiC,MAAM,CAAC,CAAC;EAC/I;AACF;AACA;EACEC,EAAE,EAAElC,SAAS,CAAC+B,SAAS,CAAC,CAAC/B,SAAS,CAACmC,OAAO,CAACnC,SAAS,CAAC+B,SAAS,CAAC,CAAC/B,SAAS,CAACoC,IAAI,EAAEpC,SAAS,CAAC2B,MAAM,EAAE3B,SAAS,CAAC8B,IAAI,CAAC,CAAC,CAAC,EAAE9B,SAAS,CAACoC,IAAI,EAAEpC,SAAS,CAAC2B,MAAM,CAAC;AACxJ,CAAC,GAAG,KAAK,CAAC;AACV,eAAetB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}